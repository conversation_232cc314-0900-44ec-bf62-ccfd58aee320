import { EventEmitter } from "events";

export interface Job {
	id: string;
	type: string;
	priority: number;
	createdAt: Date;
	scheduledAt?: Date;
	retryCount?: number;
	maxRetries?: number;
	data?: any;
	context?: any;
}

export interface JobHandler<T extends Job = Job> {
	(job: T): Promise<void>;
}

/**
 * Simple in-memory job queue with priority support
 * For production, consider using Redis or a dedicated queue service
 */
export class JobQueue extends EventEmitter {
	private queue: Job[] = [];
	private processing: boolean = false;
	private handlers: Map<string, JobHandler> = new Map();
	private concurrency: number = 1;
	private activeJobs: number = 0;

	constructor(concurrency: number = 1) {
		super();
		this.concurrency = concurrency;
	}

	/**
	 * Add a job to the queue
	 */
	async enqueue(job: Job): Promise<void> {
		// Set defaults
		job.retryCount = job.retryCount || 0;
		job.maxRetries = job.maxRetries || 3;
		job.scheduledAt = job.scheduledAt || new Date();

		// Insert job in priority order (higher priority first)
		const insertIndex = this.queue.findIndex((queuedJob) => queuedJob.priority < job.priority);
		if (insertIndex === -1) {
			this.queue.push(job);
		} else {
			this.queue.splice(insertIndex, 0, job);
		}

		console.log(`Job ${job.id} enqueued with priority ${job.priority}`);

		// Start processing if not already running
		this.processQueue();
	}

	/**
	 * Register a job handler
	 */
	onJob(handler: JobHandler): void;
	onJob(type: string, handler: JobHandler): void;
	onJob(typeOrHandler: string | JobHandler, handler?: JobHandler): void {
		if (typeof typeOrHandler === "function") {
			// Generic handler for all job types
			this.on("job", typeOrHandler);
		} else if (typeof typeOrHandler === "string" && handler) {
			// Specific handler for a job type
			this.handlers.set(typeOrHandler, handler);
		}
	}

	/**
	 * Process the queue
	 */
	private async processQueue(): Promise<void> {
		if (this.processing || this.activeJobs >= this.concurrency) {
			return;
		}

		this.processing = true;

		while (this.queue.length > 0 && this.activeJobs < this.concurrency) {
			const job = this.queue.shift();
			if (!job) continue;

			// Check if job is scheduled for future execution
			if (job.scheduledAt && job.scheduledAt > new Date()) {
				// Re-queue for later
				this.queue.unshift(job);
				break;
			}

			this.activeJobs++;
			this.processJob(job).finally(() => {
				this.activeJobs--;
				// Continue processing if there are more jobs
				if (this.queue.length > 0) {
					setImmediate(() => this.processQueue());
				}
			});
		}

		this.processing = false;
	}

	/**
	 * Process a single job
	 */
	private async processJob(job: Job): Promise<void> {
		console.log(`Processing job ${job.id} of type ${job.type}`);

		try {
			// Try specific handler first
			const specificHandler = this.handlers.get(job.type);
			if (specificHandler) {
				await specificHandler(job);
			} else {
				// Emit generic job event
				this.emit("job", job);
			}

			console.log(`Job ${job.id} completed successfully`);
		} catch (error) {
			console.error(`Job ${job.id} failed:`, error);

			// Retry logic
			if (job.retryCount! < job.maxRetries!) {
				job.retryCount!++;
				job.scheduledAt = new Date(Date.now() + this.getRetryDelay(job.retryCount!));

				console.log(`Retrying job ${job.id} (attempt ${job.retryCount}/${job.maxRetries})`);
				await this.enqueue(job);
			} else {
				console.error(`Job ${job.id} failed after ${job.maxRetries} attempts`);
			}
		}
	}

	private getRetryDelay(attempt: number): number {
		// Implement your retry delay logic here
		return 1000; // Default: 1 second
	}
}
