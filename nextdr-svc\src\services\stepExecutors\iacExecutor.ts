import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

/**
 * Executor for Infrastructure as Code (IaC) operations
 */
export class IaCExecutor extends BaseStepExecutor {
	constructor() {
		super("IaC");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Starting IaC execution for step: ${step.name}`);

		try {
			this.validate(step);
			const config = this.parseConfiguration(step);

			// Simulate IaC execution
			this.log("Executing Infrastructure as Code deployment...");
			await this.sleep(2000); // Simulate processing time

			return this.createSuccessResult("IaC deployment completed successfully", {
				deploymentId: `iac-${Date.now()}`,
				resources_created: config.resources || [],
				completed_at: new Date().toISOString(),
			});
		} catch (error: any) {
			this.log(`IaC execution failed: ${error.message}`, "error");
			return this.createErrorResult(`IaC execution failed: ${error.message}`, error.message);
		}
	}

	validate(step: any): boolean {
		super.validate(step);

		if (step.operation_type !== "IaC") {
			throw new Error(`Invalid operation type for IaC: ${step.operation_type}`);
		}

		return true;
	}
}
