import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	<PERSON><PERSON>Title,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useModalStore } from "@/lib/store/useStore";
import { RecoveryStep } from "@/lib/types";
import { toast } from "@/components/ui/sonner";
import { useAddRecoveryStep, useUpdateRecoveryStep } from "@/lib/api/hooks/recoveryPlans";
import { useUserProfiles } from "@/hooks/useUserProfiles";
import VMRestoreEditForm from "@/components/recovery/VMRestoreEditForm";
import IaCEditForm from "@/components/recovery/IaCEditForm";
import { Switch } from "@/components/ui/switch";

const operationTypes = [
	"Restore virtual machine",
	"IaC",
	"Database restore",
	"Virus check",
	"Apply OS updates",
	"Manual step",
	"Approval",
	"Verification",
	"Script",
	"Notification",
] as const;

// Define a schema that conditionally requires approver_id for Approval/Verification steps
const formSchema = z
	.object({
		name: z.string().min(3, "Name must be at least 3 characters"),
		operation_type: z.enum(operationTypes),
		approver_id: z.string().optional(),
	})
	.refine(
		(data) => {
			// If operation type is Approval or Verification, approver_id is required
			if (data.operation_type === "Approval" || data.operation_type === "Verification") {
				return !!data.approver_id;
			}
			// For other operation types, approver_id is optional
			return true;
		},
		{
			message: "Approver is required for Approval and Verification steps",
			path: ["approver_id"], // This specifies which field the error belongs to
		}
	);

type FormValues = z.infer<typeof formSchema>;

const RecoveryStepModal = () => {
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "recoveryStep";
	const { profiles: users } = useUserProfiles();

	// Check if we're in edit mode
	const isEditMode = !!modalData?.step;
	const stepToEdit = modalData?.step as RecoveryStep | undefined;

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting, isValid },
		reset,
		setValue,
		watch,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: stepToEdit?.name || "",
			operation_type: stepToEdit?.operation_type || "Manual step",
			approver_id: stepToEdit?.approval_metadata?.approver_id || undefined,
		},
		mode: "onChange", // Validate on change to provide immediate feedback
	});

	const selectedOperationType = watch("operation_type");
	const isApprovalStep =
		selectedOperationType === "Approval" || selectedOperationType === "Verification";

	useEffect(() => {
		if (showModal && stepToEdit) {
			reset({
				name: stepToEdit.name,
				operation_type: stepToEdit.operation_type,
				approver_id: stepToEdit.approval_metadata?.approver_id || undefined,
			});

			// Explicitly set values for Select components
			setValue("operation_type", stepToEdit.operation_type);
			if (stepToEdit.approval_metadata?.approver_id) {
				setValue("approver_id", stepToEdit.approval_metadata.approver_id);
			}
		} else if (showModal) {
			// Reset form with empty values for new step
			reset({
				name: "",
				operation_type: "Manual step",
				approver_id: undefined,
			});
		}
	}, [showModal, stepToEdit, reset, setValue]);

	const addRecoveryStep = useAddRecoveryStep();
	const updateRecoveryStep = useUpdateRecoveryStep();

	const onSubmit = async (data: FormValues) => {
		try {
			// Double-check that approver is specified for Approval/Verification steps
			if (
				(data.operation_type === "Approval" || data.operation_type === "Verification") &&
				!data.approver_id
			) {
				toast.error("An approver must be selected for Approval and Verification steps");
				return;
			}

			const stepData: Omit<RecoveryStep, "id" | "created_at"> = {
				name: data.name,
				operation_type: data.operation_type,
				recovery_plan_id: modalData?.planId,
				step_order: stepToEdit?.step_order || 0,
				configuration: stepToEdit?.configuration || {},
			};

			// Add approval metadata for Approval/Verification steps
			if (data.operation_type === "Approval" || data.operation_type === "Verification") {
				// We can safely use data.approver_id here because we've validated it above
				stepData.approval_metadata = {
					approver_id: data.approver_id!,
					approval_status: "pending",
				};
			}

			if (isEditMode && stepToEdit) {
				await updateRecoveryStep.mutateAsync({
					...stepToEdit,
					...stepData,
				});
				toast.success("Recovery step updated successfully");
			} else {
				await addRecoveryStep.mutateAsync(stepData);
				toast.success("Recovery step added successfully");
			}

			onClose();
		} catch (error) {
			console.error("Error saving recovery step:", error);
			toast.error("Failed to save recovery step");
		}
	};

	if (selectedOperationType === "IaC") {
		return (
			<Dialog open={showModal} onOpenChange={onClose}>
				<DialogContent className="sm:max-w-[800px]">
					<DialogHeader>
						<DialogTitle>{isEditMode ? "Edit IaC Step" : "Add IaC Step"}</DialogTitle>
						<DialogDescription>
							Configure the Infrastructure as Code recovery step.
						</DialogDescription>
					</DialogHeader>
					<IaCEditForm
						step={
							stepToEdit || ({ name: "", operation_type: "IaC", configuration: {} } as RecoveryStep)
						}
						onSave={onClose}
						onCancel={onClose}
						apiConfig={modalData?.apiConfig}
					/>
				</DialogContent>
			</Dialog>
		);
	}

	if (selectedOperationType === "Restore virtual machine") {
		return (
			<Dialog open={showModal} onOpenChange={onClose}>
				<DialogContent className="sm:max-w-[800px]">
					<DialogHeader>
						<DialogTitle>{isEditMode ? "Edit VM Restore Step" : "Add VM Restore Step"}</DialogTitle>
						<DialogDescription>Configure the virtual machine restore step.</DialogDescription>
					</DialogHeader>
					<VMRestoreEditForm
						step={
							stepToEdit ||
							({
								name: "",
								operation_type: "Restore virtual machine",
								configuration: {},
							} as RecoveryStep)
						}
						onSave={onClose}
						onCancel={onClose}
						config={stepToEdit?.configuration || {}}
					/>
				</DialogContent>
			</Dialog>
		);
	}

	return (
		<Dialog open={showModal} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{isEditMode ? "Edit Recovery Step" : "Add Recovery Step"}</DialogTitle>
					<DialogDescription>Configure the recovery step details and assignee.</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="name" className="text-sm font-medium">
							Step Name
						</label>
						<Input id="name" {...register("name")} placeholder="Enter step name" />
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label htmlFor="operation_type" className="text-sm font-medium">
							Operation Type
						</label>
						<Select
							onValueChange={(value: (typeof operationTypes)[number]) =>
								setValue("operation_type", value)
							}
							value={watch("operation_type")}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select operation type" />
							</SelectTrigger>
							<SelectContent>
								{operationTypes.map((type) => (
									<SelectItem key={type} value={type}>
										{type}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{(selectedOperationType === "Approval" || selectedOperationType === "Verification") && (
						<div className="space-y-2">
							<div className="flex items-center">
								<label htmlFor="approver_id" className="text-sm font-medium">
									Select Approver
								</label>
								<span className="text-red-500 ml-1">*</span>
								<span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">
									Required
								</span>
							</div>
							<Select
								onValueChange={(value) => setValue("approver_id", value)}
								value={watch("approver_id")}
							>
								<SelectTrigger className={errors.approver_id ? "border-red-500" : ""}>
									<SelectValue placeholder="Select approver" />
								</SelectTrigger>
								<SelectContent>
									{users?.map((user) => (
										<SelectItem key={user.id} value={user.id}>
											{user.email} {user.role && `(${user.role})`}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							{errors.approver_id && (
								<p className="text-sm text-red-500">
									{errors.approver_id.message || "Approver is required for this step type"}
								</p>
							)}
							<p className="text-xs text-muted-foreground mt-2">
								This user will be responsible for approving this step before the recovery plan can
								continue. The selected user will receive an email notification when approval is
								required.
							</p>
						</div>
					)}

					<DialogFooter>
						<Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={
								isSubmitting ||
								!isValid ||
								// Additional check for approval/verification steps
								((selectedOperationType === "Approval" ||
									selectedOperationType === "Verification") &&
									!watch("approver_id"))
							}
						>
							{isSubmitting ? "Saving..." : isEditMode ? "Update" : "Add"} Step
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default RecoveryStepModal;
