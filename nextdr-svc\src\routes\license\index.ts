import { Router } from "express";
import generateRouter from "./generate";
import activateRouter from "./activate";
import verifyRouter from "./verify";
import statusRouter from "./status";

const router = Router();

// Mount license routes
router.use("/license", generateRouter);
router.use("/license", activateRouter);
router.use("/license", verifyRouter);
router.use("/license", statusRouter);

export default router;
