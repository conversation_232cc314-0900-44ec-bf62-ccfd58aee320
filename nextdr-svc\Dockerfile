# syntax=docker/dockerfile:1

FROM node:lts-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application files
COPY . .

# Expose the port the app runs on
EXPOSE 8081

# Command to run the application
CMD ["npm", "run dev"]

# To build the Docker image, run the following command in the terminal:
# cd /[path-to-your-project]/nextdr-svc
# docker build -t nextdr-svc .

# to run the Docker container, use the following command:
# docker run -d -p 3000:3000 nextdr-svc

# After a few seconds, open your web browser to http://localhost:3000. You should see your app.