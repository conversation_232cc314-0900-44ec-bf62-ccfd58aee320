-- Create recovery_plan_executions table for tracking individual execution instances
CREATE TABLE IF NOT EXISTS recovery_plan_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recovery_plan_id UUID NOT NULL REFERENCES recovery_plans_new(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed', 'cancelled')),
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    datacenter_id TEXT NOT NULL,
    created_by UUID REFERENCES user_profiles(id),
    execution_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_recovery_plan_executions_plan ON recovery_plan_executions(recovery_plan_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_executions_status ON recovery_plan_executions(status);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_executions_datacenter ON recovery_plan_executions(datacenter_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_executions_created_by ON recovery_plan_executions(created_by);

-- Enable RLS
ALTER TABLE recovery_plan_executions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view executions for plans they have access to"
    ON recovery_plan_executions FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM recovery_plans_new rp
            WHERE rp.id = recovery_plan_id
            AND (
                rp.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM user_profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'operator')
                )
            )
        )
    );

CREATE POLICY "Users can create executions for plans they have access to"
    ON recovery_plan_executions FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM recovery_plans_new rp
            WHERE rp.id = recovery_plan_id
            AND (
                rp.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM user_profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'operator')
                )
            )
        )
    );

CREATE POLICY "Users can update executions for plans they have access to"
    ON recovery_plan_executions FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM recovery_plans_new rp
            WHERE rp.id = recovery_plan_id
            AND (
                rp.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM user_profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'operator')
                )
            )
        )
    );

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_recovery_plan_executions_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    
    IF NEW.status IN ('completed', 'failed', 'cancelled') AND OLD.status = 'in_progress' THEN
        NEW.completed_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for timestamps
CREATE TRIGGER update_recovery_plan_executions_timestamps
BEFORE UPDATE ON recovery_plan_executions
FOR EACH ROW
EXECUTE FUNCTION update_recovery_plan_executions_timestamps();

-- Add execution_id column to recovery_plan_progress if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'recovery_plan_progress' 
        AND column_name = 'execution_id'
    ) THEN
        ALTER TABLE recovery_plan_progress 
        ADD COLUMN execution_id UUID REFERENCES recovery_plan_executions(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_recovery_plan_progress_execution ON recovery_plan_progress(execution_id);
    END IF;
END $$;
