/**
 * Format a date for display
 * 
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

/**
 * Check if a license is valid based on its expiration date
 * 
 * @param expiresAt ISO date string
 * @returns True if valid, false otherwise
 */
export const isLicenseValid = (expiresAt: string | null): boolean => {
  if (!expiresAt) return false;
  
  try {
    const expirationDate = new Date(expiresAt);
    const now = new Date();
    return expirationDate > now;
  } catch (error) {
    return false;
  }
};

/**
 * Get days remaining until license expiration
 * 
 * @param expiresAt ISO date string
 * @returns Number of days remaining
 */
export const getDaysRemaining = (expiresAt: string | null): number => {
  if (!expiresAt) return 0;
  
  try {
    const expirationDate = new Date(expiresAt);
    const now = new Date();
    const diffTime = expirationDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  } catch (error) {
    return 0;
  }
};

/**
 * Check if a license is about to expire (within 30 days)
 * 
 * @param expiresAt ISO date string
 * @returns True if about to expire, false otherwise
 */
export const isLicenseAboutToExpire = (expiresAt: string | null): boolean => {
  const daysRemaining = getDaysRemaining(expiresAt);
  return daysRemaining > 0 && daysRemaining <= 30;
};

/**
 * Check if a license has a specific feature
 * 
 * @param features Array of feature codes
 * @param featureCode Feature code to check
 * @returns True if the license has the feature, false otherwise
 */
export const hasFeature = (features: string[], featureCode: string): boolean => {
  return features.includes(featureCode);
};
