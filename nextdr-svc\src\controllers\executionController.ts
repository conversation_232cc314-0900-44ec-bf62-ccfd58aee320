import { Request, Response } from "express";
import { executionEngine } from "../services/executionEngine";
import { supabase } from "../services/supabaseService";

/**
 * New execution controller using the modular execution engine
 */

/**
 * Start execution of a recovery plan
 */
export const startRecoveryPlanExecution = async (req: Request, res: Response) => {
    console.log("Starting recovery plan execution");
    
    try {
        const { planId, datacenterId } = req.params;
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "Authentication required" });
            return;
        }

        if (!planId || !datacenterId) {
            res.status(400).json({ error: "Plan ID and datacenter ID are required" });
            return;
        }

        // Validate plan exists
        const { data: plan, error: planError } = await supabase
            .from("recovery_plans_new")
            .select("*")
            .eq("id", planId)
            .single();

        if (planError || !plan) {
            res.status(404).json({ error: "Recovery plan not found" });
            return;
        }

        // Start execution
        const executionId = await executionEngine.startExecution(planId, datacenterId, userId);

        res.status(200).json({
            message: "Recovery plan execution started successfully",
            planId,
            executionId,
            datacenterId
        });

    } catch (error: any) {
        console.error("Error starting recovery plan execution:", error);
        res.status(500).json({ 
            error: "Failed to start recovery plan execution",
            details: error.message 
        });
    }
};

/**
 * Resume execution from a checkpoint
 */
export const resumeRecoveryPlanExecution = async (req: Request, res: Response) => {
    console.log("Resuming recovery plan execution");
    
    try {
        const { planId } = req.params;
        const { checkpointId, datacenterId } = req.body;

        if (!planId || !checkpointId) {
            res.status(400).json({ error: "Plan ID and checkpoint ID are required" });
            return;
        }

        // Resume execution
        await executionEngine.resumeExecution(planId, checkpointId, datacenterId);

        res.status(200).json({
            message: "Recovery plan execution resumed successfully",
            planId,
            checkpointId
        });

    } catch (error: any) {
        console.error("Error resuming recovery plan execution:", error);
        res.status(500).json({ 
            error: "Failed to resume recovery plan execution",
            details: error.message 
        });
    }
};

/**
 * Get execution status
 */
export const getExecutionStatus = async (req: Request, res: Response) => {
    try {
        const { planId } = req.params;

        if (!planId) {
            res.status(400).json({ error: "Plan ID is required" });
            return;
        }

        const status = await executionEngine.getExecutionStatus(planId);

        res.status(200).json(status);

    } catch (error: any) {
        console.error("Error getting execution status:", error);
        res.status(500).json({ 
            error: "Failed to get execution status",
            details: error.message 
        });
    }
};

/**
 * Get execution progress with step details
 */
export const getExecutionProgress = async (req: Request, res: Response) => {
    try {
        const { planId } = req.params;

        if (!planId) {
            res.status(400).json({ error: "Plan ID is required" });
            return;
        }

        // Get plan details
        const { data: plan, error: planError } = await supabase
            .from("recovery_plans_new")
            .select("*")
            .eq("id", planId)
            .single();

        if (planError || !plan) {
            res.status(404).json({ error: "Recovery plan not found" });
            return;
        }

        // Get steps with progress
        const { data: steps, error: stepsError } = await supabase
            .from("recovery_steps_new")
            .select(`
                *,
                progress:recovery_plan_progress(*)
            `)
            .eq("recovery_plan_id", planId)
            .order("step_order", { ascending: true });

        if (stepsError) {
            throw new Error(`Failed to fetch steps: ${stepsError.message}`);
        }

        // Get checkpoints
        const { data: checkpoints, error: checkpointsError } = await supabase
            .from("recovery_plan_checkpoints")
            .select("*")
            .eq("recovery_plan_id", planId);

        if (checkpointsError) {
            console.warn("Failed to fetch checkpoints:", checkpointsError);
        }

        // Calculate overall progress
        const totalSteps = steps?.length || 0;
        const completedSteps = steps?.filter(step => 
            step.progress?.[0]?.status === 'completed' || 
            step.progress?.[0]?.status === 'approved'
        ).length || 0;
        
        const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

        res.status(200).json({
            planId,
            executionId: plan.current_execution_id,
            status: plan.execution_status,
            totalSteps,
            completedSteps,
            progressPercentage,
            steps: steps || [],
            checkpoints: checkpoints || [],
            metadata: plan.execution_metadata
        });

    } catch (error: any) {
        console.error("Error getting execution progress:", error);
        res.status(500).json({ 
            error: "Failed to get execution progress",
            details: error.message 
        });
    }
};

/**
 * Cancel execution
 */
export const cancelExecution = async (req: Request, res: Response) => {
    try {
        const { planId } = req.params;
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "Authentication required" });
            return;
        }

        if (!planId) {
            res.status(400).json({ error: "Plan ID is required" });
            return;
        }

        // Update plan status to cancelled
        const { error } = await supabase
            .from("recovery_plans_new")
            .update({
                execution_status: "failed",
                execution_completed_at: new Date().toISOString(),
                execution_metadata: {
                    cancelled_by: userId,
                    cancelled_at: new Date().toISOString(),
                    reason: "User cancelled"
                }
            })
            .eq("id", planId);

        if (error) {
            throw new Error(`Failed to cancel execution: ${error.message}`);
        }

        res.status(200).json({
            message: "Execution cancelled successfully",
            planId
        });

    } catch (error: any) {
        console.error("Error cancelling execution:", error);
        res.status(500).json({ 
            error: "Failed to cancel execution",
            details: error.message 
        });
    }
};
