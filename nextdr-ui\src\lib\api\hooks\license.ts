import { useMutation } from "@tanstack/react-query";
import { toast } from "@/components/ui/sonner";
import * as apiClient from "../api-client";

export const useActivateLicense = () => {
  return useMutation({
    mutationFn: async (licenseKey: string) => {
      try {
        const data = await apiClient.activateLicense(licenseKey);
        return data;
      } catch (error: any) {
        throw new Error(error.message || "Failed to activate license");
      }
    },
    onSuccess: () => {
      toast.success("License activated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to activate license: ${error.message}`);
    },
  });
};
