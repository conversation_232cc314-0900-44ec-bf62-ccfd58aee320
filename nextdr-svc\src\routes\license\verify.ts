import { Router, Request, Response } from "express";
import { verifyLicenseToken, getLicenseInfo } from "../../utils/licenseUtils";

const router = Router();

/**
 * Verify a license token
 *
 * @route POST /api/license/verify
 * @param {string} token - License token to verify
 * @returns {Object} License information if valid
 */
const verifyLicense = async (req: Request, res: Response): Promise<any> => {
	try {
		const { token } = req.body;

		// Validate required fields
		if (!token) {
			return res.status(400).json({ error: "Missing required field: token" });
		}

		// Get license info
		const licenseInfo = await getLicenseInfo(token);

		if (!licenseInfo) {
			return res.status(400).json({ error: "Invalid license" });
		}

		// Return license information
		return res.status(200).json({
			valid: true,
			customerId: licenseInfo.customerId,
			expiresAt: licenseInfo.expiresAt,
		});
	} catch (error) {
		console.error("License verification error:", error);
		return res.status(500).json({ error: "Failed to verify license" });
	}
};

router.post("/verify", verifyLicense);

export default router;
