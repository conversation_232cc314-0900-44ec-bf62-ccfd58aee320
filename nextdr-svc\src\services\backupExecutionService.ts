import { BackupExecution } from "../models/gcp_backup";
import { supabase } from "../db/supabaseClient";
import { toSnakeCase, toCamelCase } from "../utils/stringUtils";

//save the backup execution to the database
export const saveBackupExecution = async (backupExecution: BackupExecution) => {
  const snakeCaseExecution = toSnakeCase(backupExecution);
  console.log('Saving backup execution:', JSON.stringify(snakeCaseExecution, null, 2));
  const { data, error } = await supabase
    .from('backup_execution')
    .insert(snakeCaseExecution)
    .select();

  if (error) {
    console.error('Error saving backup execution:', JSON.stringify(error, null, 2));
    throw error;
  }
  return data;
};

//get all backup executions
export const getBackupExecutions = async () => {
  const { data, error } = await supabase
    .from('backup_execution')
    .select('*');

  if (error) {
    console.error('Error getting backup executions:', error);
    throw error;
  }
  return data;
};

//get backup execution by id
export const getBackupExecutionById = async (id: string) => {
  const { data, error } = await supabase
    .from('backup_execution')
    .select('*')
    .eq('execution_id', id);  

  if (error) {
    console.error('Error getting backup execution by id:', error);
    throw error;
  }
  const camelCaseData = toCamelCase(data);
  return camelCaseData;
};

//update backup execution
export const updateBackupExecution = async (id: string, backupExecution: BackupExecution) => {
  const { data, error } = await supabase
    .from('backup_execution')
    .update(backupExecution)
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating backup execution:', error);
    throw error;
  }
  return data;
};