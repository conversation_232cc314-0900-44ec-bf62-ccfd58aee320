import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
	Loader2,
	Calendar,
	Clock,
	RefreshCw,
	HardDrive,
	RotateCw,
	Server,
	Edit,
	Plus,
} from "lucide-react";
import { ApplicationGroup, SupabaseSnapshot } from "@/lib/types";
import { toast } from "@/components/ui/sonner";
import { useGroupSnapshots } from "@/lib/api/hooks/snapshots";
import { useModalStore } from "@/lib/store/useStore";

interface SnapshotListProps {
	selectedGroup: ApplicationGroup;
}

const SnapshotList: React.FC<SnapshotListProps> = ({ selectedGroup }) => {
	const { data: snapshots = [], isLoading, refetch } = useGroupSnapshots(selectedGroup?.id || "");
	const { onOpen } = useModalStore();

	const handleRestore = (snapshot: SupabaseSnapshot) => {
		toast.success(`Restore from snapshot ${snapshot.id} initiated`);
	};

	const handleEdit = (snapshot: SupabaseSnapshot) => {
		onOpen("editSnapshot", { snapshot });
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString() + " " + date.toLocaleTimeString();
	};

	return (
		<div className="mt-6">
			<div className="flex justify-between items-center">
				<CardTitle className="text-lg flex items-center gap-2">
					<Calendar className="h-5 w-5 text-blue-500" />
					Snapshots
				</CardTitle>
				<div className="flex gap-2">
					<Button variant="outline" size="sm" onClick={() => onOpen("snapshotSchedule")}>
						<Plus className="h-4 w-4 mr-2" />
						Create
					</Button>
					<Button variant="outline" size="sm" onClick={() => refetch()}>
						<RefreshCw className="h-4 w-4 mr-2" />
						Refresh
					</Button>
				</div>
			</div>
			{isLoading ? (
				<div className="flex justify-center items-center py-8">
					<Loader2 className="h-6 w-6 animate-spin mr-2" />
					<span>Loading snapshots...</span>
				</div>
			) : snapshots.length > 0 ? (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{snapshots.map((snapshot) => (
						<div
							key={snapshot.id}
							className="border rounded-md p-2 flex flex-col justify-between h-full"
						>
							<div>
								<div className="flex items-center justify-between">
									<h4 className="font-medium">{`${
										snapshot.frequency.charAt(0).toUpperCase() + snapshot.frequency.slice(1)
									} Snapshot`}</h4>
									<span className="px-2 py-1 text-xs bg-green-900/20 text-green-500 rounded-full">
										{snapshot.status}
									</span>
									<div className="flex gap-2">
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleEdit(snapshot)}
											className="px-2"
										>
											<Edit className="h-3.5 w-3.5" />
										</Button>
									</div>
								</div>
								<div className="text-sm text-muted-foreground">
									<div className="flex items-center gap-1 mt-2">
										<Calendar className="h-3.5 w-3.5" />
										<span>Created: {formatDate(snapshot.created_at)}</span>
									</div>
									<div className="flex items-center gap-1 mt-1">
										<Clock className="h-3.5 w-3.5" />
										<span>Next run: {formatDate(snapshot.next_run)}</span>
									</div>
									{/* <div className="flex items-center gap-1 mt-1">
										<HardDrive className="h-3.5 w-3.5" />
										<span>
											{snapshot.vm_ids.length} VM{snapshot.vm_ids.length !== 1 ? "s" : ""}
										</span>
									</div> */}
								</div>
							</div>
						</div>
					))}
				</div>
			) : (
				<div className="text-center py-8 text-muted-foreground">
					<p>No snapshots found for this application group</p>
					<p className="text-sm mt-2">
						Configure a snapshot schedule to automatically create snapshots
					</p>
				</div>
			)}
		</div>
	);
};

export default SnapshotList;
