import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";
import { useUpdateRecoveryStep } from "@/lib/api/hooks/recoveryPlans";

interface GenericStepProps {
	step: RecoveryStep;
	isEditing: boolean;
	onSave: () => void;
	onCancel: () => void;
}

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const GenericStep: React.FC<GenericStepProps> = ({ step, isEditing, onSave, onCancel }) => {
	// Initialize expanded state based on isEditing or if there's a description
	const [isExpanded, setIsExpanded] = useState(isEditing || !!step.configuration?.description);
	const updateStep = useUpdateRecoveryStep();

	// Log for debugging
	useEffect(() => {
		console.log("GenericStep rendered:", {
			stepId: step.id,
			isEditing,
			isExpanded,
			configuration: step.configuration,
		});
	}, [step, isEditing, isExpanded]);

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: step.name || "",
			description: step.configuration?.description || "",
		},
	});

	// Toggle expanded state
	const toggleExpanded = () => {
		if (isEditing) return;
		setIsExpanded(!isExpanded);
	};

	const onSubmit = async (data: FormValues) => {
		try {
			const updatedStep: Partial<RecoveryStep> = {
				...step,
				name: data.name,
				configuration: {
					...step.configuration,
					description: data.description,
				},
			};

			console.log("Updating generic step:", updatedStep);
			updateStep.mutate(updatedStep as any);
			setIsExpanded(false);
			onSave();
		} catch (error) {
			console.error("Error updating generic step:", error);
			toast.error("Failed to update step");
		}
	};

	// Get operation type display name
	const getOperationTypeDisplay = (type: string) => {
		return type
			.split("_")
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(" ");
	};

	return (
		<div className="border rounded-md p-4 mb-4 bg-gray-800">
			<div className="flex justify-between items-center cursor-pointer" onClick={toggleExpanded}>
				<div>
					<h3 className="text-lg font-medium">{step.name}</h3>
					<p className="text-sm text-muted-foreground">
						{getOperationTypeDisplay(step.operation_type)}
					</p>
				</div>
				<Button
					variant="ghost"
					size="sm"
					onClick={(e) => {
						e.stopPropagation();
						toggleExpanded();
					}}
				>
					{isExpanded ? "Collapse" : "Expand"}
				</Button>
			</div>

			{isExpanded && (
				<form onSubmit={handleSubmit(onSubmit)} className="mt-4 space-y-4">
					<div className="space-y-2">
						<label htmlFor="name" className="text-sm font-medium">
							Step Name
						</label>
						<Input id="name" placeholder="Enter step name" {...register("name")} />
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label htmlFor="description" className="text-sm font-medium">
							Description
						</label>
						<Textarea
							id="description"
							placeholder="Enter step description"
							{...register("description")}
						/>
					</div>

					<div className="flex justify-end space-x-2">
						<Button variant="outline" type="button" onClick={onCancel}>
							Cancel
						</Button>
						<Button
							type="submit"
							className="bg-dr-purple hover:bg-dr-purple-dark"
							disabled={isSubmitting}
						>
							{isSubmitting ? "Saving..." : "Save"}
						</Button>
					</div>
				</form>
			)}
		</div>
	);
};

export default GenericStep;
