-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id UUID,
    details JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ip_address TEXT,
    user_agent TEXT,
    retention_period INTERVAL DEFAULT '1 year',
    is_immutable BOOLEAN DEFAULT true
);

-- Create RLS policies
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Allow admins and operators to read all audit logs
CREATE POLICY "Admins and operators can view all audit logs"
    ON audit_logs FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role IN ('admin', 'operator')
        )
    );

-- <PERSON><PERSON> function to log user management actions
CREATE OR REPLACE FUNCTION public.log_user_action(
    action TEXT,
    entity_id UUID,
    details JSONB DEFAULT '{}'::jsonb,
    ip_address TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO audit_logs (
        user_id,
        action,
        entity_type,
        entity_id,
        details,
        ip_address,
        user_agent
    )
    VALUES (
        auth.uid(),
        action,
        'user',
        entity_id,
        details,
        ip_address,
        user_agent
    )
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to log user profile updates
CREATE OR REPLACE FUNCTION public.log_user_profile_update()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.role != NEW.role THEN
        PERFORM public.log_user_action(
            'role_update',
            NEW.id,
            jsonb_build_object(
                'old_role', OLD.role,
                'new_role', NEW.role,
                'reason', COALESCE(NEW.metadata->>'reason', 'No reason provided')
            )
        );
    END IF;

    IF OLD.status != NEW.status THEN
        PERFORM public.log_user_action(
            'status_update',
            NEW.id,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'reason', COALESCE(NEW.metadata->>'reason', 'No reason provided')
            )
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_user_profile_update ON user_profiles;
CREATE TRIGGER on_user_profile_update
    AFTER UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.log_user_profile_update();

-- Create trigger to log user profile deletion
CREATE OR REPLACE FUNCTION public.log_user_profile_deletion()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM public.log_user_action(
        'user_deleted',
        OLD.id,
        jsonb_build_object(
            'email', OLD.email,
            'role', OLD.role,
            'status', OLD.status,
            'reason', COALESCE(OLD.metadata->>'reason', 'No reason provided')
        )
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_user_profile_deletion ON user_profiles;
CREATE TRIGGER on_user_profile_deletion
    BEFORE DELETE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.log_user_profile_deletion();

-- Create function to export audit logs
CREATE OR REPLACE FUNCTION export_audit_logs(
    start_date TIMESTAMPTZ DEFAULT NULL,
    end_date TIMESTAMPTZ DEFAULT NULL,
    action_filter TEXT DEFAULT NULL,
    entity_type_filter TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    timestamp TIMESTAMPTZ,
    actor_email TEXT,
    action TEXT,
    entity_type TEXT,
    entity_id UUID,
    details JSONB,
    ip_address TEXT,
    user_agent TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        al.id,
        al.created_at,
        up.email as actor_email,
        al.action,
        al.entity_type,
        al.entity_id,
        al.details,
        al.ip_address,
        al.user_agent
    FROM audit_logs al
    LEFT JOIN user_profiles up ON al.user_id = up.id
    WHERE 
        (start_date IS NULL OR al.created_at >= start_date) AND
        (end_date IS NULL OR al.created_at <= end_date) AND
        (action_filter IS NULL OR al.action = action_filter) AND
        (entity_type_filter IS NULL OR al.entity_type = entity_type_filter)
    ORDER BY al.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 