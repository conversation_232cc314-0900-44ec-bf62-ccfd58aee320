import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, AlertCircle, Loader2, ArrowRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { getApprovalDetails, processApproval } from "@/lib/api/api-client";

interface StepDetails {
	id: string;
	name: string;
	operation_type: string;
	description?: string;
	recovery_plan_id: string;
	step_order: number;
}

interface CheckpointDetails {
	id: string;
	recovery_plan_id: string;
	step_id: string;
	approver_id?: string;
	approver_role?: string;
	approval_required: boolean;
	approval_status?: string;
}

interface PlanDetails {
	id: string;
	name: string;
	description?: string;
}

interface TokenDetails {
	id: string;
	token: string;
	expires_at: string;
	is_used: boolean;
	is_checkpoint?: boolean;
	checkpoint_id?: string;
}

interface ApprovalDetails {
	type: "checkpoint" | "step";
	step: StepDetails;
	plan: PlanDetails;
	checkpoint?: CheckpointDetails;
	token: TokenDetails;
}

const ApprovePage = () => {
	const { token } = useParams();
	const navigate = useNavigate();
	const [approvalDetails, setApprovalDetails] = useState<ApprovalDetails | null>(null);
	const [loading, setLoading] = useState(true);
	const [submitting, setSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [comment, setComment] = useState("");
	const [decision, setDecision] = useState<"approved" | "rejected" | null>(null);

	useEffect(() => {
		const fetchApprovalDetails = async () => {
			try {
				if (!token) {
					setError("Invalid approval token");
					setLoading(false);
					return;
				}

				// Add retry logic for connection issues
				let retryCount = 0;
				const maxRetries = 3;

				while (retryCount <= maxRetries) {
					try {
						console.log(`Fetching approval details for token: ${token}`);
						const response = await getApprovalDetails(token);
						console.log("Approval details:", response);
						setApprovalDetails(response);
						break; // Success, exit the retry loop
					} catch (retryError: any) {
						// Check if this is a connection error
						if (retryError.message?.includes("fetch failed") && retryCount < maxRetries) {
							retryCount++;
							console.log(`Connection error, retrying (${retryCount}/${maxRetries})...`);

							// Wait before retrying (exponential backoff)
							await new Promise((resolve) =>
								setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1))
							);
						} else {
							// Not a connection error or max retries reached, rethrow
							throw retryError;
						}
					}
				}
			} catch (err: any) {
				console.error("Error fetching approval details:", err);

				// More detailed error message
				let errorMessage = err.message || "Failed to load approval details";

				// Provide more user-friendly messages for common errors
				if (errorMessage.includes("fetch failed")) {
					errorMessage =
						"Connection error: Unable to connect to the server. Please check your internet connection and try again.";
				} else if (errorMessage.includes("Invalid approval token")) {
					errorMessage = "Invalid approval token: The token may be expired or invalid.";
				}

				setError(errorMessage);
			} finally {
				setLoading(false);
			}
		};

		fetchApprovalDetails();
	}, [token]);

	const handleApproval = async (approved: boolean) => {
		try {
			setSubmitting(true);
			console.log(`Processing ${approved ? "approval" : "rejection"} for token: ${token}`);

			// Add retry logic for connection issues
			let retryCount = 0;
			const maxRetries = 3;
			let result;

			while (retryCount <= maxRetries) {
				try {
					// Process the approval
					result = await processApproval(
						token as string,
						approved ? "approved" : "rejected",
						comment
					);
					console.log("Approval result:", result);
					break; // Success, exit the retry loop
				} catch (retryError: any) {
					// Check if this is a connection error
					if (retryError.message?.includes("fetch failed") && retryCount < maxRetries) {
						retryCount++;
						console.log(`Connection error, retrying (${retryCount}/${maxRetries})...`);

						// Show a toast for the retry
						toast.info(`Connection issue, retrying (${retryCount}/${maxRetries})...`, {
							duration: 3000,
						});

						// Wait before retrying (exponential backoff)
						await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1)));
					} else {
						// Not a connection error or max retries reached, rethrow
						throw retryError;
					}
				}
			}

			// If we got here with a result, the approval was successful
			if (result) {
				setDecision(approved ? "approved" : "rejected");

				if (approved) {
					toast.success("Approved successfully. The recovery plan will continue execution.");
				} else {
					toast.success("Rejected successfully. The recovery plan will remain paused.");
				}
			}
		} catch (err: any) {
			console.error("Error processing approval:", err);

			// More detailed error message
			let errorMessage = err.message || "Failed to process approval";
			let description = "Please try again or contact your administrator.";

			// Provide more user-friendly messages for common errors
			if (errorMessage.includes("fetch failed")) {
				errorMessage = "Connection error";
				description =
					"Unable to connect to the server. Please check your internet connection and try again.";
			} else if (errorMessage.includes("Invalid approval token")) {
				description = "The approval token may be invalid or expired.";
			}

			setError(errorMessage);

			// Show a more helpful error toast
			toast.error(`Failed to process approval: ${errorMessage}`, {
				description,
			});
		} finally {
			setSubmitting(false);
		}
	};

	const handleGoToDashboard = () => {
		navigate("/");
	};

	if (loading) {
		return (
			<div className="flex flex-col items-center justify-center h-screen">
				<Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
				<p className="text-muted-foreground">Loading approval details...</p>
			</div>
		);
	}

	if (error) {
		return (
			<div className="max-w-md mx-auto mt-10 p-4">
				<Alert variant="destructive">
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>Error</AlertTitle>
					<AlertDescription>{error}</AlertDescription>
				</Alert>
				<Button className="w-full mt-4" onClick={handleGoToDashboard}>
					Go to Dashboard
				</Button>
			</div>
		);
	}

	if (decision) {
		return (
			<div className="max-w-md mx-auto mt-10 p-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							{decision === "approved" ? (
								<>
									<CheckCircle className="h-5 w-5 text-green-500 mr-2" /> Approved
								</>
							) : (
								<>
									<XCircle className="h-5 w-5 text-red-500 mr-2" /> Rejected
								</>
							)}
						</CardTitle>
						<CardDescription>
							Your decision has been recorded. Thank you for your response.
						</CardDescription>
					</CardHeader>
					<CardFooter>
						<Button className="w-full" onClick={handleGoToDashboard}>
							Go to Dashboard
						</Button>
					</CardFooter>
				</Card>
			</div>
		);
	}

	if (!approvalDetails) {
		return (
			<div className="max-w-md mx-auto mt-10 p-4">
				<Alert>
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>No approval details found</AlertTitle>
					<AlertDescription>The approval token may be invalid or expired.</AlertDescription>
				</Alert>
				<Button className="w-full mt-4" onClick={handleGoToDashboard}>
					Go to Dashboard
				</Button>
			</div>
		);
	}

	const { step, plan, token: tokenDetails } = approvalDetails;
	const isCheckpoint = approvalDetails.type === "checkpoint";

	return (
		<div className="max-w-md mx-auto mt-10 p-4">
			<Card>
				<CardHeader>
					<div className="flex justify-between items-center">
						<CardTitle>{isCheckpoint ? "Checkpoint Approval" : "Recovery Step Approval"}</CardTitle>
						<Badge variant={isCheckpoint ? "default" : "outline"}>
							{isCheckpoint ? "Checkpoint" : "Step"}
						</Badge>
					</div>
					<CardDescription>
						You are requested to review and approve or reject this{" "}
						{isCheckpoint ? "checkpoint" : "recovery step"}.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<h3 className="text-sm font-medium">Recovery Plan</h3>
						<p className="mt-1 text-sm">{plan.name}</p>
						{plan.description && (
							<p className="mt-1 text-xs text-muted-foreground">{plan.description}</p>
						)}
					</div>

					<Separator />

					<div>
						<h3 className="text-sm font-medium">Step Details</h3>
						<div className="mt-2 space-y-2 text-sm">
							<div className="flex justify-between">
								<span className="font-medium">Name:</span>
								<span>{step.name}</span>
							</div>
							<div className="flex justify-between">
								<span className="font-medium">Type:</span>
								<span>{step.operation_type}</span>
							</div>
							{step.description && (
								<div className="flex justify-between">
									<span className="font-medium">Description:</span>
									<span className="text-right">{step.description}</span>
								</div>
							)}
							<div className="flex justify-between">
								<span className="font-medium">Step Order:</span>
								<span>{step.step_order}</span>
							</div>
						</div>
					</div>

					{isCheckpoint && (
						<>
							<Separator />
							<div>
								<h3 className="text-sm font-medium">Checkpoint Information</h3>
								<p className="mt-1 text-xs text-muted-foreground">
									Approving this checkpoint will allow the recovery plan to continue execution until
									the next checkpoint or completion.
								</p>
								<div className="mt-2 p-2 bg-muted rounded-md text-xs">
									<p>
										<ArrowRight className="inline h-3 w-3 mr-1" />
										Steps after this checkpoint will execute automatically
									</p>
								</div>
							</div>
						</>
					)}

					<div>
						<h3 className="text-sm font-medium">Your Comments</h3>
						<Textarea
							placeholder="Add any comments about your decision (optional)"
							className="mt-2"
							value={comment}
							onChange={(e) => setComment(e.target.value)}
						/>
					</div>
				</CardContent>
				<CardFooter className="flex justify-between">
					<Button
						variant="outline"
						onClick={() => handleApproval(false)}
						disabled={submitting}
						className="flex-1 mr-2"
					>
						{submitting ? (
							<Loader2 className="h-4 w-4 animate-spin mr-2" />
						) : (
							<XCircle className="h-4 w-4 mr-2" />
						)}
						Reject
					</Button>
					<Button
						onClick={() => handleApproval(true)}
						disabled={submitting}
						className="flex-1 ml-2"
					>
						{submitting ? (
							<Loader2 className="h-4 w-4 animate-spin mr-2" />
						) : (
							<CheckCircle className="h-4 w-4 mr-2" />
						)}
						Approve
					</Button>
				</CardFooter>
			</Card>
		</div>
	);
};

export default ApprovePage;
