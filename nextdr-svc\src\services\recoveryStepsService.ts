import { supabase } from "../db/supabaseClient";
import { RecoverySteps } from "../models/gcp_backup";

// get all recovery steps for a recovery planId
export const getRecoverySteps = async (recoveryPlanId: string) => {
	const { data, error } = await supabase
		.from("recovery_steps_new")
		.select("*")
		.eq("recovery_plan_id", recoveryPlanId);

	if (error) {
		console.error("Error getting recovery steps:", error);
		throw error;
	}
	return data;
};

// create a recovery step
export const createRecoveryStep = async (recoveryStep: RecoverySteps) => {
	const { data, error } = await supabase.from("recovery_steps_new").insert(recoveryStep);
};

// update a recovery step
export const updateRecoveryStep = async (recoveryStep: RecoverySteps) => {
	const { data, error } = await supabase
		.from("recovery_steps_new")
		.update(recoveryStep)
		.eq("id", recoveryStep.id);
};

// delete a recovery step
export const deleteRecoveryStep = async (recoveryStep: RecoverySteps) => {
	const { data, error } = await supabase
		.from("recovery_steps_new")
		.delete()
		.eq("id", recoveryStep.id);
};
