# NextDR Software Installation Script

This script facilitates the installation and management of the NextDR software suite. It supports both **interactive mode** and **express mode** for flexibility and ease of use.

## Features

- **Interactive Mode**: Provides a menu-driven interface for step-by-step installation and management.
- **Express Mode**: Allows for quick, automated installation with predefined options.
- **Component Management**:
  - Check and install all required prerequesite packages (NPM, GIT, Docker, Supabase, GAWK, etc). You may be prompted individually by each package to confirm installation, permissions, disk usage, etc.
  - Pull database schema from a remote Supabase instance. Note - Remote Supabase database access token and project password required to download schema. Due to the number of supabase calls that require the project password to be reentered, it is advisable to provide this as a commandline parameter to the install script so it can be passed on automatically.
  - Install a new Supabase Docker container.
  - Clean/remove existing Supabase Docker containers.
  - Install the NextDR client/server package.

---

## Prerequisites

Before running the script, ensure the following packages are installed on your system:

1. **Node.js and NPM/NPX**  
   Install from [Node.js Downloads](https://nodejs.org/).
2. **Git**  
   Install from [Git Downloads](https://git-scm.com/downloads).
3. **Docker**  
   Install from [Docker Desktop](https://www.docker.com/products/docker-desktop/).

---

## Usage

### Running the Script

To execute the script, open a terminal in the directory containing the script and run:

```bash
sudo su
./ndrInstall.sh [--express expressMode] [--password mypassword] [--remote-token supabaseRemoteDBToken] [--debug]

Pull the database schema from a remote instance (will interactively prompt for password):
ndrInstall.sh --express pullschema

Run with a remote password:
ndrInstall.sh --express pullschema --password 'mypassword' --remote-token 'supabaseRemoteDBToken'
eg. ndrInstall.sh --express pullschema --password 'pass123' --remote-token '********************************************'

Install a new Supabase Docker container:
ndrInstall.sh --express installcontainer

Install a new Supabase Docker container with password:
ndrInstall.sh --express installcontainer --password 'mypassword'

Pull the schema and install a new container (will interactively prompt for password):
ndrInstall.sh --express pullandinstall

Run with a remote password:
ndrInstall.sh --express pullandinstall --password 'mypassword' --remote-token 'supabaseRemoteDBToken'

Clean and remove existing containers:
ndrInstall.sh --express cleancontainer

Notes
The --password flag is optional and used for remote Supabase operations requiring authentication.
The --debug flag enables verbose output for troubleshooting.
