import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useModalStore } from "@/lib/store/useStore";
import { useToast } from "@/hooks/use-toast";
import { useAddDatacenter } from "@/lib/api/hooks/datacenters";

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	hypervisor_type: z.enum(["GCP", "AWS", "VMware", "Proxmox"], {
		required_error: "Please select a hypervisor type",
	}),
	project_id: z.string().optional(),
	apitoken: z.string().min(10, "API token is required"),
});

type FormValues = z.infer<typeof formSchema>;

const DatacenterForm = () => {
	const { onClose } = useModalStore();
	const { toast } = useToast();

	const addDatacenter = useAddDatacenter();

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			hypervisor_type: undefined,
			project_id: "",
			apitoken: "",
		},
	});

	const onSubmit = (data: FormValues) => {
		try {
			addDatacenter.mutate({
				name: data.name,
				hypervisor_type: data.hypervisor_type,
				project_id: data.project_id,
				apitoken: data.apitoken,
			});
			toast({
				title: "Datacenter added",
				description: `${data.name} has been added successfully`,
			});
		} catch (error) {
			console.error("Error adding datacenter:", error);
			toast({
				title: "Error",
				description: "Failed to add datacenter",
				variant: "destructive",
			});
		} finally {
			onClose();
		}
	};

	const hypervisorType = form.watch("hypervisor_type");

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Datacenter Name</FormLabel>
							<FormControl>
								<Input placeholder="e.g. GCP Production" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="hypervisor_type"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Hypervisor Type</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select a hypervisor type" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="GCP">Google Cloud Platform</SelectItem>
									<SelectItem value="AWS">Amazon Web Services</SelectItem>
									<SelectItem value="VMware">VMware vSphere</SelectItem>
									<SelectItem value="Proxmox">Proxmox VE</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				{(hypervisorType === "GCP" || hypervisorType === "AWS") && (
					<FormField
						control={form.control}
						name="project_id"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{hypervisorType === "GCP" ? "Project ID" : "AWS Account ID"}</FormLabel>
								<FormControl>
									<Input
										placeholder={hypervisorType === "GCP" ? "my-gcp-project-123" : "************"}
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				)}

				<FormField
					control={form.control}
					name="apitoken"
					render={({ field }) => (
						<FormItem>
							<FormLabel>API Credentials</FormLabel>
							<FormControl>
								<Textarea
									placeholder={
										hypervisorType === "GCP"
											? "Paste your GCP service account JSON"
											: hypervisorType === "AWS"
											? "Paste your AWS access key/secret"
											: "Enter API credentials"
									}
									rows={5}
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="flex justify-end space-x-2">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button type="submit" className="bg-dr-purple hover:bg-dr-purple-dark">
						Save Datacenter
					</Button>
				</div>
			</form>
		</Form>
	);
};

export default DatacenterForm;
