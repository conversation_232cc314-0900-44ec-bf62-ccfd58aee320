import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	<PERSON>alogTitle,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/sonner";
import { useModalStore } from "@/lib/store/useStore";
import { ApplicationGroup, SupabaseSnapshot } from "@/lib/types";
import { useVMsInZone } from "@/lib/api/hooks/vms";
import { useUpdateSnapshot } from "@/lib/api/hooks/snapshots";

const formSchema = z.object({
	frequency: z.enum(["hourly", "daily", "weekly", "monthly"]),
	retention_period: z.number().min(1, "Retention period must be at least 1"),
	start_time: z.string().min(1, "Start time is required"),
	vm_ids: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface SnapshotEditModalProps {
	selectedGroup: ApplicationGroup | null;
	projectId: string;
	zone: string;
	datacenterId: string;
}

const SnapshotEditModal = ({
	selectedGroup,
	projectId,
	zone,
	datacenterId,
}: SnapshotEditModalProps) => {
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "editSnapshot";
	const snapshot = modalData?.snapshot as SupabaseSnapshot | undefined;
	const [selectedVMs, setSelectedVMs] = useState<string[]>([]);
	const updateSnapshot = useUpdateSnapshot();

	const { data: vmData = [], isLoading: isLoadingVMs } = useVMsInZone(
		zone,
		projectId,
		datacenterId,
		{
			enabled: showModal && !!projectId && !!zone && !!datacenterId,
		}
	);

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
		setValue,
		watch,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			frequency: "daily",
			retention_period: 7,
			start_time: "00:00",
			vm_ids: [],
		},
	});

	// Reset form when modal opens and populate with snapshot data
	useEffect(() => {
		if (showModal && snapshot) {
			reset({
				frequency: snapshot.frequency,
				retention_period: snapshot.retention_period,
				start_time: snapshot.start_time,
				vm_ids: snapshot.vm_ids,
			});
			setSelectedVMs(snapshot.vm_ids || []);
		}
	}, [showModal, reset, snapshot]);

	const handleVMToggle = (vmId: string) => {
		setSelectedVMs((prev) => {
			if (prev.includes(vmId)) {
				return prev.filter((id) => id !== vmId);
			} else {
				return [...prev, vmId];
			}
		});
	};

	const onSubmit = async (data: FormValues) => {
		try {
			if (!snapshot) {
				toast.error("No snapshot data available");
				return;
			}

			if (selectedVMs.length === 0) {
				toast.error("Please select at least one VM");
				return;
			}

			// Create updated snapshot object
			const updatedSnapshot: SupabaseSnapshot = {
				...snapshot,
				frequency: data.frequency,
				retention_period: data.retention_period,
				start_time: data.start_time,
				vm_ids: selectedVMs,
			};

			updateSnapshot.mutate(updatedSnapshot);
			onClose();
		} catch (error) {
			console.error(`Error updating snapshot:`, error);
			toast.error(`Failed to update snapshot`);
		}
	};

	return (
		<Dialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">Edit Snapshot Schedule</DialogTitle>
					<DialogDescription>Update the snapshot schedule settings</DialogDescription>
				</DialogHeader>

				{snapshot ? (
					<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
						<div className="space-y-2">
							<label htmlFor="frequency" className="text-sm font-medium">
								Frequency
							</label>
							<Select
								onValueChange={(value) =>
									setValue("frequency", value as "hourly" | "daily" | "weekly" | "monthly")
								}
								defaultValue={snapshot.frequency}
							>
								<SelectTrigger>
									<SelectValue placeholder="Select frequency" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="hourly">Hourly</SelectItem>
									<SelectItem value="daily">Daily</SelectItem>
									<SelectItem value="weekly">Weekly</SelectItem>
									<SelectItem value="monthly">Monthly</SelectItem>
								</SelectContent>
							</Select>
							{errors.frequency && (
								<p className="text-sm text-red-500">{errors.frequency.message}</p>
							)}
						</div>

						<div className="space-y-2">
							<label htmlFor="retention_period" className="text-sm font-medium">
								Retention Period (days)
							</label>
							<Input
								id="retention_period"
								type="number"
								min="1"
								{...register("retention_period", { valueAsNumber: true })}
							/>
							{errors.retention_period && (
								<p className="text-sm text-red-500">{errors.retention_period.message}</p>
							)}
						</div>

						<div className="space-y-2">
							<label htmlFor="start_time" className="text-sm font-medium">
								Start Time
							</label>
							<Input id="start_time" type="time" {...register("start_time")} />
							{errors.start_time && (
								<p className="text-sm text-red-500">{errors.start_time.message}</p>
							)}
						</div>

						<div className="space-y-2">
							<label className="text-sm font-medium">Select VMs</label>
							{isLoadingVMs ? (
								<p className="text-sm text-muted-foreground">Loading VMs...</p>
							) : vmData.length > 0 ? (
								<div className="max-h-60 overflow-y-auto border rounded-md p-2">
									{vmData.map((vm) => (
										<div key={vm.id || vm.name} className="flex items-center space-x-2 py-2">
											<Checkbox
												id={`vm-${vm.id || vm.name}`}
												checked={selectedVMs.includes(vm.id || vm.name)}
												onCheckedChange={() => handleVMToggle(vm.id || vm.name)}
											/>
											<Label htmlFor={`vm-${vm.id || vm.name}`} className="text-sm cursor-pointer">
												{vm.name}
											</Label>
										</div>
									))}
								</div>
							) : (
								<p className="text-sm text-muted-foreground">No VMs available</p>
							)}
							{selectedVMs.length === 0 && (
								<p className="text-sm text-amber-500">Please select at least one VM</p>
							)}
						</div>

						<DialogFooter>
							<Button variant="outline" type="button" onClick={onClose}>
								Cancel
							</Button>
							<Button
								type="submit"
								className="bg-dr-purple hover:bg-dr-purple-dark"
								disabled={isSubmitting || selectedVMs.length === 0}
							>
								{isSubmitting ? "Updating..." : "Update Schedule"}
							</Button>
						</DialogFooter>
					</form>
				) : (
					<div className="py-4 text-center">
						<p className="text-muted-foreground">No snapshot data available</p>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
};

export default SnapshotEditModal;
