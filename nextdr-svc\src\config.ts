import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

interface Config {
  PORT: number;
  NODE_ENV: string;
  SUPABASE_URL: string;
  SUPABASE_SERVICE_KEY: string;
  FRONTEND_URL: string;
  GMAIL_USER: string;
  GMAIL_APP_PASSWORD: string;
  EMAIL_FROM: string;
  APPROVAL_TOKEN_EXPIRY_HOURS: number;
}

export const config: Config = {
  PORT: parseInt(process.env.PORT || '3000'),
  NODE_ENV: process.env.NODE_ENV || 'development',
  SUPABASE_URL: process.env.SUPABASE_URL || '',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY || '',
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:8080',
  GMAIL_USER: process.env.GMAIL_USER || '',
  GMAIL_APP_PASSWORD: process.env.GMAIL_APP_PASSWORD || '',
  EMAIL_FROM: process.env.EMAIL_FROM || process.env.GMAIL_USER || '',
  APPROVAL_TOKEN_EXPIRY_HOURS: parseInt(process.env.APPROVAL_TOKEN_EXPIRY_HOURS || '24'),
};
