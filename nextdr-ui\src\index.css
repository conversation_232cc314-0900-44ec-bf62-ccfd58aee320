@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;

    --card: 0 0% 7%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 70% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 5.9% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    --accent: 142 70% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 142 70% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 9%;
    --sidebar-foreground: 240 5% 90%;
    --sidebar-primary: 142 70% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 90%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 142 70% 50%;

    --glass-bg: rgba(18, 18, 18, 0.75);
    --glass-border: rgba(255, 255, 255, 0.05);
    --glass-highlight: rgba(255, 255, 255, 0.03);
    --glass-shadow: rgba(0, 0, 0, 0.2);
    --glass-blur: 8px;

    --sidebar-bg: rgba(18, 18, 18, 0.4);
    --sidebar-border: rgba(40, 40, 40, 0.5);
    --sidebar-highlight: rgba(29, 185, 84, 0.02);
    --sidebar-active: rgba(29, 185, 84, 0.08);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-dr-dark text-foreground;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 var(--glass-shadow);
}

.sidebar-glass {
  background: rgba(18, 18, 18, 0.4);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border-right: 1px solid rgba(40, 40, 40, 0.5);
}

.glass-effect-light {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  border: 1px solid var(--glass-border);
}

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  box-shadow: 0 4px 16px 0 var(--glass-shadow);
  transition: all 0.3s ease;
}

.glass-card:hover {
  border-color: rgba(29, 185, 84, 0.2);
  box-shadow: 0 8px 32px 0 rgba(29, 185, 84, 0.15);
}