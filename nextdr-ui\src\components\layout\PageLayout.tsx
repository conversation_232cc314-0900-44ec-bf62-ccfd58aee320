import React from "react";
import { motion } from "framer-motion";
import { useSidebarStore } from "@/lib/store/useStore";

interface PageLayoutProps {
	children: React.ReactNode;
	title: string;
}

const PageLayout = ({ children, title }: PageLayoutProps) => {
	const { collapsed } = useSidebarStore();

	const mainColor = "#1DB954";
	const bgColor = "#121212";

	return (
		<>
			<motion.main
				initial={{ marginLeft: collapsed ? 64 : 250 }}
				animate={{ marginLeft: collapsed ? 64 : 250 }}
				transition={{ duration: 0.3, ease: "easeInOut" }}
				className="min-h-screen flex flex-col relative"
				style={{
					backgroundColor: bgColor,
					backgroundImage: `
            radial-gradient(circle at 50% 0%, rgba(29, 185, 84, 0.08), transparent 70%),
            radial-gradient(circle at 100% 100%, rgba(29, 185, 84, 0.05), transparent 50%),
            linear-gradient(to bottom, rgba(29, 185, 84, 0.03), transparent 30%)
          `,
				}}
			>
				{/* Glass morphism floating orbs for visual interest */}
				<div className="absolute top-[10%] right-[5%] w-64 h-64 rounded-full bg-[rgba(29,185,84,0.03)] blur-3xl pointer-events-none"></div>
				<div className="absolute bottom-[20%] left-[10%] w-80 h-80 rounded-full bg-[rgba(29,185,84,0.02)] blur-3xl pointer-events-none"></div>

				{/* Content container with subtle glass effect */}
				<div className="p-5 md:p-6 relative z-10">{children}</div>
			</motion.main>
		</>
	);
};

export default PageLayout;
