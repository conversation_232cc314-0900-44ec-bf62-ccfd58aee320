import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
	"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
	{
		variants: {
			variant: {
				default:
					"bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg hover:translate-y-[-1px] active:translate-y-[1px]",

				destructive:
					"bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg border-b-4 border-red-900 hover:border-b-2 hover:mt-[2px]",

				outline:
					"border-2 border-input bg-transparent text-foreground hover:bg-accent/20 hover:text-accent-foreground hover:border-accent backdrop-blur-sm",

				secondary:
					"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-l-4 border-r-4 border-primary/40 hover:border-primary",

				ghost:
					"text-foreground hover:bg-accent/30 hover:text-accent-foreground hover:shadow-inner transition-all duration-300",

				link: "text-primary underline-offset-4 hover:underline decoration-2 hover:decoration-4 hover:text-primary/80 p-0 h-auto",

				gradient:
					"bg-gradient-to-r from-purple-600 to-blue-500 text-white hover:from-purple-700 hover:to-blue-600 shadow-md hover:shadow-lg hover:shadow-purple-500/20",

				glassmorphic:
					"bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 shadow-lg shadow-black/5",

				pill: "rounded-full bg-primary text-primary-foreground hover:bg-primary/90 shadow-md px-6",

				floating:
					"shadow-lg hover:shadow-xl bg-primary text-primary-foreground hover:bg-primary/90 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-200",
			},

			size: {
				default: "h-10 px-4 py-2",
				sm: "h-8 rounded-md px-3 text-xs",
				lg: "h-12 rounded-md px-8 text-base",
				xl: "h-14 rounded-md px-10 text-lg",
				icon: "h-10 w-10",
				iconSm: "h-8 w-8",
				iconLg: "h-12 w-12",
			},
		},

		defaultVariants: {
			variant: "default",
			size: "default",
		},
	}
);

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof buttonVariants> {
	asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, ...props }, ref) => {
		const Comp = asChild ? Slot : "button";
		return (
			<Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
		);
	}
);
Button.displayName = "Button";

export { Button, buttonVariants };
