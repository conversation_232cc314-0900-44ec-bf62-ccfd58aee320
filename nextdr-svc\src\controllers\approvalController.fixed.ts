import { Request, Response, NextFunction } from "express";
import { processApproval } from "../services/approvalService";
import { supabase } from "../db/supabaseClient";

/**
 * Get step details by approval token
 */
export const getStepByToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { token } = req.params;

    if (!token) {
      res.status(400).json({ error: "Token is required" });
      return;
    }

    // Validate token
    const { data: tokenData, error: tokenError } = await supabase
      .from("approval_tokens")
      .select("*")
      .eq("token", token)
      .single();

    if (tokenError || !tokenData) {
      res.status(404).json({ error: "Invalid or expired approval token" });
      return;
    }

    // Check if token is expired
    if (new Date(tokenData.expires_at) < new Date()) {
      res.status(400).json({ error: "Approval token has expired" });
      return;
    }

    // Get step details
    const { data: step, error: stepError } = await supabase
      .from("recovery_steps_new")
      .select("*")
      .eq("id", tokenData.step_id)
      .single();

    if (stepError || !step) {
      res.status(404).json({ error: "Failed to get step details" });
      return;
    }

    // Return step details
    res.status(200).json(step);
  } catch (error: any) {
    console.error("Error getting step by token:", error);
    res.status(500).json({ error: error.message || "Internal server error" });
  }
};

/**
 * Process approval decision
 */
export const handleApprovalDecision = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { token } = req.params;
    const { decision, comment } = req.body;

    if (!token) {
      res.status(400).json({ error: "Token is required" });
      return;
    }

    if (!decision || (decision !== "approved" && decision !== "rejected")) {
      res.status(400).json({ error: "Valid decision (approved/rejected) is required" });
      return;
    }

    // Get client IP address
    const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress || "unknown";

    // Process approval
    const step = await processApproval(
      token,
      decision as "approved" | "rejected",
      comment || "",
      clientIp as string
    );

    // Return updated step
    res.status(200).json({
      message: `Step ${decision} successfully`,
      step
    });
  } catch (error: any) {
    console.error("Error processing approval decision:", error);
    res.status(500).json({ error: error.message || "Internal server error" });
  }
};
