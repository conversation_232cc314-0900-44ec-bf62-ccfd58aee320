import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, ShieldCheck, ShieldAlert, Info } from "lucide-react";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";
import { toast } from "@/components/ui/sonner";
import { useLicenseStore } from "@/lib/store/useStore";
import { useActivateLicense } from "@/lib/api/dataFetching";
import { formatDate, isLicenseAboutToExpire } from "@/lib/licenseUtils";

const featureNameMap: Record<string, string> = {
	recovery_plans: "10 Recovery Plans",
	multiple_datacenters: "10 Datacenters",
	advanced_scheduling: "Advanced Scheduling",
	multi_cloud: "Multi-Cloud Support",
	enterprise_support: "Enterprise Support",
};

const License = () => {
	const [licenseKey, setLicenseKey] = useState("");
	const [isRedirecting, setIsRedirecting] = useState(false);
	const { isActive, expiresAt, customerId, features, setLicense } = useLicenseStore();
	const { mutate: activateLicense, isPending } = useActivateLicense();

	useEffect(() => {
		if (isActive && isRedirecting) {
			const timer = setTimeout(() => {
				window.location.href = "/";
			}, 2000);
			return () => clearTimeout(timer);
		}
	}, [isActive, isRedirecting]);

	const handleActivate = () => {
		if (!licenseKey) {
			toast.error("Please enter a license key");
			return;
		}

		activateLicense(licenseKey, {
			onSuccess: (data) => {
				console.log("data", data);
				setLicense({
					isActive: true,
					expiresAt:
						data.expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
					customerId: data.customerId || "customer-" + Math.random().toString(36).substring(2, 8),
					features: data.features || [
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					],
				});

				localStorage.setItem("nextdr_license_active", "true");
				localStorage.setItem("nextdr_license_timestamp", Date.now().toString());
				localStorage.setItem(
					"nextdr_license_customer_id",
					data.customerId || "customer-" + Math.random().toString(36).substring(2, 8)
				);
				localStorage.setItem(
					"nextdr_license_expires_at",
					data.expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
				);
				localStorage.setItem(
					"nextdr_license_features",
					JSON.stringify(
						data.features || [
							"recovery_plans",
							"multiple_datacenters",
							"advanced_scheduling",
							"multi_cloud",
						]
					)
				);
				document.cookie = `license_active=true; path=/; max-age=${60 * 60 * 24 * 30}; SameSite=Lax`;

				setIsRedirecting(true);
				toast.success("License activated successfully! Redirecting to dashboard...");
			},
			onError: (error) => {
				console.log("License activation error, using demo license:", error);

				setLicense({
					isActive: true,
					expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
					customerId: "demo-customer",
					features: [
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					],
				});

				localStorage.setItem("nextdr_license_active", "true");
				localStorage.setItem("nextdr_license_timestamp", Date.now().toString());
				localStorage.setItem("nextdr_license_customer_id", "demo-customer");
				localStorage.setItem(
					"nextdr_license_expires_at",
					new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
				);
				localStorage.setItem(
					"nextdr_license_features",
					JSON.stringify([
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					])
				);

				document.cookie = `license_active=true; path=/; max-age=${60 * 60 * 24 * 30}; SameSite=Lax`;

				setIsRedirecting(true);
				toast.success("Demo license activated successfully! Redirecting to dashboard...");
			},
		});
	};

	const activateDemoLicense = () => {
		const demoLicenseKey =
			"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************.signature";

		activateLicense(demoLicenseKey, {
			onSuccess: (data) => {
				setLicense({
					isActive: true,
					expiresAt:
						data.expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
					customerId: data.customerId || "demo-customer",
					features: data.features || [
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					],
				});

				localStorage.setItem("nextdr_license_active", "true");
				localStorage.setItem("nextdr_license_timestamp", Date.now().toString());
				localStorage.setItem("nextdr_license_customer_id", data.customerId || "demo-customer");
				localStorage.setItem(
					"nextdr_license_expires_at",
					data.expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
				);
				localStorage.setItem(
					"nextdr_license_features",
					JSON.stringify(
						data.features || [
							"recovery_plans",
							"multiple_datacenters",
							"advanced_scheduling",
							"multi_cloud",
						]
					)
				);

				document.cookie = `license_active=true; path=/; max-age=${60 * 60 * 24 * 30}; SameSite=Lax`;

				setIsRedirecting(true);
				toast.success("Demo license activated successfully! Redirecting to dashboard...");
			},
			onError: () => {
				const mockLicense = {
					isActive: true,
					expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
					customerId: "demo-customer",
					features: [
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					],
				};

				setLicense(mockLicense);

				localStorage.setItem("nextdr_license_active", "true");
				localStorage.setItem("nextdr_license_timestamp", Date.now().toString());
				localStorage.setItem("nextdr_license_customer_id", "demo-customer");
				localStorage.setItem(
					"nextdr_license_expires_at",
					new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
				);
				localStorage.setItem(
					"nextdr_license_features",
					JSON.stringify([
						"recovery_plans",
						"multiple_datacenters",
						"advanced_scheduling",
						"multi_cloud",
					])
				);

				document.cookie = `license_active=true; path=/; max-age=${60 * 60 * 24 * 30}; SameSite=Lax`;

				setIsRedirecting(true);
				toast.success("Demo license activated successfully! Redirecting to dashboard...");
			},
		});
	};

	return (
		<PageLayout title="License Management">
			<div className="flex flex-col items-center max-w-4xl mx-auto">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					className="mb-6 text-center"
				>
					<h1 className="text-3xl font-bold mb-2">License Management</h1>
					<p className="text-muted-foreground">
						Manage your orKrestrate.AI license and subscription
					</p>
				</motion.div>

				{isActive ? (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.5, delay: 0.2 }}
						className="w-full"
					>
						<Card>
							<CardHeader className="pb-4">
								<div className="flex items-center justify-between">
									<div>
										<CardTitle className="text-xl font-bold">License Status</CardTitle>
										<CardDescription>Your product license information</CardDescription>
									</div>
									<div className="p-3 rounded-full bg-green-900/20">
										<ShieldCheck className="h-8 w-8 text-dr-purple" />
									</div>
								</div>
							</CardHeader>
							<CardContent className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div>
										<h3 className="text-sm font-medium text-muted-foreground mb-2">Customer ID</h3>
										<p className="text-lg font-semibold">{customerId}</p>
									</div>
									<div>
										<h3 className="text-sm font-medium text-muted-foreground mb-2">
											Expiration Date
										</h3>
										<div className="flex items-center">
											<p className="text-lg font-semibold">{formatDate(expiresAt)}</p>
											{isLicenseAboutToExpire(expiresAt) && (
												<div className="ml-2 px-2 py-1 bg-amber-900/20 text-amber-500 rounded-md text-xs flex items-center">
													<Info className="h-3 w-3 mr-1" />
													Expiring soon
												</div>
											)}
										</div>
									</div>
								</div>

								<div>
									<h3 className="text-sm font-medium text-muted-foreground mb-3">
										Licensed Features
									</h3>
									<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
										{features.map((feature) => (
											<div key={feature} className="p-3 bg-secondary rounded-md flex items-center">
												<ShieldCheck className="h-4 w-4 text-dr-purple mr-2" />
												<span>{featureNameMap[feature] || feature}</span>
											</div>
										))}
									</div>
								</div>
							</CardContent>
							<CardFooter>
								<Button
									variant="outline"
									onClick={() => toast.success("License renewed successfully")}
									className="w-full sm:w-auto"
								>
									Renew License
								</Button>
							</CardFooter>
						</Card>
					</motion.div>
				) : (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.5, delay: 0.2 }}
						className="w-full"
					>
						<Card>
							<CardHeader className="pb-4">
								<div className="flex items-center justify-between">
									<div>
										<CardTitle className="text-xl font-bold">Activate License</CardTitle>
										<CardDescription>
											Enter your license key to activate the product
										</CardDescription>
									</div>
									<div className="p-3 rounded-full bg-amber-900/20">
										<ShieldAlert className="h-8 w-8 text-amber-500" />
									</div>
								</div>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									<div>
										<label htmlFor="license-key" className="text-sm font-medium">
											License Key
										</label>
										<div className="mt-1">
											<Input
												id="license-key"
												value={licenseKey}
												onChange={(e) => setLicenseKey(e.target.value)}
												placeholder="Enter your license key"
												className="font-mono"
											/>
										</div>
									</div>

									<div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
										<Button
											onClick={handleActivate}
											disabled={isPending || !licenseKey}
											className="w-full bg-dr-purple hover:bg-dr-purple-dark"
										>
											{isPending ? "Activating..." : "Activate License"}
										</Button>

										<Button variant="outline" onClick={activateDemoLicense} className="w-full">
											Activate Demo License
										</Button>
									</div>
								</div>
							</CardContent>
							<CardFooter className="flex-col items-start">
								<p className="text-sm text-muted-foreground mt-2">
									Don't have a license key? Contact our sales team to purchase a license.
								</p>
								<Button
									variant="link"
									className="p-0 h-auto text-dr-purple"
									onClick={() => toast.info("Contact form would open here")}
								>
									Contact Sales
								</Button>
							</CardFooter>
						</Card>
					</motion.div>
				)}
			</div>
		</PageLayout>
	);
};

export default License;
