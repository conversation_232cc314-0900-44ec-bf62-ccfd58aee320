import { create } from "zustand";

interface SidebarStore {
	collapsed: boolean;
	toggleCollapse: () => void;
	setCollapsed: (collapsed: boolean) => void;
}

export const useSidebarStore = create<SidebarStore>((set) => ({
	collapsed: false,
	toggleCollapse: () => set((state) => ({ collapsed: !state.collapsed })),
	setCollapsed: (collapsed: boolean) => set({ collapsed }),
}));

interface ModalStore {
	isOpen: boolean;
	modalType: string | null;
	modalData: any;
	onOpen: (modalType: string, data?: any) => void;
	onClose: () => void;
}

export const useModalStore = create<ModalStore>((set) => ({
	isOpen: false,
	modalType: null,
	modalData: null,
	onOpen: (modalType, data = null) => set({ isOpen: true, modalType, modalData: data }),
	onClose: () => set({ isOpen: false, modalType: null, modalData: null }),
}));

interface LicenseState {
	isActive: boolean;
	expiresAt: string | null;
	customerId: string | null;
	features: string[];
	setLicense: (license: {
		isActive: boolean;
		expiresAt: string | null;
		customerId: string | null;
		features: string[];
	}) => void;
	clearLicense: () => void;
}

export const useLicenseStore = create<LicenseState>((set) => ({
	isActive: false,
	expiresAt: null,
	customerId: null,
	features: [],
	setLicense: (license) =>
		set({
			isActive: license.isActive,
			expiresAt: license.expiresAt,
			customerId: license.customerId,
			features: license.features,
		}),
	clearLicense: () =>
		set({
			isActive: false,
			expiresAt: null,
			customerId: null,
			features: [],
		}),
}));
