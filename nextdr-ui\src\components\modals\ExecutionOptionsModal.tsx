import React from "react";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useModalStore } from "@/lib/store/useStore";
import { Button } from "@/components/ui/button";
import { Play, ArrowRight } from "lucide-react";

interface ExecutionOptionsModalProps {}

const ExecutionOptionsModal: React.FC<ExecutionOptionsModalProps> = () => {
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "executionOptions";

	const title = modalData?.title || "Execution Options";
	const message = modalData?.message || "Choose how to execute the recovery plan:";
	const onRestart = modalData?.onRestart || (() => {});
	const onResume = modalData?.onResume || (() => {});
	const canResume = modalData?.canResume || false;
	const currentCheckpointId = modalData?.currentCheckpointId;

	const handleRestart = () => {
		onRestart();
		onClose();
	};

	const handleResume = () => {
		if (currentCheckpointId) {
			onResume(currentCheckpointId);
		}
		onClose();
	};

	return (
		<AlertDialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>{title}</AlertDialogTitle>
					<AlertDialogDescription>{message}</AlertDialogDescription>
				</AlertDialogHeader>
				<div className="flex flex-col gap-4 py-4">
					<Button onClick={handleRestart} className="w-full" variant="outline">
						<Play className="mr-2 h-4 w-4" />
						Start from beginning
						<span className="ml-2 text-xs">(Resets all progress)</span>
					</Button>

					<Button onClick={handleResume} className="w-full" variant="floating">
						<ArrowRight className="mr-2 h-4 w-4" />
						Resume from last checkpoint
						<span className="ml-2 text-xs">(Continues where you left off)</span>
					</Button>
				</div>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancel</AlertDialogCancel>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
};

export default ExecutionOptionsModal;
