import { BaseStepExecutor } from './baseStepExecutor';
import { VMRestoreExecutor } from './vmRestoreExecutor';
import { IaCExecutor } from './iacExecutor';
import { DatabaseRestoreExecutor } from './databaseRestoreExecutor';
import { VirusCheckExecutor } from './virusCheckExecutor';
import { OSUpdateExecutor } from './osUpdateExecutor';
import { ManualStepExecutor } from './manualStepExecutor';
import { ApprovalExecutor } from './approvalExecutor';
import { VerificationExecutor } from './verificationExecutor';
import { ScriptExecutor } from './scriptExecutor';
import { NotificationExecutor } from './notificationExecutor';

/**
 * Factory for creating step executors based on operation type
 */
export class StepExecutorFactory {
    private executors: Map<string, BaseStepExecutor> = new Map();

    constructor() {
        this.registerExecutors();
    }

    /**
     * Register all available executors
     */
    private registerExecutors(): void {
        this.register(new VMRestoreExecutor());
        this.register(new IaCExecutor());
        this.register(new DatabaseRestoreExecutor());
        this.register(new VirusCheckExecutor());
        this.register(new OSUpdateExecutor());
        this.register(new ManualStepExecutor());
        this.register(new ApprovalExecutor());
        this.register(new VerificationExecutor());
        this.register(new ScriptExecutor());
        this.register(new NotificationExecutor());
    }

    /**
     * Register a step executor
     */
    register(executor: BaseStepExecutor): void {
        this.executors.set(executor.getStepType(), executor);
        console.log(`Registered executor for step type: ${executor.getStepType()}`);
    }

    /**
     * Get executor for operation type
     */
    getExecutor(operationType: string): BaseStepExecutor {
        const executor = this.executors.get(operationType);
        if (!executor) {
            throw new Error(`No executor found for operation type: ${operationType}`);
        }
        return executor;
    }

    /**
     * Get all registered operation types
     */
    getRegisteredTypes(): string[] {
        return Array.from(this.executors.keys());
    }

    /**
     * Check if operation type is supported
     */
    isSupported(operationType: string): boolean {
        return this.executors.has(operationType);
    }
}
