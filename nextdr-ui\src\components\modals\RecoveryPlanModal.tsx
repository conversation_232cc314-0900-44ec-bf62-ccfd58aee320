import React, { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	<PERSON>alogTitle,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useModalStore } from "@/lib/store/useStore";
import { RecoveryPlan, ApplicationGroup } from "@/lib/types";
import { toast } from "@/components/ui/sonner";
import { useApplicationGroups } from "@/lib/api/hooks/applicationGroups";
import { useAddRecoveryPlan, useUpdateRecoveryPlan } from "@/lib/api/hooks/recoveryPlans";

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	description: z.string().min(1, "Description is required"),
	app_group_id: z.string().min(1, "Application group is required"),
});

type FormValues = z.infer<typeof formSchema>;

const RecoveryPlanModal = () => {
	const { data: appGroups = [] } = useApplicationGroups();
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "recoveryPlan";

	// Check if we're in edit mode
	const isEditMode = !!modalData?.plan;
	const planToEdit = modalData?.plan as RecoveryPlan | undefined;

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
		setValue,
		watch,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: planToEdit?.name || "",
			description: planToEdit?.description || "",
			app_group_id: planToEdit?.app_group_id ? String(planToEdit.app_group_id) : "",
		},
	});

	// Reset form when modal opens/closes or when editing a different plan
	useEffect(() => {
		if (showModal) {
			reset({
				name: planToEdit?.name || "",
				description: planToEdit?.description || "",
				app_group_id: planToEdit?.app_group_id ? String(planToEdit.app_group_id) : "",
			});
		}
	}, [showModal, planToEdit, reset]);

	const selectedAppGroupId = watch("app_group_id");

	const addRecoveryPlan = useAddRecoveryPlan();
	const updateRecoveryPlan = useUpdateRecoveryPlan();

	const onSubmit = async (data: FormValues) => {
		try {
			if (isEditMode && planToEdit) {
				// Update existing plan
				const updatedPlan: RecoveryPlan = {
					...planToEdit,
					name: data.name,
					description: data.description,
					app_group_id: data.app_group_id,
				};

				updateRecoveryPlan.mutate(updatedPlan);
			} else {
				// Create new plan
				const newRecoveryPlan: Partial<RecoveryPlan> = {
					name: data.name,
					description: data.description,
					app_group_id: data.app_group_id,
					status: "pending",
				};

				addRecoveryPlan.mutate(newRecoveryPlan as any);
			}

			reset();
			onClose();
		} catch (error) {
			console.error(`Error ${isEditMode ? "updating" : "creating"} recovery plan:`, error);
			toast.error(`Failed to ${isEditMode ? "update" : "create"} recovery plan`);
		}
	};

	return (
		<Dialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						{isEditMode ? "Edit Recovery Plan" : "New Recovery Plan"}
					</DialogTitle>
					<DialogDescription>
						{isEditMode
							? "Update your recovery plan details."
							: "Create a new recovery plan to protect your application group."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="name" className="text-sm font-medium">
							Name
						</label>
						<Input id="name" placeholder="Enter recovery plan name" {...register("name")} />
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label htmlFor="description" className="text-sm font-medium">
							Description
						</label>
						<Textarea
							id="description"
							placeholder="Enter description"
							{...register("description")}
						/>
						{errors.description && (
							<p className="text-sm text-red-500">{errors.description.message}</p>
						)}
					</div>

					<div className="space-y-2">
						<label htmlFor="app_group_id" className="text-sm font-medium">
							Application Group
						</label>
						<Select
							onValueChange={(value) => setValue("app_group_id", value)}
							value={selectedAppGroupId}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select application group" />
							</SelectTrigger>
							<SelectContent>
								{appGroups.map((group) => (
									<SelectItem key={group.id} value={String(group.id)}>
										{group.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						{errors.app_group_id && (
							<p className="text-sm text-red-500">{errors.app_group_id.message}</p>
						)}
					</div>

					<DialogFooter>
						<Button variant="outline" type="button" onClick={onClose}>
							Cancel
						</Button>
						<Button
							type="submit"
							className="bg-dr-purple hover:bg-dr-purple-dark"
							disabled={isSubmitting}
						>
							{isSubmitting
								? isEditMode
									? "Updating..."
									: "Creating..."
								: isEditMode
								? "Update Plan"
								: "Create Plan"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default RecoveryPlanModal;
