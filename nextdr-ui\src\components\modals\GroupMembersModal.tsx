import React from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useModalStore } from "@/lib/store/useStore";
import {
	useGroupMembers,
	useAddGroupMember,
	useRemoveGroupMember,
} from "@/lib/api/hooks/internalGroups";
import { useUserProfiles } from "@/hooks/useUserProfiles";
import { toast } from "@/components/ui/sonner";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

export const GroupMembersModal = () => {
	const { isOpen, modalType, onClose, modalData } = useModalStore();
	const showModal = isOpen && modalType === "groupMembers";
	const group = modalData?.data;

	const { data: members = [], isLoading: isLoadingMembers } = useGroupMembers(group?.id);
	const { profiles: users = [], isLoading: isLoadingUsers } = useUserProfiles();
	const addMember = useAddGroupMember();
	const removeMember = useRemoveGroupMember();

	const handleAddMember = async (userId: string) => {
		if (!group?.id) return;

		try {
			await addMember.mutateAsync({
				groupId: group.id,
				userId,
			});
			toast.success("Member added successfully");
		} catch (error) {
			console.error("Error adding member:", error);
			toast.error("Failed to add member");
		}
	};

	const handleRemoveMember = async (userId: string) => {
		if (!group?.id) return;

		try {
			await removeMember.mutateAsync({
				groupId: group.id,
				userId,
			});
			toast.success("Member removed successfully");
		} catch (error) {
			console.error("Error removing member:", error);
			toast.error("Failed to remove member");
		}
	};

	const isMember = (userId: string) => {
		return members.some((member) => member.user_id === userId);
	};

	return (
		<Dialog open={showModal} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>Manage Group Members</DialogTitle>
					<DialogDescription>
						Add or remove members from {group?.name || "this group"}
					</DialogDescription>
				</DialogHeader>

				<div className="grid gap-6">
					<div>
						<h3 className="text-sm font-medium mb-2">Current Members</h3>
						<ScrollArea className="h-[200px] rounded-md border p-4">
							{isLoadingMembers ? (
								<div className="space-y-2">
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
								</div>
							) : members.length === 0 ? (
								<p className="text-sm text-muted-foreground">No members yet</p>
							) : (
								<div className="space-y-2">
									{members.map((member) => {
										const user = users.find((u) => u.id === member.user_id);
										return (
											<div
												key={member.id}
												className="flex items-center justify-between p-2 rounded-md bg-secondary/50"
											>
												<div>
													<p className="text-sm font-medium">{user?.email}</p>
													<div className="flex gap-2 mt-1">
														<Badge variant="outline">{user?.role}</Badge>
														<Badge variant={user?.status === "active" ? "default" : "destructive"}>
															{user?.status}
														</Badge>
													</div>
												</div>
												<Button
													variant="destructive"
													size="sm"
													onClick={() => handleRemoveMember(member.user_id)}
												>
													Remove
												</Button>
											</div>
										);
									})}
								</div>
							)}
						</ScrollArea>
					</div>

					<div>
						<h3 className="text-sm font-medium mb-2">Available Users</h3>
						<ScrollArea className="h-[200px] rounded-md border p-4">
							{isLoadingUsers ? (
								<div className="space-y-2">
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
								</div>
							) : (
								<div className="space-y-2">
									{users.map(
										(user) =>
											!isMember(user.id) && (
												<div
													key={user.id}
													className="flex items-center justify-between p-2 rounded-md bg-secondary/50"
												>
													<div>
														<p className="text-sm font-medium">{user.email}</p>
														<div className="flex gap-2 mt-1">
															<Badge variant="outline">{user.role}</Badge>
															<Badge variant={user.status === "active" ? "default" : "destructive"}>
																{user.status}
															</Badge>
														</div>
													</div>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleAddMember(user.id)}
													>
														Add
													</Button>
												</div>
											)
									)}
								</div>
							)}
						</ScrollArea>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};
