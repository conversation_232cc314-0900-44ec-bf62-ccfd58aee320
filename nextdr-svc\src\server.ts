import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import helloRoutes from "./routes/gcpRoutes";
//import backupRoutes from "./routes/backupRoutes";
import app from "./app";
import { snapshotScheduler } from "./services/schedulerService";
import { gcpAdSchedulerService } from "./services/gcpAdSchedulerService";
dotenv.config();

const PORT = process.env.PORT || 8081;
// Middleware
app.use(cors({ origin: "*", credentials: true }));
app.use(express.json());

// add backup routes
app.use("/api", helloRoutes);
//app.use("/api/backup", backupRoutes);
app.listen(PORT, async () => {
	console.log(`Server running on http://localhost:${PORT}`);

	try {
		await gcpAdSchedulerService.initialize();
		console.log("GCP AD scheduler initialized");
	} catch (error) {
		console.error("Failed to initialize GCP AD scheduler:", error);
	}
});

snapshotScheduler
	.initializeSchedules()
	.then(() => console.log("Backup schedules initialized."))
	.catch((error) => console.error("Failed to initialize backup schedules:", error));
