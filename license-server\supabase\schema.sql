-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Licenses Table
create table licenses (
  id uuid default uuid_generate_v4() primary key,
  customer_id uuid references auth.users(id),
  license_key text unique,
  status text check (status in ('active', 'inactive', 'expired')),
  features jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  expires_at timestamp with time zone,
  last_verified timestamp with time zone,
  usage_count integer default 0,
  created_by uuid references auth.users(id)
);

-- License Usage Logs
create table license_logs (
  id uuid default uuid_generate_v4() primary key,
  license_id uuid references licenses(id),
  action text,
  timestamp timestamp with time zone default timezone('utc'::text, now()),
  metadata jsonb
);

-- Customer Profiles (extends auth.users)
create table customer_profiles (
  id uuid references auth.users(id) primary key,
  company_name text,
  contact_number text,
  address text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
alter table licenses enable row level security;
alter table license_logs enable row level security;
alter table customer_profiles enable row level security;

-- Licenses table policies
create policy "Admins have full access"
on licenses for all
to authenticated
using (auth.uid() in (select id from auth.users where raw_user_meta_data->>'role' = 'admin'));

create policy "Customers can view own licenses"
on licenses for select
to authenticated
using (customer_id = auth.uid());

-- License logs policies
create policy "Admins have full access to logs"
on license_logs for all
to authenticated
using (auth.uid() in (select id from auth.users where raw_user_meta_data->>'role' = 'admin'));

create policy "Customers can view own license logs"
on license_logs for select
to authenticated
using (license_id in (select id from licenses where customer_id = auth.uid()));

-- Customer profiles policies
create policy "Admins have full access to profiles"
on customer_profiles for all
to authenticated
using (auth.uid() in (select id from auth.users where raw_user_meta_data->>'role' = 'admin'));

create policy "Customers can view own profile"
on customer_profiles for select
to authenticated
using (id = auth.uid());

-- Functions

-- Function to update customer profile updated_at timestamp
create or replace function update_customer_profile_timestamp()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Trigger for customer profile updates
create trigger update_customer_profile_timestamp
  before update on customer_profiles
  for each row
  execute function update_customer_profile_timestamp();

-- Function to log license actions
create or replace function log_license_action()
returns trigger as $$
begin
  insert into license_logs (license_id, action, metadata)
  values (
    new.id,
    case
      when new.status != old.status then 'status_change'
      when new.usage_count > old.usage_count then 'usage'
      else 'update'
    end,
    jsonb_build_object(
      'old_status', old.status,
      'new_status', new.status,
      'old_usage_count', old.usage_count,
      'new_usage_count', new.usage_count
    )
  );
  return new;
end;
$$ language plpgsql;

-- Trigger for license updates
create trigger log_license_action
  after update on licenses
  for each row
  execute function log_license_action(); 