import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../../supabase-client";
import { toast } from "@/components/ui/sonner";
import { ApplicationGroup } from "@/lib/types";

export const useApplicationGroups = () => {
  return useQuery({
    queryKey: ["applicationGroups"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("application_groups")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      return data as ApplicationGroup[];
    },
  });
};

export const useAddVMToGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ groupId, vmId }: { groupId: string; vmId: string }) => {
      const { data: group, error: getError } = await supabase
        .from("application_groups")
        .select("vm_ids")
        .eq("id", groupId)
        .single();

      if (getError) {
        throw new Error(getError.message);
      }

      const vmIds = Array.isArray(group.vm_ids) ? group.vm_ids : [];
      if (!vmIds.includes(vmId)) {
        vmIds.push(vmId);
      }

      const { data, error } = await supabase
        .from("application_groups")
        .update({ vm_ids: vmIds })
        .eq("id", groupId)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data as ApplicationGroup;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applicationGroups"] });
    },
    onError: (error) => {
      toast.error(`Failed to add VM to group: ${error.message}`);
    },
  });
};

export const useRemoveVMFromGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ groupId, vmId }: { groupId: string; vmId: string }) => {
      const { data: group, error: getError } = await supabase
        .from("application_groups")
        .select("vm_ids")
        .eq("id", groupId)
        .single();

      if (getError) {
        throw new Error(getError.message);
      }

      const vmIds = Array.isArray(group.vm_ids) ? group.vm_ids.filter((id) => id !== vmId) : [];

      const { data, error } = await supabase
        .from("application_groups")
        .update({ vm_ids: vmIds })
        .eq("id", groupId)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data as ApplicationGroup;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applicationGroups"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove VM from group: ${error.message}`);
    },
  });
};

export const useAddApplicationGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (appGroup: Omit<ApplicationGroup, "id" | "created_at">) => {
      const { data, error } = await supabase
        .from("application_groups")
        .insert(appGroup)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data as ApplicationGroup;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applicationGroups"] });
      toast.success("Application group added successfully");
    },
    onError: (error) => {
      toast.error(`Failed to add application group: ${error.message}`);
    },
  });
};

export const useUpdateApplicationGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (appGroup: ApplicationGroup) => {
      if (!appGroup.id) {
        throw new Error("Missing required field: id");
      }

      const { data, error } = await supabase
        .from("application_groups")
        .update({
          name: appGroup.name,
          description: appGroup.description,
          data_center_id: appGroup.data_center_id,
        })
        .eq("id", appGroup.id)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data as ApplicationGroup;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applicationGroups"] });
      toast.success("Application group updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update application group: ${error.message}`);
    },
  });
};

export const useDeleteApplicationGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      const { error } = await supabase.from("application_groups").delete().eq("id", groupId);

      if (error) {
        throw new Error(error.message);
      }

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applicationGroups"] });
      toast.success("Application group deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete application group: ${error.message}`);
    },
  });
};
