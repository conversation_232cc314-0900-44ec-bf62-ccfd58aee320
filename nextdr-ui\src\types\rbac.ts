export type UserRole = "admin" | "operator" | "approver" | "viewer";

export interface RolePermissions {
	canManageUsers: boolean;
	canManageGroups: boolean;
	canExecuteRecoveryPlans: boolean;
	canApproveActions: boolean;
	canViewAuditLogs: boolean;
	canExportAuditLogs: boolean;
}

export const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
	admin: {
		canManageUsers: true,
		canManageGroups: true,
		canExecuteRecoveryPlans: true,
		canApproveActions: true,
		canViewAuditLogs: true,
		canExportAuditLogs: true,
	},
	operator: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: true,
		canApproveActions: false,
		canViewAuditLogs: true,
		canExportAuditLogs: true,
	},
	approver: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: false,
		canApproveActions: true,
		canViewAuditLogs: true,
		canExportAuditLogs: false,
	},
	viewer: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: false,
		canApproveActions: false,
		canViewAuditLogs: false,
		canExportAuditLogs: false,
	},
};

export interface UserClaims {
	role: UserRole;
	permissions: RolePermissions;
}
