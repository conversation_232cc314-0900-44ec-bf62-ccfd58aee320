import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

export async function createClient() {
	const cookieStore = await cookies();

	return createServerClient(
		process.env.NEXT_PUBLIC_SUPABASE_URL!,
		process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
		{
			cookies: {
				get(name: string) {
					return cookieStore.get(name)?.value;
				},
				set(name: string, value: string, options: any) {
					cookieStore.set({ name, value, ...options });
				},
				remove(name: string, options: any) {
					cookieStore.delete({ name, ...options });
				},
			},
		}
	);
}

export async function getServerUser(accessToken?: string) {
	const supabase = await createClient();

	let user = null;
	let error = null;

	if (accessToken) {
		// If we have an access token, use it to get the user
		const { data, error: tokenError } = await supabase.auth.getUser(accessToken);
		user = data.user;
		error = tokenError;
	} else {
		// Fallback to session-based auth
		const { data, error: sessionError } = await supabase.auth.getUser();
		user = data.user;
		error = sessionError;
	}

	// Debug logging (remove in production)
	// console.log("Server auth debug:", {
	// 	user: user?.email,
	// 	error: error?.message,
	// 	userMetadata: user?.user_metadata,
	// 	hasToken: !!accessToken,
	// });

	if (error || !user) {
		return null;
	}

	return user;
}

export async function isAdmin(accessToken?: string) {
	const user = await getServerUser(accessToken);
	if (!user) {
		// console.log("isAdmin: No user found");
		return false;
	}

	const userRole = user.user_metadata?.role || "customer";
	// Debug logging (remove in production)
	// console.log("isAdmin check:", {
	// 	email: user.email,
	// 	role: userRole,
	// 	isAdmin: userRole === "admin",
	// });
	return userRole === "admin";
}
