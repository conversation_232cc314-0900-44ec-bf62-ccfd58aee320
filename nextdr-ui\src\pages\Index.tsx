import React from "react";
import { motion } from "framer-motion";
import { Database, DatabaseBackup, FileArchive, RefreshCw } from "lucide-react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from "@/components/ui/card";
import PageLayout from "@/components/layout/PageLayout";
import { Link } from "react-router-dom";
import { useDatacenters } from "@/lib/api/hooks/datacenters";
import { useApplicationGroups } from "@/lib/api/hooks/applicationGroups";
import { useRecoveryPlans } from "@/lib/api/hooks/recoveryPlans";

const Dashboard = () => {
	const { data: datacenters = [], isLoading: isLoadingDatacenters } = useDatacenters();
	const { data: appGroups = [], isLoading: isLoadingAppGroups } = useApplicationGroups();
	const { data: recoveryPlans = [], isLoading: isLoadingRecoveryPlans } = useRecoveryPlans();

	// const connectedDatacenters = datacenters.filter((dc) => dc.status === "connected").length;
	const totalApps = appGroups.length;
	// const readyPlans = recoveryPlans.filter((plan) => plan.status === "ready").length;

	const container = {
		hidden: { opacity: 0 },
		show: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1,
			},
		},
	};

	const item = {
		hidden: { opacity: 0, y: 20 },
		show: { opacity: 1, y: 0, transition: { duration: 0.4 } },
	};

	return (
		<PageLayout title="Dashboard">
			<motion.div
				variants={container}
				initial="hidden"
				animate="show"
				className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
			>
				<motion.div variants={item}>
					<Card className="bg-secondary border-border hover:border-dr-purple transition-colors">
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Datacenters</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{datacenters.length}</div>
							<p className="text-xs text-muted-foreground mt-1">Total datacenters</p>
						</CardContent>
					</Card>
				</motion.div>

				<motion.div variants={item}>
					<Card className="bg-secondary border-border hover:border-dr-purple transition-colors">
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Application Groups</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{totalApps}</div>
							<p className="text-xs text-muted-foreground mt-1">Total Application Groups</p>
						</CardContent>
					</Card>
				</motion.div>

				<motion.div variants={item}>
					<Card className="bg-secondary border-border hover:border-dr-purple transition-colors">
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Recovery Plans</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{recoveryPlans.length}</div>
							<p className="text-xs text-muted-foreground mt-1">Total Recovery Plans</p>
						</CardContent>
					</Card>
				</motion.div>
			</motion.div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.3, duration: 0.5 }}
				>
					<Card className="border-border h-full">
						<CardHeader>
							<div className="flex justify-between items-center">
								<CardTitle className="text-lg">Recovery Plans</CardTitle>
								<Link
									to="/recoveryplan"
									className="text-xs text-dr-purple hover:text-dr-purple-light"
								>
									View All
								</Link>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							{recoveryPlans.slice(0, 3).map((plan) => (
								<div key={plan.id} className="flex items-center p-3 bg-secondary rounded-md">
									<div className="mr-4">
										<div
											className={`p-2 rounded-full ${
												plan.status === "ready"
													? "bg-green-900/20 text-green-500"
													: "bg-amber-900/20 text-amber-500"
											}`}
										>
											<RefreshCw size={16} />
										</div>
									</div>
									<div className="flex-1">
										<div className="font-medium">{plan.name}</div>
										{/* <div className="text-xs text-muted-foreground">{plan.steps_count} steps</div> */}
									</div>
									{/* <div
										className={`text-xs px-2 py-1 rounded-full ${
											plan.status === "Ready"
												? "bg-green-900/20 text-green-500"
												: "bg-amber-900/20 text-amber-500"
										}`}
									>
										{plan.status}
									</div> */}
								</div>
							))}
						</CardContent>
					</Card>
				</motion.div>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.4, duration: 0.5 }}
				>
					<Card className="border-border h-full">
						<CardHeader>
							<div className="flex justify-between items-center">
								<CardTitle className="text-lg">Application Groups</CardTitle>
								<Link
									to="/applications"
									className="text-xs text-dr-purple hover:text-dr-purple-light"
								>
									View All
								</Link>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							{appGroups.slice(0, 3).map((group) => (
								<div key={group.id} className="flex items-center p-3 bg-secondary rounded-md">
									<div className="mr-4">
										<div className="p-2 rounded-full bg-blue-900/20 text-blue-500">
											<FileArchive size={16} />
										</div>
									</div>
									<div className="flex-1">
										<div className="font-medium">{group.name}</div>
										<div className="text-xs text-muted-foreground">{group.vm_ids.length} VMs</div>
									</div>
									<div className="text-xs text-muted-foreground">
										Created {new Date(group.created_at).toLocaleDateString()}
									</div>
								</div>
							))}
						</CardContent>
					</Card>
				</motion.div>
			</div>
		</PageLayout>
	);
};

export default Dashboard;
