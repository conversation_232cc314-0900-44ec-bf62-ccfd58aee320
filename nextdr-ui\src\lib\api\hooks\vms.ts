import { useQuery } from "@tanstack/react-query";
import * as apiClient from "../api-client";

export const useVMsInZone = (
  zone: string,
  projectId: string,
  datacenterId: string,
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: ["vms", zone, projectId, datacenterId],
    queryFn: async () => {
      try {
        return await apiClient.getVMsInZone(zone, projectId, datacenterId);
      } catch (error: any) {
        throw new Error(error.message || "Failed to fetch VMs");
      }
    },
    enabled:
      options?.enabled !== undefined ? options.enabled : !!zone && !!projectId && !!datacenterId,
  });
};

export const useVMConfig = (projectId: string, vmName: string, datacenterId: string) => {
  return useQuery({
    queryKey: ["vmConfig", projectId, vmName, datacenterId],
    queryFn: async () => {
      try {
        return await apiClient.getVMConfig(projectId, vmName, datacenterId);
      } catch (error: any) {
        throw new Error(error.message || "Failed to fetch VM configuration");
      }
    },
    enabled: !!projectId && !!vmName && !!datacenterId,
  });
};
