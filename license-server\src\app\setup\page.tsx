"use client";

import { useState } from "react";
import { useAuth } from "@/components/auth/AuthProvider";

export default function SetupPage() {
	const { user, refreshUser } = useAuth();
	const [loading, setLoading] = useState(false);
	const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);

	const makeCurrentUserAdmin = async () => {
		if (!user) {
			setMessage({ type: "error", text: "You must be logged in to use this setup page" });
			return;
		}

		setLoading(true);
		setMessage(null);

		try {
			// This is a client-side approach - in a real app, you'd want to do this server-side
			// For now, we'll show instructions on how to set up admin role manually
			setMessage({
				type: "success",
				text: `To make ${user.email} an admin, you need to update the user metadata in Supabase. See the instructions below.`,
			});
		} catch (error: any) {
			setMessage({ type: "error", text: error.message });
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full mx-auto space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Admin Setup</h2>
					<p className="mt-2 text-center text-sm text-gray-600">
						Set up the first admin user for your license server
					</p>
				</div>

				{user ? (
					<div className="space-y-6">
						<div className="bg-blue-50 border border-blue-200 rounded-md p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
										<path
											fillRule="evenodd"
											d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<h3 className="text-sm font-medium text-blue-800">Current User</h3>
									<div className="mt-2 text-sm text-blue-700">
										<p>Email: {user.email}</p>
										<p>Current Role: {user.user_metadata?.role || "customer"}</p>
										<details className="mt-2">
											<summary className="cursor-pointer text-xs">Debug: View User Object</summary>
											<pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
												{JSON.stringify(user, null, 2)}
											</pre>
										</details>
									</div>
								</div>
							</div>
						</div>

						{message && (
							<div
								className={`rounded-md p-4 ${
									message.type === "error" ? "bg-red-50 text-red-700" : "bg-green-50 text-green-700"
								}`}
							>
								<p className="text-sm">{message.text}</p>
							</div>
						)}

						<div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
										<path
											fillRule="evenodd"
											d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<h3 className="text-sm font-medium text-yellow-800">Manual Setup Required</h3>
									<div className="mt-2 text-sm text-yellow-700">
										<p>To set up admin access, you need to update the user metadata in Supabase:</p>
										<ol className="mt-2 list-decimal list-inside space-y-1">
											<li>Go to your Supabase dashboard</li>
											<li>Navigate to Authentication → Users</li>
											<li>Find the user: {user.email}</li>
											<li>Click on the user to edit</li>
											<li>In the "Raw User Meta Data" section, add:</li>
										</ol>
										<pre className="mt-2 bg-gray-100 p-2 rounded text-xs">
											{`{
  "role": "admin"
}`}
										</pre>
										<p className="mt-2">
											After saving, refresh this page and try accessing the dashboard.
										</p>
									</div>
								</div>
							</div>
						</div>

						<div className="space-y-4">
							<button
								onClick={async () => {
									setLoading(true);
									await refreshUser();
									setLoading(false);
									setMessage({
										type: "success",
										text: "User data refreshed! Check your current role above.",
									});
								}}
								disabled={loading}
								className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
							>
								{loading ? "Refreshing..." : "Refresh User Data"}
							</button>

							<button
								onClick={makeCurrentUserAdmin}
								disabled={loading}
								className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
							>
								{loading ? "Processing..." : "Show Setup Instructions"}
							</button>

							<a
								href="/admin/dashboard"
								className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
							>
								Go to Dashboard
							</a>
						</div>
					</div>
				) : (
					<div className="text-center">
						<p className="text-gray-600">Please log in first to set up admin access.</p>
						<a
							href="/login"
							className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
						>
							Go to Login
						</a>
					</div>
				)}
			</div>
		</div>
	);
}
