import { Request, Response } from "express";
import { supabase } from "../services/supabaseService";
import { gcpAdSyncService } from "../services/gcpAdSyncService";

export const getIntegrationConfig = async (req: Request, res: Response): Promise<void> => {
	try {
		const { data: config, error } = await supabase
			.from("integration_configs")
			.select("*")
			.eq("source", "gcp_ad")
			.single();

		if (error && error.code !== "PGRST116") {
			throw error;
		}

		if (!config) {
			const defaultConfig = {
				id: null,
				source: "gcp_ad",
				enabled: false,
				last_sync_at: null,
				sync_status: "idle",
				error_message: null,
				config: {
					service_account_key: "",
					domain: "",
					sync_interval_minutes: 15,
				},
				created_at: null,
				updated_at: null,
			};
			res.json(defaultConfig);
			return;
		}

		res.json(config);
	} catch (error: any) {
		console.error("Error fetching integration config:", error);
		res.status(500).json({ error: "Failed to fetch integration configuration" });
	}
};

export const updateIntegrationConfig = async (req: Request, res: Response): Promise<void> => {
	try {
		const { enabled, config } = req.body;

		if (enabled) {
			if (!config?.service_account_key || !config?.domain) {
				res.status(400).json({
					error: "Service Account Key and Domain are required when enabling integration",
				});
				return;
			}

			try {
				const parsedKey = JSON.parse(config.service_account_key);

				const requiredFields = [
					"type",
					"project_id",
					"private_key_id",
					"private_key",
					"client_email",
					"client_id",
				];
				const missingFields = requiredFields.filter((field) => !parsedKey[field]);

				if (missingFields.length > 0) {
					res.status(400).json({
						error: `Invalid service account key: missing fields: ${missingFields.join(", ")}`,
					});
					return;
				}

				if (parsedKey.type !== "service_account") {
					res.status(400).json({
						error: "Invalid service account key: type must be 'service_account'",
					});
					return;
				}
			} catch (error) {
				res.status(400).json({
					error: "Invalid service account key: must be valid JSON",
				});
				return;
			}
		}

		const updateData = {
			source: "gcp_ad",
			enabled: enabled || false,
			config: config || {},
			updated_at: new Date().toISOString(),
		};

		const { data, error } = await supabase
			.from("integration_configs")
			.upsert(updateData, { onConflict: "source" })
			.select()
			.single();

		if (error) {
			throw error;
		}

		await supabase.from("audit_logs").insert({
			user_id: req.user?.id,
			action: "update_integration_config",
			entity_type: "integration_config",
			entity_id: data.id,
			details: {
				source: "gcp_ad",
				enabled: enabled,
				previous_enabled: !enabled,
			},
		});

		res.json(data);
	} catch (error: any) {
		console.error("Error updating integration config:", error);
		res.status(500).json({ error: "Failed to update integration configuration" });
	}
};

export const triggerSync = async (req: Request, res: Response): Promise<void> => {
	try {
		const { data: config, error: configError } = await supabase
			.from("integration_configs")
			.select("*")
			.eq("source", "gcp_ad")
			.single();

		if (configError || !config?.enabled) {
			res.status(400).json({
				error: "GCP AD integration is not configured or enabled",
			});
			return;
		}

		await supabase
			.from("integration_configs")
			.update({
				sync_status: "in_progress",
				error_message: null,
				updated_at: new Date().toISOString(),
			})
			.eq("source", "gcp_ad");

		await supabase.from("audit_logs").insert({
			user_id: req.user?.id,
			action: "trigger_sync",
			entity_type: "integration_config",
			entity_id: config.id,
			details: {
				source: "gcp_ad",
				trigger_type: "manual",
			},
		});

		try {
			const syncResult = await gcpAdSyncService.performSync(config);

			await supabase
				.from("integration_configs")
				.update({
					sync_status: "success",
					last_sync_at: new Date().toISOString(),
					error_message: null,
					updated_at: new Date().toISOString(),
				})
				.eq("source", "gcp_ad");

			res.json({
				message: "Sync completed successfully",
				stats: syncResult,
			});
		} catch (syncError: any) {
			await supabase
				.from("integration_configs")
				.update({
					sync_status: "error",
					error_message: syncError.message,
					updated_at: new Date().toISOString(),
				})
				.eq("source", "gcp_ad");

			throw syncError;
		}
	} catch (error: any) {
		console.error("Error triggering sync:", error);
		res.status(500).json({ error: "Failed to trigger sync: " + error.message });
	}
};

export const getSyncStats = async (req: Request, res: Response): Promise<void> => {
	try {
		const { data: stats, error } = await supabase
			.from("sync_stats")
			.select("*")
			.eq("source", "gcp_ad")
			.order("created_at", { ascending: false })
			.limit(1)
			.single();

		if (error && error.code !== "PGRST116") {
			throw error;
		}

		if (!stats) {
			const defaultStats = {
				source: "gcp_ad",
				total_users: 0,
				total_groups: 0,
				synced_users: 0,
				synced_groups: 0,
				conflicts: 0,
				last_sync_at: null,
			};
			res.json(defaultStats);
			return;
		}

		res.json(stats);
	} catch (error: any) {
		console.error("Error fetching sync stats:", error);
		res.status(500).json({ error: "Failed to fetch sync statistics" });
	}
};
