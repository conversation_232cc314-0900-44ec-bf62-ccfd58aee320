import { BaseStepExecutor, ExecutionContext, ExecutionResult } from './baseStepExecutor';

export class NotificationExecutor extends BaseStepExecutor {
    constructor() {
        super('Notification');
    }

    async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
        this.log(`Sending notification for step: ${step.name}`);
        
        try {
            this.validate(step);
            const config = this.parseConfiguration(step);
            
            // Simulate notification sending
            await this.sleep(500);
            
            return this.createSuccessResult(
                'Notification sent successfully',
                { 
                    recipients: config.recipients || [],
                    message: config.message || 'Recovery step notification'
                }
            );

        } catch (error) {
            return this.createErrorResult(`Notification failed: ${error.message}`, error.message);
        }
    }

    validate(step: any): boolean {
        super.validate(step);
        if (step.operation_type !== 'Notification') {
            throw new Error(`Invalid operation type for notification: ${step.operation_type}`);
        }
        return true;
    }
}
