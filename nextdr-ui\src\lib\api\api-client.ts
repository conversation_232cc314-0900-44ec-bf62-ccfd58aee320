import { NetworkInterfaceResponse, IAMAccountResponse } from "../types";

const API_URL = import.meta.env.VITE_API_SERVER_URL || "http://localhost:8081";

// SSE Event Types
export type RecoveryStepStatus =
	| "PENDING"
	| "IN_PROGRESS"
	| "COMPLETED"
	| "FAILED"
	| "AWAITING_APPROVAL"
	| "APPROVED"
	| "REJECTED";

export type RecoveryPlanStatus =
	| "NOT_STARTED"
	| "IN_PROGRESS"
	| "PAUSED"
	| "RESUMING"
	| "EXECUTING"
	| "RESUME_FAILED"
	| "COMPLETED"
	| "FAILED";

export interface RecoveryStepEvent {
	type: "step_update";
	stepId: string;
	status: RecoveryStepStatus;
	timestamp: string;
	checkpoint_id?: string;
	approver_id?: string;
	approver_role?: string;
	comment?: string;
	approved_by?: string;
	approved_at?: string;
	error?: string;
}

export interface RecoveryPlanEvent {
	type: "plan_update";
	planId: string;
	status: RecoveryPlanStatus;
	timestamp: string;
	checkpoint_id?: string;
	message?: string;
	next_step_id?: string;
	next_step_name?: string;
	error?: string;
}

export interface ConnectionEvent {
	type: "connection";
	status: "connected";
	planId: string;
}

export type RecoveryEvent = RecoveryStepEvent | RecoveryPlanEvent | ConnectionEvent;

async function getAuthToken(): Promise<string | null> {
	try {
		const { supabase } = await import("../supabase-client");
		const { data } = await supabase.auth.getSession();
		return data.session?.access_token || null;
	} catch (error) {
		console.error("Error getting auth token:", error);
		return null;
	}
}

export async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
	const url = `${API_URL}${endpoint}`;

	const defaultHeaders: Record<string, string> = {
		"Content-Type": "application/json",
		Accept: "application/json",
	};

	const token = await getAuthToken();
	if (token) {
		defaultHeaders["Authorization"] = `Bearer ${token}`;
	}

	try {
		const response = await fetch(url, {
			...options,
			credentials: endpoint.includes("/license") ? "include" : "omit",
			mode: "cors",
			headers: {
				...defaultHeaders,
				...((options.headers as Record<string, string>) || {}),
			},
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));

			if (response.status === 401) {
				console.error("Authentication error:", errorData.error);
			}

			throw new Error(errorData.error || `API request failed with status ${response.status}`);
		}

		return response.json();
	} catch (error) {
		if (error instanceof Error) {
			console.error("API request failed:", error.message);
			throw error;
		}
		throw new Error("An unexpected error occurred");
	}
}

export async function getDatacenters() {
	return apiRequest<any[]>("/api/datacenters");
}

interface VMResponse {
	vms: Array<{
		name: string;
		status: string;
		zone: string;
		machineType: string;
		creationTimestamp: string;
		projectId: string;
	}>;
	total: number;
	zone: string;
}

export async function getVMsInZone(zone: string, projectId: string, datacenterId: string) {
	const response = await apiRequest<VMResponse>(
		`/api/gcp/vms?project_id=${projectId}&zone=${zone}&datacenterId=${datacenterId}`
	);

	return response.vms;
}

export async function getVMConfig(projectId: string, vmName: string, datacenterId: string) {
	return apiRequest<any>(
		`/api/vm/config?project_id=${projectId}&vm_name=${vmName}&datacenterId=${datacenterId}`
	);
}

export async function getVMSnapshots(instanceName: string) {
	return apiRequest<any[]>(`/api/gcp/snapshots/${instanceName}`);
}

export async function getSnapshots(instanceName: string, datacenterId: string) {
	return apiRequest<any[]>(`/api/gcp/snapshots/${instanceName}?datacenterId=${datacenterId}`);
}

export async function getNetworkInterfaces(projectId: string, datacenterId: string) {
	const response = await apiRequest<NetworkInterfaceResponse>(
		`/api/gcp/project/get_network_interfaces?projectId=${projectId}&datacenterId=${datacenterId}&zone=us-central1-c`
	);

	return response.networkInterfaces;
}

export async function getIAMAccountsRoles(projectId: string, datacenterId: string) {
	const response = await apiRequest<IAMAccountResponse>(
		`/api/gcp/project/get_iam_accounts_roles?projectId=${projectId}&datacenterId=${datacenterId}`
	);

	return response.iamRoles;
}

export async function getProjectResources(projectId: string, datacenterId: string) {
	return apiRequest<any>(
		`/api/gcp/project/resources?projectId=${projectId}&datacenterId=${datacenterId}`
	);
}

export async function createBackup(
	vmId: string,
	frequency: string,
	retention: string,
	datacenterId: string
) {
	return apiRequest<any>("/api/gcp/backup", {
		method: "POST",
		body: JSON.stringify({ vmId, frequency, retention, datacenterId }),
	});
}

export async function restoreFromSnapshot(instanceName: string, snapshotName: string) {
	return apiRequest<any>("/api/gcp/restore", {
		method: "POST",
		body: JSON.stringify({ instanceName, snapshotName }),
	});
}

/**
 * Execute a recovery plan
 * @param planId The recovery plan ID
 * @param datacenterId The datacenter ID
 * @param resumeExecution Whether to resume an existing execution
 * @returns The execution result
 */
export async function executeRecoveryPlan(
	planId: string,
	datacenterId: string,
	resumeExecution: boolean = false
) {
	console.log(`Executing recovery plan with ID: ${planId}`);
	return apiRequest<any>(`/api/gcp/recovery/execute/${planId}/${datacenterId}`, {
		method: "POST",
		body: JSON.stringify({
			planId,
			resumeExecution,
		}),
	});
}

/**
 * Resume execution of a recovery plan after approval
 * @param planId The recovery plan ID
 * @param checkpointId The checkpoint ID that was approved
 * @param datacenterId Optional datacenter ID (will be retrieved from plan metadata if not provided)
 * @returns The execution result
 */
export async function resumeRecoveryPlanExecution(
	planId: string,
	checkpointId: string,
	datacenterId?: string
) {
	console.log(
		`Resuming recovery plan execution for plan ${planId} after checkpoint ${checkpointId}${
			datacenterId ? `, datacenter ${datacenterId}` : ""
		}`
	);
	return apiRequest<any>(`/api/gcp/recovery/resume/${planId}`, {
		method: "POST",
		body: JSON.stringify({
			checkpointId,
			datacenterId,
		}),
	});
}

/**
 * Get approval details by token
 * @param token The approval token
 * @returns The approval details
 */
export async function getApprovalDetails(token: string) {
	console.log(`Getting approval details for token: ${token}`);

	try {
		// For approval requests, we'll use a more direct approach that doesn't rely on authentication
		// This is important for email-based approvals where the user might not be logged in
		const url = `${API_URL}/api/gcp/recovery/approval/${token}`;

		const response = await fetch(url, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
			},
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			console.error(`Getting approval details failed with status ${response.status}:`, errorData);
			throw new Error(errorData.error || `Failed to get approval details: ${response.status}`);
		}

		const result = await response.json();
		console.log("Approval details retrieved successfully:", result);
		return result;
	} catch (error) {
		console.error("Error getting approval details:", error);
		throw error;
	}
}

/**
 * Process an approval decision
 * @param token The approval token
 * @param decision The approval decision (approved or rejected)
 * @param comment Optional comment
 * @returns The approval result
 */
export async function processApproval(
	token: string,
	decision: "approved" | "rejected",
	comment: string = ""
) {
	console.log(`Processing approval: token=${token}, decision=${decision}`);

	try {
		// For approval requests, we'll use a more direct approach that doesn't rely on authentication
		// This is important for email-based approvals where the user might not be logged in
		const url = `${API_URL}/api/gcp/recovery/approval/${token}`;

		const response = await fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({ decision, comment }),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			console.error(`Approval request failed with status ${response.status}:`, errorData);
			throw new Error(errorData.error || `Approval request failed with status ${response.status}`);
		}

		const result = await response.json();
		console.log("Approval processed successfully:", result);
		return result;
	} catch (error) {
		console.error("Error processing approval:", error);
		throw error;
	}
}

export async function activateLicense(token: string) {
	return apiRequest<any>("/api/license/activate", {
		method: "POST",
		body: JSON.stringify({ token }),
		// credentials: "include" is already set in apiRequest for license endpoints
	});
}

export async function verifyLicense(token: string) {
	return apiRequest<any>(`${process.env.NEXT_PUBLIC_LICENSE_SERVER_URL}/api/license/verify`, {
		method: "POST",
		body: JSON.stringify({ licenseKey: token }),
	});
}

export async function verifyLicenseStatus() {
	return apiRequest<any>(`${process.env.NEXT_PUBLIC_LICENSE_SERVER_URL}/api/license/status`, {
		method: "GET",
		credentials: "include",
	});
}

/**
 * Creates an SSE connection to listen for recovery plan execution events
 * @param planId The ID of the recovery plan to listen for events
 * @param onEvent Callback function to handle incoming events
 * @param onError Callback function to handle errors
 * @returns A function to close the SSE connection
 */
export async function createRecoveryPlanEventSource(
	planId: string,
	onEvent: (event: RecoveryEvent) => void,
	onError?: (error: Event) => void
): Promise<() => void> {
	// Get authentication token
	const token = await getAuthToken();

	// Create URL with authentication token as query parameter for SSE
	// This is a common pattern for authenticating SSE connections
	const url = new URL(`${API_URL}/api/events/${planId}`);
	if (token) {
		url.searchParams.append("auth_token", token);
	}

	const eventSource = new EventSource(url.toString());

	eventSource.onmessage = (event) => {
		try {
			const data = JSON.parse(event.data) as RecoveryEvent;
			onEvent(data);
		} catch (error) {
			console.error("Error parsing SSE event:", error);
		}
	};

	if (onError) {
		eventSource.onerror = onError;
	} else {
		eventSource.onerror = (error) => {
			console.error("SSE connection error:", error);
			// Try to reconnect if the connection is closed
			if (eventSource.readyState === EventSource.CLOSED) {
				console.log("SSE connection closed, attempting to reconnect...");
				// Implement reconnection logic here if needed
			}
		};
	}

	// Return a function to close the connection
	return () => {
		eventSource.close();
	};
}
