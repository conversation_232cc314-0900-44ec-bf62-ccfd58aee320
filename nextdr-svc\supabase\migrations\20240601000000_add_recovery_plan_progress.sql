-- Create recovery_plan_progress table to track execution progress
CREATE TABLE IF NOT EXISTS recovery_plan_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recovery_plan_id UUID NOT NULL REFERENCES recovery_plans_new(id) ON DELETE CASCADE,
    step_id UUID NOT NULL REFERENCES recovery_steps_new(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'awaiting_approval', 'approved', 'rejected')),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    execution_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(recovery_plan_id, step_id)
);

-- Create indexes for faster lookups
CREATE INDEX idx_recovery_plan_progress_plan ON recovery_plan_progress(recovery_plan_id);
CREATE INDEX idx_recovery_plan_progress_step ON recovery_plan_progress(step_id);
CREATE INDEX idx_recovery_plan_progress_status ON recovery_plan_progress(status);

-- Add RLS policies for recovery_plan_progress
ALTER TABLE recovery_plan_progress ENABLE ROW LEVEL SECURITY;

-- Create recovery_plan_checkpoints table to define approval checkpoints
CREATE TABLE IF NOT EXISTS recovery_plan_checkpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recovery_plan_id UUID NOT NULL REFERENCES recovery_plans_new(id) ON DELETE CASCADE,
    step_id UUID NOT NULL REFERENCES recovery_steps_new(id) ON DELETE CASCADE,
    approver_id UUID REFERENCES user_profiles(id),
    approver_role TEXT,
    approval_required BOOLEAN NOT NULL DEFAULT true,
    approval_status TEXT CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    approved_at TIMESTAMPTZ,
    approved_by UUID REFERENCES user_profiles(id),
    approval_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(recovery_plan_id, step_id),
    CHECK (
        (approver_id IS NOT NULL AND approver_role IS NULL) OR
        (approver_id IS NULL AND approver_role IS NOT NULL)
    )
);

-- Create indexes for faster lookups
CREATE INDEX idx_recovery_plan_checkpoints_plan ON recovery_plan_checkpoints(recovery_plan_id);
CREATE INDEX idx_recovery_plan_checkpoints_step ON recovery_plan_checkpoints(step_id);
CREATE INDEX idx_recovery_plan_checkpoints_approver ON recovery_plan_checkpoints(approver_id);
CREATE INDEX idx_recovery_plan_checkpoints_role ON recovery_plan_checkpoints(approver_role);

-- Add RLS policies for recovery_plan_checkpoints
ALTER TABLE recovery_plan_checkpoints ENABLE ROW LEVEL SECURITY;

-- Enhance approval_tokens table
ALTER TABLE approval_tokens
ADD COLUMN is_used BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN used_at TIMESTAMPTZ,
ADD COLUMN used_by UUID REFERENCES user_profiles(id),
ADD COLUMN is_checkpoint BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN checkpoint_id UUID REFERENCES recovery_plan_checkpoints(id) ON DELETE CASCADE;

-- Create index for checkpoint lookups
CREATE INDEX idx_approval_tokens_checkpoint ON approval_tokens(checkpoint_id);

-- Add execution_id to recovery_plans_new to track current execution
ALTER TABLE recovery_plans_new
ADD COLUMN current_execution_id UUID,
ADD COLUMN execution_status TEXT CHECK (execution_status IN ('not_started', 'in_progress', 'paused', 'completed', 'failed')),
ADD COLUMN execution_started_at TIMESTAMPTZ,
ADD COLUMN execution_completed_at TIMESTAMPTZ,
ADD COLUMN execution_metadata JSONB DEFAULT '{}'::jsonb;

-- Create function to update recovery_plan_progress timestamps
CREATE OR REPLACE FUNCTION update_recovery_plan_progress_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    
    IF NEW.status = 'in_progress' AND OLD.status = 'pending' THEN
        NEW.started_at = NOW();
    END IF;
    
    IF NEW.status IN ('completed', 'failed') AND OLD.status = 'in_progress' THEN
        NEW.completed_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for recovery_plan_progress timestamps
CREATE TRIGGER update_recovery_plan_progress_timestamps
BEFORE UPDATE ON recovery_plan_progress
FOR EACH ROW
EXECUTE FUNCTION update_recovery_plan_progress_timestamps();

-- Create function to check if user has approval role
CREATE OR REPLACE FUNCTION has_approval_role(user_id UUID, required_role TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = user_id
        AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is authorized to approve
CREATE OR REPLACE FUNCTION is_authorized_approver(user_id UUID, checkpoint_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    checkpoint_record RECORD;
BEGIN
    -- Get the checkpoint record
    SELECT * INTO checkpoint_record
    FROM recovery_plan_checkpoints
    WHERE id = checkpoint_id;
    
    -- Check if user is the assigned approver
    IF checkpoint_record.approver_id IS NOT NULL THEN
        RETURN checkpoint_record.approver_id = user_id;
    END IF;
    
    -- Check if user has the required role
    IF checkpoint_record.approver_role IS NOT NULL THEN
        RETURN has_approval_role(user_id, checkpoint_record.approver_role);
    END IF;
    
    -- If neither condition is met, user is not authorized
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit logging function for approval actions
CREATE OR REPLACE FUNCTION log_approval_action()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        user_id,
        action,
        entity_type,
        entity_id,
        details,
        ip_address
    ) VALUES (
        NEW.approved_by,
        CASE WHEN NEW.approval_status = 'approved' THEN 'approve_checkpoint' ELSE 'reject_checkpoint' END,
        'recovery_plan_checkpoint',
        NEW.id,
        jsonb_build_object(
            'recovery_plan_id', NEW.recovery_plan_id,
            'step_id', NEW.step_id,
            'previous_status', OLD.approval_status,
            'new_status', NEW.approval_status
        ),
        NEW.approval_metadata->>'ip_address'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for approval audit logging
CREATE TRIGGER log_approval_action
AFTER UPDATE OF approval_status ON recovery_plan_checkpoints
FOR EACH ROW
WHEN (OLD.approval_status IS DISTINCT FROM NEW.approval_status)
EXECUTE FUNCTION log_approval_action();
