import { NextResponse } from "next/server";
import { createClient, isAdmin } from "@/lib/supabase/server";

export async function GET(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const supabase = await createClient();
		const { data: licenses, error } = await supabase.from("licenses").select("*");

		if (error) throw error;

		return NextResponse.json(licenses);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}

export async function POST(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { customerId, features, expiresAt } = await request.json();

		if (!customerId || !features || !expiresAt) {
			return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
		}

		// For now, return a placeholder response since generateLicenseKey is not available
		const license = {
			id: "temp-" + Date.now(),
			license_key: "TEMP-" + Math.random().toString(36).substr(2, 9).toUpperCase(),
			customer_id: customerId,
			features,
			expires_at: expiresAt,
			status: "active",
			created_at: new Date().toISOString(),
		};

		return NextResponse.json(license);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}

export async function DELETE(request: Request) {
	try {
		// Extract access token from Authorization header
		const authHeader = request.headers.get("Authorization");
		const accessToken = authHeader?.replace("Bearer ", "");

		const isUserAdmin = await isAdmin(accessToken);
		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { searchParams } = new URL(request.url);
		const licenseId = searchParams.get("licenseId");

		if (!licenseId) {
			return NextResponse.json({ error: "License ID is required" }, { status: 400 });
		}

		// For now, return success without actually deactivating since deactivateLicense is not available
		// TODO: Implement license deactivation logic

		return NextResponse.json({ success: true });
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
