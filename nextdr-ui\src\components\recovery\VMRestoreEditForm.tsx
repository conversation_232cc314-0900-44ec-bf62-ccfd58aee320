import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";
import { useUpdateRecoveryStep } from "@/lib/api/hooks/recoveryPlans";
import { ChevronDown, Save, Check, Clock, Server, Calendar } from "lucide-react";
import * as apiClient from "@/lib/api/api-client";

// Define a simplified VM type for use in this component
interface VM {
	id: string;
	name: string;
	status?: string;
	zone?: string;
	machineType?: string;
	backuptimes: {
		name: string;
		state: string;
		backupType: string;
		description: string;
		createTime: string;
	}[];
}

interface VMRestoreSelection {
	vm_id: string;
	restore_point_id: string;
	rename_to?: string;
}

interface VMRestoreEditFormProps {
	step: RecoveryStep;
	onSave: () => void;
	onCancel: () => void;
	config: Record<string, any>;
}

const VMRestoreEditForm: React.FC<VMRestoreEditFormProps> = ({
	step,
	onSave,
	onCancel,
	config,
}) => {
	const [vms, setVMs] = useState<VM[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [expandedVMs, setExpandedVMs] = useState<string[]>([]);
	const [selectedRestorePoints, setSelectedRestorePoints] = useState<Record<string, string>>({});
	const [renameValues, setRenameValues] = useState<Record<string, string>>({});
	const [saveInProgress, setSaveInProgress] = useState<boolean>(false);
	const updateStep = useUpdateRecoveryStep();

	useEffect(() => {
		const fetchData = async () => {
			setLoading(true);

			try {
				const savedRestorePoints: Record<string, string> = {};
				const savedRenameValues: Record<string, string> = {};

				if (config.vm_selections && config.vm_selections.length > 0) {
					config.vm_selections.forEach((selection) => {
						if (selection.vm_id && selection.restore_point_id) {
							savedRestorePoints[selection.vm_id] = selection.restore_point_id;
						}
						if (selection.vm_id && selection.rename_to) {
							savedRenameValues[selection.vm_id] = selection.rename_to;
						}
					});
				}

				setSelectedRestorePoints(savedRestorePoints);
				setRenameValues(savedRenameValues);

				if (!config.zone || !config.project_id || !config.datacenter_id) {
					console.error("Missing required configuration for VM fetch");
					setLoading(false);
					return;
				}

				const vmsData = await apiClient.getVMsInZone(
					config.zone,
					config.project_id,
					config.datacenter_id
				);

				if (!vmsData || vmsData.length === 0) {
					setVMs([]);
					setLoading(false);
					return;
				}

				const snapshotsData = [];
				for (const vm of vmsData) {
					const data = await apiClient.getSnapshots(vm.name, config.datacenter_id);
					snapshotsData.push(data);
				}

				const processedVMs = vmsData.map((vm: any, index: number) => {
					const vmSnapshots = snapshotsData[index]?.snapshotNames || [];
					const backuptimes = vmSnapshots.map((snapshotName: string) => {
						const timestampMatch = snapshotName.match(/-(\d+)$/);
						const timestamp = timestampMatch
							? new Date(parseInt(timestampMatch[1])).toISOString()
							: new Date().toISOString();

						return {
							name: snapshotName,
							state: "READY",
							backupType: "SNAPSHOT",
							description: `Snapshot for ${vm.name}`,
							createTime: timestamp,
						};
					});

					return {
						id: vm.name,
						name: vm.name,
						status: vm.status,
						zone: vm.zone,
						machineType: vm.machineType,
						backuptimes: backuptimes,
					};
				});

				setVMs(processedVMs);
			} catch (error) {
				console.error("Error fetching data:", error);
				toast.error("Failed to load virtual machines");
				setVMs([]);
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [step]);

	const toggleExpand = (vmId: string) => {
		setExpandedVMs((prev) =>
			prev.includes(vmId) ? prev.filter((id) => id !== vmId) : [...prev, vmId]
		);
	};

	const handleRestorePointSelect = (vmId: string, restorePointId: string) => {
		console.log("Selecting restore point:", { vmId, restorePointId });
		setSelectedRestorePoints((prev) => ({
			...prev,
			[vmId]: restorePointId,
		}));
	};

	const handleRenameChange = (vmId: string, newName: string) => {
		setRenameValues((prev) => ({
			...prev,
			[vmId]: newName,
		}));
	};

	const formatTimestamp = (timestamp: string) => {
		try {
			const date = new Date(timestamp);
			return date.toLocaleString();
		} catch (error) {
			return timestamp;
		}
	};

	const handleSaveConfiguration = async () => {
		setSaveInProgress(true);

		try {
			const vmSelections: VMRestoreSelection[] = vms
				.map((vm) => ({
					vm_id: vm.id,
					restore_point_id: selectedRestorePoints[vm.id] || "",
					rename_to: renameValues[vm.id] !== vm.name ? renameValues[vm.id] : undefined,
				}))
				.filter((selection) => selection.restore_point_id || selection.rename_to);

			const updatedStep: Partial<RecoveryStep> = {
				...step,
				configuration: {
					...step.configuration,
					type: "Restore virtual machine",
					vm_selections: vmSelections,
					vm_ids: vms.map((vm) => vm.id),
				},
			};

			updateStep.mutate(updatedStep as any, {
				onSuccess: () => {
					toast.success("VM restore configuration saved successfully");
					onSave();
				},
				onError: (error) => {
					toast.error(`Failed to save configuration: ${error.message}`);
				},
			});
		} catch (error: any) {
			console.error("Error saving VM restore configuration:", error);
			toast.error("An unexpected error occurred while saving");
		} finally {
			setSaveInProgress(false);
		}
	};

	return (
		<div className="rounded-lg bg-dr-dark">
			<div className="p-4 border-b border-dr-purple/5">
				<h3 className="text-lg font-semibold text-dr-purple/80 mb-1">
					Edit VM Restore Step: {step.name}
				</h3>
				<p className="text-dr-gray text-sm">
					Select restore points and configure renaming options for each virtual machine.
				</p>
			</div>

			{loading ? (
				<div className="p-6 text-center">
					<div className="flex items-center justify-center space-x-2">
						<div className="w-4 h-4 bg-dr-purple rounded-full animate-pulse"></div>
						<div className="w-4 h-4 bg-dr-purple rounded-full animate-pulse delay-75"></div>
						<div className="w-4 h-4 bg-dr-purple rounded-full animate-pulse delay-150"></div>
						<span className="text-dr-gray ml-2">Loading virtual machines...</span>
					</div>
				</div>
			) : vms.length === 0 ? (
				<div className="p-6 text-center text-dr-gray">
					<p>No virtual machines available for restore.</p>
					<p className="mt-2 text-sm">
						Please make sure the recovery step has the correct project, zone, and datacenter
						configuration.
					</p>
				</div>
			) : (
				<div className="divide-y divide-dr-purple/5">
					{vms.map((vm) => (
						<div key={vm.id} className="p-4">
							<div
								className="flex items-center justify-between cursor-pointer"
								onClick={() => toggleExpand(vm.id)}
							>
								<div className="flex items-center space-x-3">
									<Server className="h-5 w-5 text-dr-purple" />
									<div>
										<h4 className="font-medium text-white">{vm.name}</h4>
										<p className="text-sm text-dr-gray">
											{vm.zone} • {vm.machineType}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-3">
									{selectedRestorePoints[vm.id] && (
										<span className="px-2 py-1 text-xs bg-dr-purple/10 text-dr-purple rounded-full flex items-center">
											<Check className="h-3 w-3 mr-1" />
											Restore Point Selected
										</span>
									)}
									<ChevronDown
										className={`h-5 w-5 text-dr-gray transition-transform duration-200 ${
											expandedVMs.includes(vm.id) ? "rotate-180" : ""
										}`}
									/>
								</div>
							</div>

							{expandedVMs.includes(vm.id) && (
								<div className="p-4 border-t border-dr-purple/5 mt-4 space-y-4">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div className="space-y-2">
											<label className="block text-sm font-medium text-dr-purple/80">
												Rename Virtual Machine
											</label>
											<div className="flex space-x-2">
												<input
													type="text"
													value={renameValues[vm.id] || vm.name}
													onChange={(e) => handleRenameChange(vm.id, e.target.value)}
													placeholder="Enter new name"
													className="flex-1 px-3 py-2 bg-dr-dark text-white rounded-md border border-dr-purple/30 focus:outline-none focus:ring-2 focus:ring-dr-purple focus:border-dr-purple placeholder-dr-gray hover:border-dr-purple/60 transition-colors"
												/>
												{renameValues[vm.id] !== vm.name && (
													<button
														onClick={(e) => {
															e.stopPropagation();
															handleRenameChange(vm.id, vm.name);
														}}
														className="px-3 py-2 bg-dr-dark text-white rounded-md border border-dr-purple/30 hover:bg-dr-purple/5 hover:border-dr-purple/60 transition-colors"
													>
														Reset
													</button>
												)}
											</div>
										</div>

										<div className="space-y-2">
											<label className="block text-sm font-medium text-dr-purple/80">
												Select Restore Point
											</label>
											{vm.backuptimes.length > 0 ? (
												<Select
													onValueChange={(value) => handleRestorePointSelect(vm.id, value)}
													value={selectedRestorePoints[vm.id] || ""}
												>
													<SelectTrigger className="bg-dr-dark border-dr-purple/30 text-white hover:border-dr-purple/60 transition-colors">
														<SelectValue placeholder="Select a restore point" />
													</SelectTrigger>
													<SelectContent className="bg-dr-dark border-dr-purple/5 text-white">
														{vm.backuptimes.map((backup) => (
															<SelectItem key={backup.name} value={backup.name}>
																<div className="flex items-center space-x-2">
																	<Calendar className="h-4 w-4 text-dr-purple" />
																	<span>{formatTimestamp(backup.createTime)}</span>
																</div>
															</SelectItem>
														))}
													</SelectContent>
												</Select>
											) : (
												<div className="p-3 bg-dr-dark rounded-md border border-dr-purple/30 text-dr-gray">
													No restore points available for this VM.
												</div>
											)}
										</div>
									</div>
								</div>
							)}
						</div>
					))}
				</div>
			)}

			<div className="p-4 border-t border-dr-purple/5 flex justify-end space-x-3">
				<Button
					variant="outline"
					onClick={onCancel}
					className="border-dr-purple/5 hover:bg-dr-purple/5"
				>
					Cancel
				</Button>
				<Button
					onClick={handleSaveConfiguration}
					disabled={saveInProgress || vms.length === 0}
					className={`px-4 py-2 rounded-md flex items-center justify-center space-x-2 ${
						saveInProgress || vms.length === 0
							? "bg-dr-purple/20 text-dr-gray cursor-not-allowed"
							: "bg-dr-purple text-white hover:bg-dr-purple/90"
					}`}
				>
					{saveInProgress ? (
						<>
							<div className="h-4 w-4 border-2 border-dr-gray border-t-transparent rounded-full animate-spin"></div>
							<span>Saving...</span>
						</>
					) : (
						<>
							<Save className="h-5 w-5" />
							<span>Save Configuration</span>
						</>
					)}
				</Button>
			</div>
		</div>
	);
};

export default VMRestoreEditForm;
