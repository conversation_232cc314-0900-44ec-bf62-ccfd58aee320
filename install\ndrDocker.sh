#!/bin/bash
# ndrDocker.sh - A Bash module providing common Docker related functions

source "$(dirname "${BASH_SOURCE[0]}")/ndrCommon.sh"

# --- FUNCTIONS ---

function ndr_checkAndInstallDocker ()
{
  # -------------------------------------
  # cleanup old docker packages

  local logSectionDesc="Conflicting Docker package removal"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"
  
  # -------------------------------------
  # 🚮 Remove any stale Docker APT source files

  local logSectionDesc="🧹 Cleaning up old Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Remove docker.list if it exists
  docker_list="/etc/apt/sources.list.d/docker.list"
  if [[ -f "$docker_list" ]]; then
    sudo rm -f "$docker_list"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "file $docker_list not removed, please manually remove file before proceeding."
      return 1
    fi
    ndr_logInfo "🗑️ Removed stale Docker source file."
  fi

  # Optional: grep and warn about any other Docker-related entries
  if grep -r "download.docker.com" /etc/apt/sources.list.d/ > /dev/null; then
    ndr_logWarn "Found other Docker source entries in [/etc/apt/sources.list.d/]."
  fi

  if grep -q "download.docker.com" /etc/apt/sources.list; then
    ndr_logWarn "Found Docker source entry in [/etc/apt/sources.list]  You may want to clean that manually.033[0m"
  fi

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # Add Docker's official GPG key:

  local logSectionDesc="Adding Official Docker GPG Key"
  ndr_logSecStart "$logSectionDesc"

  sudo apt-get update
  pkgs=(
    ca-certificates
    curl
  )
  # Loop through each package and install it
  for pkg in "${pkgs[@]}"; do
    ndr_logInfo "💾 Installing $pkg..."
    sudo apt-get install --assume-yes "$pkg"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "$pkg package not installed, please manually install package before proceeding."
      return 1
    fi
  done

  local cmds=(
    "sudo install -m 0755 -d /etc/apt/keyrings" \
    "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
    "sudo chmod a+r /etc/apt/keyrings/docker.asc"
    )
  
  for cmd in "${cmds[@]}"; do
    ndr_logInfo "💾 Executing command [$cmd]..."
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Command [$cmd] failure, please run manually before proceeding."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # add deb line to apt sources list

  local logSectionDesc="Adding Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"
  
  # single line execution
  #echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list

  # multi line execution
  # Step 1: Get system architecture
  arch=$(dpkg --print-architecture)

  # Step 2: Get Ubuntu codename (e.g., focal, jammy)
  . /etc/os-release
  codename="${UBUNTU_CODENAME:-$VERSION_CODENAME}"

  # Step 3: Construct the deb line
  deb_line="deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable"

  # Step 4: Write to APT sources list with sudo 
  apt_source_file="/etc/apt/sources.list.d/docker.list"
  echo "$deb_line" | sudo tee "$apt_source_file" > /dev/null
  
  ndr_logInfo "Adding deb line [$deb_line] to list file [$apt_source_file]."

  sudo apt-get update

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # verify apt sources

  local logSectionDesc="Verifying Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Step 1: Verify the deb line exists
  if [[ ! -f "$apt_source_file" ]]; then
    ndr_logError "APT source file not found: [$apt_source_file]."
    return 1
  fi

  if grep -Fxq "$deb_line" "$apt_source_file"; then
    ndr_logInfo "Docker APT source entry is present."
  else
    ndr_logError "Docker APT source entry is missing or incorrect."
    return 1
  fi

  # Step 2: Update APT
  ndr_logInfo "🔄 Running apt-get update..."
  sudo apt-get update -qq
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "apt-get update failed."
    return 1
  fi

  # Step 3: Check for Docker packages
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Checking if Docker package [$pkg] is available..."
    repo_line=$(apt-cache policy "$pkg" 2>/dev/null | grep "https://download.docker.com/linux/ubuntu $codename")
    return_code=$?
    if [[ $return_code -eq 0 && -n "$repo_line" ]]; then
      ndr_logInfo "✅ Docker package [$pkg] is available from Docker's APT repository."
    else
      ndr_logError "Docker package [$pkg] not found in the expected repository."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # install docker packages
  
  local logSectionDesc="Installing Docker packages"
  ndr_logSecStart "$logSectionDesc"

  #sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Installing Docker engine package [$pkg]..."
    cmd="sudo apt-get install --assume-yes $pkg"
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to install Docker package [$pkg]."
      return 1
    else
      ndr_logInfo "✅ Docker package [$pkg] installed successfully."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_checkAndInstallPrerequisites () 
{
  local logSectionDesc="Checking prerequisites"
  ndr_logSecStart "$logSectionDesc"

  if [[ $gPrereqCheckComplete -eq 1 ]]; then
    ndr_logInfo "Prerequesite check complete, skipping."
    return 0
  fi

  local checkOnlyFlag=false
  if [[ "$1" == "checkOnly" ]]; then
    checkOnlyFlag=true
  fi

  local prereqPackages=("yq" \
                        #"gawk" \
                        "npm" \
                        "npx" \
                        "git" \
                        "docker")

  # assume all packages are installed by default, then check one by one.
  local packageInstalled=true
  for pkg in "${prereqPackages[@]}"; do
    if ! command -v "$pkg" &> /dev/null; then
      ndr_logWarn "$pkg is not installed."
      # indicate that the package is not installed
      packageInstalled=false

      if [[ "$checkOnlyFlag" == "true" ]]; then
        continue
      fi

      
      read -p "Would you like to install $pkg now? [Y/n] " reply
      if [[ -z "$reply" || "$reply" =~ ^[Yy] ]]; then
        ndr_logInfo "Installing [$pkg] package..."
        case "$pkg" in
           yq)
            packageURL="https://github.com/mikefarah/yq"

            cmd="wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/local/bin/yq"
            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            sudo chmod +x /usr/local/bin/yq

            ndr_logInfo "$pkg package installed successfully."
            ;;

           awk|gawk)
            packageURL="https://www.gnu.org/software/gawk/manual/gawk.html"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt-get install --assume-yes gawk"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install gawk"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm gawk"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install gawk"
            fi
            
            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          npm|npx)
            packageURL="https://docs.npmjs.com/downloading-and-installing-node-js-and-npm"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt install --assume-yes npm"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install nodejs"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm nodejs"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install node"
            fi
            
            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          git)
            packageURL="https://git-scm.com/downloads"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt install --assume-yes git"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install git"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm git"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install git"
            fi

            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          docker)
            packageURL="https://docs.docker.com/engine/install/"
            if [[ "$distroFamily" == "debian" ]]; then
              local installCmds=(
                ndr_checkAndInstallDocker
              )
            elif [[ "$distroFamily" == "debianOLD" ]]; then
              local installCmds=(
                    "sudo apt-get install --assume-yes gnome-terminal"
                    "sudo apt-get update" \
                    "sudo apt-get install --assume-yes ca-certificates" \
                    "sudo apt-get install --assume-yes curl" \
                    "sudo install -m 0755 -d /etc/apt/keyrings" \
                    "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
                    "sudo chmod a+r /etc/apt/keyrings/docker.asc" \
                    #"echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list"
                    # Step 1: Get system architecture
                    "arch=$(dpkg --print-architecture)" \
                    # Step 2: Get Ubuntu codename (e.g., focal, jammy)
                    ". /etc/os-release" \
                    "codename=\"${UBUNTU_CODENAME:-$VERSION_CODENAME}\"" \
                    # Step 3: Construct the deb line
                    "deb_line=\"deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable\"" \
                    # Step 4: Write to APT sources list with sudo
                    "echo \"$deb_line\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null" \
                    "sudo apt-get update" \
                    "sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin")
            elif [[ "$distroFamily" == "redhat" ]]; then
              local installCmds=(
                    "sudo dnf install gnome-terminal" \
                    "sudo dnf -y install dnf-plugins-core" \
                    "sudo dnf-3 config-manager --add-repo https://download.docker.com/linux/fedora/docker-ce.repo" \
                    "sudo dnf install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin" \
                    "sudo systemctl enable --now docker")
            elif [[ "$distroFamily" == "suse" ]]; then
              local installCmds=(
                    "sudo zypper addrepo https://download.opensuse.org/repositories/Virtualization:containers/openSUSE_Tumbleweed_and_d_l_g/Virtualization:containers.repo" \
                    "sudo zypper addrepo https://download.docker.com/linux/sles/docker-ce.repo" \
                    "sudo zypper refresh" \
                    "sudo zypper install docker-compose" \
                    "sudo systemctl enable docker.service" \
                    "sudo usermod -aG docker $USER" \
                    "sudo systemctl start docker.service" \
                    "sudo zypper install docker")
            fi

            for installCmd in "${installCmds[@]}"; do
            ndr_logInfo "Executing installation command [$installCmd]..."
              $installCmd
              return_code=$?
              if [ $return_code != 0 ]; then
                ndr_logError "[$installCmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
                # skip apt-get command failures
                if [[ "$installCmd" == *"apt-get update"* || "$installCmd" == *addrepo* ]]; then
                  continue
                fi
                return 1
              fi
            done
            
            getent group docker || sudo groupadd docker
            return_code=$?
            if [ $return_code -ne 0 ]; then
              ndr_logError "Failed to create docker group. Please check your system configuration."
              return 1
            fi
            ndr_logInfo "Created docker group if it did not exist."

            sudo usermod -aG docker "$USER"
            return_code=$?
            if [ $return_code -ne 0 ]; then
              ndr_logError "Failed to add user $USER to docker group. Please check your system configuration."
              return 1
            fi
            ndr_logInfo "User $USER added to docker group. You may need to log out and back in for this change to take effect."

            if systemctl is-active --quiet docker; then
              ndr_logInfo "Docker is running."
            else
              # Optional: try to start it
              ndr_logInfo "Docker is NOT running, attempting to start Docker..."
              
              sudo systemctl start docker

              # Check again after attempt
              if systemctl is-active --quiet docker; then
                  ndr "Docker started successfully."
              else
                  ndr_logError "Failed to start Docker. Check logs with: journalctl -u docker. A reboot may be required to complete the installation."
                  return 1
              fi
            fi
            
            ndr_logInfo "$pkg package installed successfully."            
            ;;
          *)
            ndr_logWarn "Unknown package [$pkg] "
            continue
            ;;
        esac

        # indicate that the package is now successfully installed
        packageInstalled=true  
      else
        ndr_logWarn "Skipping installation of $pkg."
      fi
    else
      ndr_logInfo "✅ $pkg is installed."
      # indicate that the package was already installed
      packageInstalled=true
    fi
  done

  # the prereq check complete flag needs to be qualfied by all packages installed before setting
  if [ $packageInstalled == true ]; then
    ndr_logInfo "All required packages are installed."
    gPrereqCheckComplete=1
  else
    ndr_logError "Some required packages are not installed. Please install them before proceeding."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_removeDockerPackages ()
{
  local logSectionDesc="Cleaning up packages"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  # Loop through each package and remove it
  for pkg in "${dockerInstallPackages[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  sudo apt-get autoremove --assume-yes
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to autoremove unused packages."
    #return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# returns 2 for usage or general error
# returns 1 for image not found
# returns 0 for image found
# usage: function <image:tag>
function ndr_verifyDockerImageExists () 
{
  local logSectionDesc="Verifying Docker Image Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  local image_name="$1"
  if [[ -z "$image_name" ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  # Run command and capture both output and return status
  while true; do
    
    local output
    #output=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -Fx "$image_name")
    output=$(docker images --format "{{.Repository}}:{{.Tag}}")
    return_code=$?

    # Check if docker failed
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to query for Docker images."
      retVal=2
      break
    fi

    # check if output is empty (no images at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker images found."
      retVal=1
      break
    fi

    #if [[ -n "$output" ]]; then
    #  ndr_logInfo "✅ Docker image '${image_name}' exists."
    #  retVal=0
    #else
    #  ndr_logWarn "No matching image output returned for '${image_name}'."
    #  retVal=1
    #fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$image_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker image '${image_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching image output returned for '${image_name}'."
      retVal=1
    fi
    
    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# returns 2 for usage or general error
# returns 1 for container not found
# returns 0 for container found
# usage: function <container_name>
function ndr_verifyDockerContainerExists () 
{
  local logSectionDesc="Verifying Docker Container Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  local container_name="$1"
  if [[ -z "$container_name" ]]; then
    echo "❌ Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  # Capture the output and return code separately
  while true; do
    
    local output
    #output=$(docker ps -a --format '{{.Names}}' | grep -Fx "$container_name")
    output=$(docker ps -a --format '{{.Names}}')
    local status=$?

    # Check if docker failed
    if [[ $status -ne 0 ]]; then
      ndr_logError "Failed to query for Docker containers."
      retVal=2
      break
    fi

    # check if output is empty (no containers at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker containers found."
      retVal=1
      break
    fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$container_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker container '${container_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching container output returned for '${container_name}'."
      retVal=1
    fi

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# usage: function <image_name> [build_mode_options]
function ndr_cleanupDockerImage() 
{
  local logSectionDesc="Cleaning up Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do

    local image_name="$1"
    if [[ -z "$image_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerImage <image_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker image '${image_name}' not found, no cleanup needed."
      retVal=0
      break
    fi
    
    # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker image '${image_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker image removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🧹 Forcing removal of image '${image_name}'..."
    docker image rm -f "$image_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove image '${image_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker image '${image_name}' removal command succeeded."

    docker buildx prune -f
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logWarn "Failed to prune dangling docker build cache entries."
    fi
    ndr_logInfo "Successfully pruned dangling docker build cache entries."

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      # image still exists after removal attempt
      ndr_logError "Error, image still present after removal '${image_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Image '${image_name}' successfully removed and verified."

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function  <image_name> [build_mode_options]
function ndr_cleanupDockerContainer ()
{
  local logSectionDesc="Cleaning up Docker Container"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do
    local container_name="$1"
    
    if [[ -z "$container_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerContainer <container_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker container '${container_name}' not found, nothing to remove."
      retVal=0
      break
    fi

     # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker container '${container_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker container removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🛑 Stopping container '${container_name}'..."

    docker stop "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logWarn "Failed to stop Docker container '${container_name}'."
      #retVal=1
      #break
    fi

    ndr_logInfo "🧹 Removing container '${container_name}'..."
    
    docker container rm -f "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove Docker container '${container_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker container removal command succeeded."

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      ndr_logError "Error, container still present after removal '${container_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Container '${container_name}' successfully removed and verified."

    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> [build_mode_options]
function ndr_cleanupDockerApplication ()
{
  local logSectionDesc="Cleanup Docker Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName="$4"
  #local dockerContainerURL="http://localhost:$dockerContainerPort"

  local dockerAppManageOptions="${5:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: ndr_cleanupDockerApplication <folder> <image_name> <image_version> <container_name>"
    return 1
  fi

  # move from the install directory to the container/module directory
  cd "$gSCRIPT_HOME_DIR/../$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }

  # check if the Dockerfile exists
  if [[ ! -f Dockerfile ]]; then
    ndr_logWarn "Dockerfile not found in $containerFolder directory."
    #return 1
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )); then
    # remove any existing Docker container with the same name
    ndr_cleanupDockerContainer "$dockerContainerName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker container."
      return 1
    fi
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE )); then
    # remove any existing image with the same name
    ndr_cleanupDockerImage "$dockerImageName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker image."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> <container_port> [build_mode_options]
function ndr_buildDockerApplicationContainer ()
{
  local logSectionDesc="Building Docker Application Container"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName=$4
  local dockerContainerPort=$5
  local dockerContainerURL="http://localhost:$dockerContainerPort"

  local dockerAppManageOptions="${6:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 5 ]]; then
    ndr_logError "Usage: buildDockerApplication <folder> <image_name> <image_version> <container_name> <container_port>"
    return 1
  fi

  # move from the install directory to the container/module directory
  cd "$gSCRIPT_HOME_DIR/../$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }
  
  # check if the Dockerfile exists
  if [[ ! -f Dockerfile ]]; then
    ndr_logError "Dockerfile not found in $containerFolder directory."
    return 1
  fi

  dockerAppManageOptions=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT ))
  # remove any existing Docker container with the same name
  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application container."
    return 1
  fi

  # create the bridge network if it does not exist
  ndr_createDockerBridgeNetwork
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to create Docker bridge network."
    return 1
  fi
  ndr_logInfo "Docker bridge network created."

  # run the Docker container
  docker run -d --name "$dockerContainerName" --network "$NDR_DOCKER_BRIDGE_NETWORK_NAME" -p "$dockerContainerPort:$dockerContainerPort" "$dockerImageName"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to run Docker container [$dockerContainerName]."
    return 1
  fi

  # check if the newly built Docker container exists
  ndr_verifyDockerContainerExists "$dockerContainerName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker container verification failed."
    return 1
  fi
  # check if the container is running
  docker ps -a | grep "$dockerContainerName" | grep "Up"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is not running."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running."

  # check if the container is running on specified container port
  docker ps -a | grep "$dockerContainerName" | grep "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is NOT running on port $dockerContainerPort."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running on port $dockerContainerPort."
  
  # execute curl command to check if the container is running
  curl -s -o /dev/null -w "%{http_code}" "$dockerContainerURL"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to access the container at $dockerContainerURL"
    return 1
  fi
  ndr_logInfo "Successfully accessed the container at $dockerContainerURL"
  
  ndr_logInfo "Docker image [$dockerImageName] is built and ready to use."
  ndr_logInfo "Docker container [$dockerContainerName] is running and accessible."
  
  ndr_logInfo "You can access the container at $dockerContainerURL"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_scrubAllDockerResources ()
{
  local logSectionDesc="Force Removing All Local Docker Resources"
  ndr_logSecStart "$logSectionDesc"  

  ndr_logInfo "ALL existing local Docker resources will be removed, both active and inactive. Warning, this action cannot be undone!"
  read -p "Are you sure you want to proceed? [y|N] " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    ndr_logInfo "Proceeding with all local Docker resource removal."
  else
    ndr_logInfo "Operation cancelled, returning to main menu."
    return 0
  fi

  # docker cleanup
  # https://stackoverflow.com/questions/45357771/stop-and-remove-all-docker-containers

  #Stop all the containers
  docker stop $(docker ps -a -q)

  #Remove all the containers
  docker rm -f $(docker ps -a -q)

  #Deleting no longer needed containers (stopped)
  docker container prune -f

  #Deleting no longer needed images which means, that it only deletes images, which are not tagged and are not pointed on by "latest" - so no real images you can regularly use are deleted
  docker image prune -a -f

  #Delete all volumes, which are not used by any existing container ( even stopped containers do claim volumes ). 
  # This usually cleans up dangling anon-volumes of containers have been deleted long time ago. 
  # It should never delete named volumes since the containers of those should exists / be running. 
  # Be careful, ensure your stack at least is running before going with this one
  docker volume prune -f

  #Same for unused networks
  docker network prune -f

  #And finally, if you want to get rid if all the trash - to ensure nothing happens to your production, be sure all stacks are running and then run
  docker system prune -f

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Function to parse details of a given service
function ndr_ParseComposeFileServiceDetails () 
{
  if [[ $# -ne 2 ]]; then
    ndr_logWarn "Usage: ndr_ParseComposeFileServiceDetails <compose_file_path> <service_name>"
    return 1
  fi

  local compose_file=$1
  local service=$2

  # Extract container_name, image full string (name:tag)
  local container_name image_full image_name image_tag

  container_name=$(yq eval ".services.\"$service\".container_name // \"\"" "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse container_name for service '$service'"
    return 1
  fi

  image_full=$(yq eval ".services.\"$service\".image // \"\"" "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse image for service '$service'" >&2
    return 1
  fi

  # If no image defined, leave blank
  if [[ -z "$image_full" ]]; then
    image_name=""
    image_tag=""
  else
    # Split image_full into name and tag by ':'
    if [[ "$image_full" == *:* ]]; then
      image_name="${image_full%%:*}"
      image_tag="${image_full##*:}"
    else
      image_name="$image_full"
      image_tag="latest"
    fi
  fi

  # Save to global associative arrays keyed by service name
  ndr_supabase_container_service_container_names["$service"]="$container_name"
  ndr_supabase_container_service_image_names["$service"]="$image_name"
  ndr_supabase_container_service_image_tags["$service"]="$image_tag"

  return 0
}

#declare -a ndr_supabase_container_services=()
#declare -A ndr_supabase_container_service_container_names=()
#declare -A ndr_supabase_container_service_image_names=()
#declare -A ndr_supabase_container_service_image_tags=()
# Function to parse top-level services and call detail parser
function ndr_ParseComposeFileServices () 
{
  local logSectionDesc="Parsing Compose File Services"
  ndr_logSecStart "$logSectionDesc"  

  if [[ $# -ne 1 ]]; then
    ndr_logWarn "Usage: ndr_ParseComposeFileServiceDetails <compose_file_path>"
    return 1
  fi

  local compose_file=$1
  if [[ ! -f "$compose_file" ]]; then
    ndr_logError "Compose file not found: $compose_file"
    return 1
  fi

  # Clear previous contents
  ndr_supabase_container_services=()
  ndr_supabase_container_service_container_names=()
  ndr_supabase_container_service_image_names=()
  ndr_supabase_container_service_image_tags=()

  # Extract service names using yq (expects yq v4+)
  mapfile -t ndr_supabase_container_services < <(yq eval '.services | keys | .[]' "$compose_file")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse services from $compose_file"
    return 1
  fi

  ndr_logInfo "Found ${#ndr_supabase_container_services[@]} services in the compose file '$compose_file'."

  # For each service, call details parser
  for svc in "${ndr_supabase_container_services[@]}"; do
    ndr_ParseComposeFileServiceDetails "$compose_file" "$svc"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to parse service details for '$svc' in $compose_file"
      return 1
    fi
  done

  if  [[ ${#ndr_supabase_container_services[@]} -ne 0 ]]; then  
    echo "-----------------------------------------------------------------------------------------"
    echo "Parsed Docker Compose Services in [$compose_file]:"
    echo "-----------------------------------------------------------------------------------------"

    for svc in "${ndr_supabase_container_services[@]}"; do
      # Output
      echo "Service: $svc"
      echo "  Container Name: ${ndr_supabase_container_service_container_names[$svc]}"
      echo "  Image Name    : ${ndr_supabase_container_service_image_names[$svc]}"
      echo "  Image Tag     : ${ndr_supabase_container_service_image_tags[$svc]}"
      echo
    done

    echo "-----------------------------------------------------------------------------------------"
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupServiceApplication ()
{
  local logSectionDesc="Cleanup Service Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_SERVICE_HOME_LOC
  local dockerImageName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerContainerName=$NDR_SERVICE_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupUIApplication ()
{
  local logSectionDesc="Cleanup UI Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_UI_HOME_LOC
  local dockerImageName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerContainerName=$NDR_UI_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_createDockerBridgeNetwork ()
{
  local logSectionDesc="Create Docker Bridge Network"
  ndr_logSecStart "$logSectionDesc"

  if ! docker network ls --format '{{.Name}}' | grep -q "^${NDR_DOCKER_BRIDGE_NETWORK_NAME}$"; then
    ndr_logInfo "Creating Docker network: ${NDR_DOCKER_BRIDGE_NETWORK_NAME}"
    docker network create "${NDR_DOCKER_BRIDGE_NETWORK_NAME}"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to create Docker network '${NDR_DOCKER_BRIDGE_NETWORK_NAME}'. Please check your Docker installation and permissions."
      return 1
    fi
  else
    ndr_logInfo "Docker network '${NDR_DOCKER_BRIDGE_NETWORK_NAME}' already exists"
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
