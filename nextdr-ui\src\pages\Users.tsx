import React, { useState } from "react";
import { motion } from "framer-motion";
import { User, UserPlus, Shield, Mail, Calendar, Trash2, History, RefreshCw } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import PageLayout from "@/components/layout/PageLayout";
import { useAuth } from "@/lib/context/AuthContext";
import { format } from "date-fns";
import InviteUserModal from "@/components/modals/InviteUserModal";
import { useUserProfiles } from "@/hooks/useUserProfiles";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Users as UsersIcon } from "lucide-react";
import { AuditLogs } from "@/components/audit/AuditLogs";
import { useInternalGroups } from "@/lib/api/hooks/internalGroups";
import { useTriggerSync } from "@/lib/api/hooks/integrations";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { UserRole } from "@/types/rbac";
import { IntegrationSource, UserProfile } from "@/types/integrations";

const UserManagement = () => {
	const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
	const { profiles, isLoading, inviteUser, removeUser, suspendUser, activateUser, updateUserRole } =
		useUserProfiles();
	const { isAdmin, isLoading: isLoadingAdmin } = useIsAdmin();
	const { data: groups } = useInternalGroups();
	const triggerSyncMutation = useTriggerSync();

	const roles: UserRole[] = ["admin", "operator", "approver", "viewer"];

	if (isLoadingAdmin) {
		return (
			<PageLayout title="Users">
				<div className="space-y-4">
					<Skeleton className="h-8 w-full" />
					<Skeleton className="h-8 w-full" />
					<Skeleton className="h-8 w-full" />
				</div>
			</PageLayout>
		);
	}

	if (!isAdmin) {
		return (
			<PageLayout title="Users">
				<div className="flex h-[50vh] items-center justify-center">
					<p className="text-muted-foreground">Access denied. Admin privileges required.</p>
				</div>
			</PageLayout>
		);
	}

	return (
		<PageLayout title="Users">
			<div className="space-y-6">
				<div className="flex justify-between items-center">
					<div className="flex items-center gap-3">
						<User className="h-6 w-6 text-blue-500" />
						<h2 className="text-xl font-semibold">User Management</h2>
						<Badge variant="secondary" className="ml-2">
							{profiles?.length} {profiles?.length === 1 ? "user" : "users"}
						</Badge>
					</div>
					<div className="flex gap-2">
						<Button
							variant="outline"
							onClick={() => triggerSyncMutation.mutate()}
							disabled={triggerSyncMutation.isPending}
						>
							<RefreshCw className="h-4 w-4 mr-2" />
							{triggerSyncMutation.isPending ? "Syncing..." : "Sync Users"}
						</Button>
						<Button onClick={() => setIsInviteModalOpen(true)}>
							<UserPlus className="h-4 w-4 mr-2" />
							Invite User
						</Button>
					</div>
				</div>

				<Tabs defaultValue="users" className="space-y-4">
					<TabsList>
						<TabsTrigger value="users" className="flex items-center gap-2">
							<UsersIcon className="h-4 w-4" />
							Users
						</TabsTrigger>
						<TabsTrigger value="audit" className="flex items-center gap-2">
							<History className="h-4 w-4" />
							Audit Logs
						</TabsTrigger>
					</TabsList>

					<TabsContent value="users">
						{isLoading ? (
							<div className="space-y-4">
								<Skeleton className="h-20 w-full" />
								<Skeleton className="h-20 w-full" />
								<Skeleton className="h-20 w-full" />
							</div>
						) : (
							<div className="rounded-md border overflow-x-auto">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Email</TableHead>
											<TableHead>Role</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Source</TableHead>
											<TableHead>Created</TableHead>
											<TableHead>Last Sign In</TableHead>
											<TableHead>Groups</TableHead>
											<TableHead>Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{profiles?.map((profile) => (
											<TableRow key={profile.id}>
												<TableCell className="font-medium truncate max-w-[200px]">
													{profile.email}
												</TableCell>
												<TableCell>
													<select
														className={`rounded px-2 py-1 border border-[#333] focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[#18181b] text-white transition-colors duration-150 ${
															updateUserRole.isPending ? "opacity-50" : ""
														}`}
														value={profile.role}
														disabled={updateUserRole.isPending || profile.source === "gcp_ad"}
														onChange={(e) => {
															const newRole = e.target.value as UserRole;
															if (newRole !== profile.role) {
																updateUserRole.mutate({ userId: profile.id, role: newRole });
															}
														}}
													>
														{roles.map((role) => (
															<option key={role} value={role} className="bg-[#18181b] text-white">
																{role.charAt(0).toUpperCase() + role.slice(1)}
															</option>
														))}
													</select>
												</TableCell>
												<TableCell>
													<Badge variant={profile.status === "active" ? "default" : "secondary"}>
														{profile.status}
													</Badge>
												</TableCell>
												<TableCell>
													<Badge variant={profile.source === "gcp_ad" ? "default" : "outline"}>
														{profile.source === "gcp_ad" ? "GCP AD" : "Native"}
													</Badge>
													{profile.sync_metadata && (
														<Badge
															variant={
																profile.sync_metadata.sync_status === "synced"
																	? "default"
																	: profile.sync_metadata.sync_status === "conflict"
																	? "secondary"
																	: "destructive"
															}
															className="ml-2"
														>
															{profile.sync_metadata.sync_status}
														</Badge>
													)}
												</TableCell>
												<TableCell>{format(new Date(profile.created_at), "MMM d, yyyy")}</TableCell>
												<TableCell>
													{profile.last_sign_in_at
														? format(new Date(profile.last_sign_in_at), "MMM d, yyyy")
														: "-"}
												</TableCell>
												<TableCell>
													{profile.groups && profile.groups.length > 0 ? (
														<div className="flex flex-wrap gap-1">
															{profile.groups.map((group) => (
																<Badge
																	key={group.id}
																	variant="outline"
																	className="truncate max-w-[120px]"
																>
																	{group.name}
																</Badge>
															))}
														</div>
													) : (
														"-"
													)}
												</TableCell>
												<TableCell>
													<div className="flex flex-wrap gap-2">
														{profile.source === "native" && (
															<>
																{profile.status === "active" ? (
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={() => suspendUser.mutate(profile.id)}
																	>
																		Suspend
																	</Button>
																) : (
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={() => activateUser.mutate(profile.id)}
																	>
																		Activate
																	</Button>
																)}
																<Button
																	variant="destructive"
																	size="sm"
																	onClick={() => removeUser.mutate(profile.id)}
																>
																	Remove
																</Button>
															</>
														)}
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						)}
					</TabsContent>

					<TabsContent value="audit">
						<AuditLogs />
					</TabsContent>
				</Tabs>
			</div>

			<InviteUserModal
				isOpen={isInviteModalOpen}
				onClose={() => setIsInviteModalOpen(false)}
				onInvite={async (email, role) => {
					await inviteUser.mutateAsync({ email, role: role as UserRole });
				}}
			/>
		</PageLayout>
	);
};

export default UserManagement;
