// routes/sse.ts
import express from "express";
import { registerClient } from "./sseManager";
import { supabase } from "../../services/supabaseService";

const router = express.Router();

/**
 * Middleware to authenticate SSE connections
 * Supports both:
 * 1. Bearer token in Authorization header
 * 2. auth_token query parameter (for EventSource which can't set headers)
 */
const sseAuthMiddleware = async (req: any, res: express.Response, next: express.NextFunction) => {
	try {
		let token = null;

		// Check for token in query parameter (for EventSource)
		if (req.query.auth_token) {
			token = req.query.auth_token;
		}
		// Check for token in Authorization header
		else if (req.headers.authorization && req.headers.authorization.startsWith("Bearer ")) {
			token = req.headers.authorization.substring(7);
		}

		// If no token, allow connection but mark as unauthenticated
		if (!token) {
			req.isAuthenticated = false;
			return next();
		}

		// Verify token with Supabase
		const { data, error } = await supabase.auth.getUser(token);

		if (error || !data.user) {
			req.isAuthenticated = false;
		} else {
			req.isAuthenticated = true;
			req.user = {
				id: data.user.id,
				email: data.user.email,
				role: data.user.role,
			};
		}

		next();
	} catch (error) {
		console.error("SSE auth error:", error);
		req.isAuthenticated = false;
		next();
	}
};

// Endpoint to open an SSE connection for a specific `planId`
router.get("/events/:planId", sseAuthMiddleware, (req, res) => {
	const { planId } = req.params;

	// Register client with authentication info
	registerClient(planId, res, {
		isAuthenticated: (req as any).isAuthenticated,
		user: (req as any).user,
	});
});

export default router;
