export interface License {
	id: string;
	customer_id: string;
	license_key: string;
	status: "active" | "inactive" | "expired";
	features: Record<string, any>;
	created_at: string;
	expires_at: string;
	last_verified: string;
	usage_count: number;
	created_by: string;
}

export interface LicenseLog {
	id: string;
	license_id: string;
	action: string;
	timestamp: string;
	metadata: Record<string, any>;
}

export interface CustomerProfile {
	id: string;
	company_name: string;
	contact_number: string;
	address: string;
	created_at: string;
	updated_at: string;
}

export interface User {
	id: string;
	email: string;
	role: "admin" | "customer";
	metadata: Record<string, any>;
}
