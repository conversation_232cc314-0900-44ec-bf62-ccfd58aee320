import React, { useState, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/lib/context/AuthContext";
import { useLicenseStore } from "@/lib/store/useStore";
import { Shield } from "lucide-react";
import { Button } from "@/components/ui/button";

interface RequireAuthProps {
	children: React.ReactNode;
}

const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
	const { user, isLoading } = useAuth();
	const { isActive, setLicense } = useLicenseStore();
	const location = useLocation();
	const [isLicenseChecked, setIsLicenseChecked] = useState(false);

	useEffect(() => {
		const checkLocalStorageLicense = () => {
			const hasLocalStorage = localStorage.getItem("nextdr_license_active") === "true";

			if (hasLocalStorage && !isActive) {
				const customerId = localStorage.getItem("nextdr_license_customer_id") || "unknown";
				const expiresAt =
					localStorage.getItem("nextdr_license_expires_at") ||
					new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

				let features = ["recovery_plans", "multiple_datacenters"];
				try {
					const storedFeatures = localStorage.getItem("nextdr_license_features");
					if (storedFeatures) {
						features = JSON.parse(storedFeatures);
					}
				} catch (e) {
					console.error("Error parsing license features:", e);
				}

				setLicense({
					isActive: true,
					expiresAt,
					customerId,
					features,
				});
			}

			setIsLicenseChecked(true);
		};

		checkLocalStorageLicense();
	}, [isActive, setLicense]);

	if (isLoading || !isLicenseChecked) {
		return (
			<div className="flex h-screen items-center justify-center">
				<div className="animate-pulse text-center">
					<Shield className="mx-auto h-12 w-12 text-dr-purple mb-4" />
					<p className="text-lg">Loading...</p>
				</div>
			</div>
		);
	}

	if (!user) {
		return <Navigate to="/login" state={{ from: location }} replace />;
	}

	if (!isActive && location.pathname !== "/license") {
		return (
			<div className="flex flex-col h-screen items-center justify-center p-4">
				<Shield className="h-16 w-16 text-amber-500 mb-4" />
				<h1 className="text-2xl font-bold mb-2">License Required</h1>
				<p className="text-muted-foreground mb-4 text-center max-w-md">
					Your license is not active or has expired. Please activate your license to continue.
				</p>
				<Button
					onClick={() => (window.location.href = "/license")}
					className="bg-dr-purple hover:bg-dr-purple-dark"
				>
					Go to License Page
				</Button>
			</div>
		);
	}

	return <>{children}</>;
};

export default RequireAuth;
