import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import {
	Database,
	Folder,
	Home,
	Layers,
	RefreshCw,
	Shield,
	LogOut,
	ChevronLeft,
	ChevronRight,
	Users as UsersIcon,
	UserCog,
	Setting<PERSON>,
} from "lucide-react";
import { useSidebarStore } from "@/lib/store/useStore";
import { cn } from "@/lib/utils";
import { useAuth } from "@/lib/context/AuthContext";
import { useRBAC } from "@/hooks/useRBAC";
import { RolePermissions } from "@/types/rbac";

const Sidebar = () => {
	const location = useLocation();
	const { collapsed, toggleCollapse } = useSidebarStore();
	const { signOut, user } = useAuth();
	const { hasPermission } = useRBAC();
	const [hoveredItem, setHoveredItem] = useState(null);

	const mainColor = "#1DB954";
	const glassEffect = "sidebar-glass";

	const navItems = [
		{
			name: "Dashboard",
			path: "/",
			icon: Home,
			permission: null as keyof RolePermissions | null, // Everyone can access dashboard
		},
		{
			name: "Datacenters",
			path: "/datacenters",
			icon: Database,
			permission: null as keyof RolePermissions | null, // Everyone can access datacenters
		},
		{
			name: "Applications",
			path: "/applications",
			icon: Folder,
			permission: null as keyof RolePermissions | null, // Everyone can access applications
		},
		{
			name: "Recovery Plans",
			path: "/recoveryplan",
			icon: RefreshCw,
			permission: "canExecuteRecoveryPlans" as keyof RolePermissions,
		},
		{
			name: "Backup Catalogs",
			path: "/backupcatalogs",
			icon: Layers,
			permission: null as keyof RolePermissions | null, // Everyone can access backup catalogs
		},
		{
			name: "Users",
			path: "/users",
			icon: UsersIcon,
			permission: "canManageUsers" as keyof RolePermissions,
		},
		{
			name: "Internal Groups",
			path: "/internal-groups",
			icon: UserCog,
			permission: "canManageGroups" as keyof RolePermissions,
		},
		{
			name: "Integrations",
			path: "/integrations",
			icon: Settings,
			permission: null as keyof RolePermissions | null, // Everyone can access integrations
		},
		{
			name: "License",
			path: "/license",
			icon: Shield,
			permission: null as keyof RolePermissions | null,
		},
	];

	const filteredNavItems = navItems.filter((item) => {
		if (!item.permission) return true;
		return hasPermission(item.permission);
	});

	const sidebarVariants = {
		expanded: { width: 250 },
		collapsed: { width: 64 },
	};

	const textVariants = {
		expanded: { opacity: 1, display: "block" },
		collapsed: { opacity: 0, display: "none" },
	};

	return (
		<motion.div
			initial={collapsed ? "collapsed" : "expanded"}
			animate={collapsed ? "collapsed" : "expanded"}
			variants={sidebarVariants}
			transition={{ duration: 0.3, ease: "easeInOut" }}
			className={`h-screen fixed top-0 left-0 z-40 flex flex-col ${glassEffect}`}
			style={{
				color: "white",
				backgroundImage: `
					linear-gradient(135deg, rgba(29, 185, 84, 0.02) 0%, rgba(18, 18, 18, 0.4) 100%),
					radial-gradient(circle at 0% 0%, rgba(29, 185, 84, 0.01), transparent 50%)
				`,
			}}
		>
			<div className="absolute top-[15%] left-[20%] w-24 h-24 rounded-full bg-[rgba(29,185,84,0.01)] blur-3xl pointer-events-none"></div>
			<div className="absolute bottom-[25%] right-[10%] w-32 h-32 rounded-full bg-[rgba(29,185,84,0.01)] blur-3xl pointer-events-none"></div>

			<div className="py-5 px-5 flex justify-between items-center border-b border-white/5 relative overflow-hidden">
				<motion.div
					variants={textVariants}
					transition={{ delay: 0.1 }}
					className="flex items-center"
				>
					<img src="/logo.png" alt="orKrestrate.AI Logo" className="w-24 h-24" />
					{/* <span className="font-semibold text-lg text-white">orKrestrate.AI</span> */}
				</motion.div>

				<button
					onClick={toggleCollapse}
					style={{
						backgroundColor: collapsed ? "transparent" : "rgba(255, 255, 255, 0.05)",
						color: mainColor,
					}}
					className="p-1.5 rounded-full hover:bg-white/10 transition-all duration-300"
					aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
				>
					{collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
				</button>
			</div>

			<nav className="flex-1 py-3 overflow-y-auto">
				<ul className="space-y-1 px-2">
					{filteredNavItems.map((item) => {
						const isActive = location.pathname === item.path;
						return (
							<li key={item.name}>
								<Link
									to={item.path}
									style={{
										backgroundColor: isActive ? "var(--sidebar-active)" : "transparent",
										color: isActive ? mainColor : "rgba(255, 255, 255, 0.7)",
									}}
									className={cn(
										"flex items-center rounded-md transition-all duration-200 relative group",
										collapsed ? "justify-center h-10 w-10 mx-auto" : "px-3 py-2.5",
										"hover:bg-[rgba(29,185,84,0.02)]"
									)}
									onMouseEnter={() => setHoveredItem(item.name)}
									onMouseLeave={() => setHoveredItem(null)}
								>
									<div className={cn("flex-shrink-0", collapsed && "flex justify-center")}>
										<item.icon size={18} />
									</div>
									<motion.span
										variants={textVariants}
										className="ml-3 text-sm font-medium whitespace-nowrap"
									>
										{item.name}
									</motion.span>

									{isActive && (
										<>
											<motion.div
												layoutId="activeIndicator"
												style={{ backgroundColor: mainColor }}
												className="absolute left-0 top-0 w-1 h-full rounded-r"
											/>
											<motion.div
												layoutId="activeGlowSubtle"
												className="absolute inset-0 rounded-md opacity-10"
												style={{
													backgroundColor: "rgba(29, 185, 84, 0.1)",
												}}
											/>
										</>
									)}

									{collapsed && hoveredItem === item.name && (
										<div className="absolute left-full ml-2 px-3 py-1.5 rounded text-white text-xs whitespace-nowrap z-50 bg-[rgba(18,18,18,0.8)] backdrop-blur-sm border border-[rgba(40,40,40,0.5)]">
											{item.name}
										</div>
									)}
								</Link>
							</li>
						);
					})}
				</ul>
			</nav>

			<div className="mt-auto px-3 py-2 border-t border-white/10">
				<div className="flex flex-col gap-2">
					<div className="flex items-center gap-2 px-1">
						<div className="w-8 h-8 rounded-full bg-[rgba(29,185,84,0.1)] flex items-center justify-center flex-shrink-0">
							<span className="text-sm font-medium text-[#1DB954]">
								{user?.email?.charAt(0).toUpperCase()}
							</span>
						</div>
						<motion.div variants={textVariants} className="min-w-0 flex-1">
							<p className="text-sm font-medium text-white/70 truncate">{user?.email}</p>
						</motion.div>
					</div>
					<button
						onClick={signOut}
						className="w-full flex items-center px-3 py-2.5 text-sm rounded-md transition-all duration-200
						text-white/70 hover:text-white hover:bg-[rgba(29,185,84,0.02)] group"
						onMouseEnter={() => setHoveredItem("signOut")}
						onMouseLeave={() => setHoveredItem(null)}
					>
						<LogOut size={18} className="flex-shrink-0" />
						<motion.span variants={textVariants} className="ml-3 font-medium">
							Sign Out
						</motion.span>

						{collapsed && hoveredItem === "signOut" && (
							<div className="absolute left-full ml-2 px-3 py-1.5 rounded text-white text-xs whitespace-nowrap z-50 bg-[rgba(18,18,18,0.8)] backdrop-blur-sm border border-[rgba(40,40,40,0.5)]">
								Sign Out
							</div>
						)}
					</button>
				</div>
			</div>
		</motion.div>
	);
};

export default Sidebar;
