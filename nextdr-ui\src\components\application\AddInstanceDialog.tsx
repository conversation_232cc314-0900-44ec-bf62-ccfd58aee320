import React, { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { ApplicationGroup } from "@/lib/types";
import { useToast } from "@/components/ui/use-toast";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	Di<PERSON>Title,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface Instance {
	id: string;
	name: string;
	machineType?: string;
	zone?: string;
	status?: string;
	displayName?: string;
}

interface AddInstanceDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onInstancesSelect: (instances: Instance[]) => void;
	selectedGroup: ApplicationGroup;
	fetchInstances: (projectId: string, datacenterId: string) => Promise<Instance[]>;
	datacenterId: string;
}

export const AddInstanceDialog: React.FC<AddInstanceDialogProps> = ({
	isOpen,
	onClose,
	onInstancesSelect,
	selectedGroup,
	fetchInstances,
	datacenterId,
}) => {
	const [selectedInstances, setSelectedInstances] = useState<Instance[]>([]);
	const [availableInstances, setAvailableInstances] = useState<Instance[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const { toast } = useToast();
	console.log("AddInstanceDialog rendered", datacenterId);

	useEffect(() => {
		async function loadInstances() {
			if (!selectedGroup?.data_center_id || !isOpen) return;

			setLoading(true);
			try {
				const instances = await fetchInstances(selectedGroup.data_center_id, datacenterId);
				const filteredInstances = instances.filter(
					(instance) => !selectedGroup.vm_ids.includes(instance.id || instance.name)
				);
				setAvailableInstances(filteredInstances);
			} catch (error) {
				console.error("Error fetching instances:", error);
				toast({
					title: "Error",
					description: "Failed to load VM instances",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		}

		loadInstances();
	}, [selectedGroup, datacenterId, isOpen]);

	const handleInstanceSelect = (instance: Instance, checked: boolean) => {
		if (checked) {
			setSelectedInstances([...selectedInstances, instance]);
		} else {
			setSelectedInstances(selectedInstances.filter((i) => i.id !== instance.id));
		}
	};

	const handleSubmit = () => {
		if (selectedInstances.length === 0) {
			toast({
				title: "Warning",
				description: "Please select at least one instance",
				variant: "destructive",
			});
			return;
		}

		onInstancesSelect(selectedInstances);
		setSelectedInstances([]);
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Add Instances</DialogTitle>
					<DialogDescription>Select instances to add to the application group</DialogDescription>
				</DialogHeader>

				<div className="space-y-4 my-4">
					<div>
						<Label className="text-sm font-medium mb-2">Select Instances</Label>
						{loading ? (
							<div className="flex items-center justify-center p-4">
								<Loader2 className="animate-spin mr-2 h-5 w-5" />
								<span>Loading instances...</span>
							</div>
						) : (
							<div className="max-h-60 overflow-y-auto bg-secondary rounded-lg border border-border">
								{availableInstances?.length === 0 ? (
									<div className="text-center p-4 text-muted-foreground">
										No instances found. Please add VMs to the datacenter.
									</div>
								) : (
									availableInstances?.map((instance) => (
										<div
											key={instance.id}
											className="flex items-center p-3 hover:bg-secondary/80 border-b border-border last:border-b-0"
										>
											<Checkbox
												id={instance.id}
												checked={selectedInstances.some((i) => i.id === instance.id)}
												onCheckedChange={(checked) =>
													handleInstanceSelect(instance, checked as boolean)
												}
												className="mr-3"
											/>
											<Label htmlFor={instance.id} className="flex-1 cursor-pointer">
												<div>{instance.name}</div>
												<div className="text-sm text-muted-foreground">
													{instance.displayName || instance.machineType || ""}
												</div>
											</Label>
											{instance.status && (
												<span
													className={`px-2 py-1 rounded-full text-xs ${
														instance.status === "RUNNING"
															? "bg-green-900/20 text-green-400"
															: "bg-gray-900/20 text-gray-400"
													}`}
												>
													{instance.status}
												</span>
											)}
										</div>
									))
								)}
							</div>
						)}
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={loading || selectedInstances.length === 0}
						className="bg-dr-purple hover:bg-dr-purple-dark"
					>
						Add Selected Instances
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default AddInstanceDialog;
