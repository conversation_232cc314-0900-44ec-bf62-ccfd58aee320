import React, { useState, useEffect } from "react";
import { RecoveryStep } from "@/lib/types";
import { useModalStore } from "@/lib/store/useStore";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { motion } from "framer-motion";
import {
	Info,
	Pencil,
	GripVertical,
	Play,
	CheckCircle,
	RefreshCw,
	Trash,
	AlertCircle,
	Plus,
	Clock,
	ThumbsUp,
	ThumbsDown,
	ArrowRight,
	PauseCircle,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { useDeleteRecoveryStep, useRecoveryPlanExecution } from "@/lib/api/hooks/recoveryPlans";
import { RecoveryStepStatus, RecoveryPlanStatus } from "@/lib/api/api-client";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import IaCRecoveryStep from "@/components/recovery/IaCRecoveryStep";
import VMRestoreStep from "@/components/recovery/VMRestoreStep";
import ExecutionProgress from "@/components/recovery/ExecutionProgress";

interface RecoveryStepListProps {
	steps: RecoveryStep[];
	planId?: string;
	datacenterId?: string;
	onEditStep: (step: RecoveryStep) => void;
	onReorderSteps: (steps: RecoveryStep[]) => void;
	onDeleteStep?: (step: RecoveryStep) => void;
}

const RecoveryStepList: React.FC<RecoveryStepListProps> = ({
	steps,
	planId,
	datacenterId,
	onEditStep,
	onReorderSteps,
	onDeleteStep,
}) => {
	const { onOpen } = useModalStore();
	const [stepProgress, setStepProgress] = useState<Record<string, number>>({});
	const [useNewExecutionEngine, setUseNewExecutionEngine] = useState(true);

	const {
		stepStatuses,
		planStatus,
		checkpoints,
		isExecuting,
		error: executionError,
		isLoadingInitialState,
		executeRecoveryPlan,
		resumeExecution,
		calculateProgress,
		getCheckpointForStep,
		refreshExecutionState,
	} = useRecoveryPlanExecution(planId);

	const planProgress = calculateProgress(steps.length);

	const deleteStepMutation = useDeleteRecoveryStep();

	const handleAddStep = () => {
		if (!planId) {
			toast.error("Cannot add step: Missing recovery plan ID");
			return;
		}
		onOpen("recoveryStep", { planId });
	};

	const handleEditStep = (step: RecoveryStep) => {
		onEditStep(step);
	};

	const handleDeleteStep = (step: RecoveryStep) => {
		onOpen("confirmDialog", {
			title: "Delete Recovery Step",
			message: `Are you sure you want to delete the step "${step.name}"? This action cannot be undone.`,
			confirmLabel: "Delete",
			cancelLabel: "Cancel",
			variant: "destructive",
			onConfirm: async () => {
				try {
					await deleteStepMutation.mutateAsync(step.id);
					toast.success(`Step "${step.name}" deleted successfully`);

					if (onDeleteStep) {
						onDeleteStep(step);
					}
				} catch (error) {
					console.error("Error deleting recovery step:", error);
					toast.error("Failed to delete recovery step");
				}
			},
		});
	};

	const handleInfoClick = (step: RecoveryStep) => {
		onOpen("stepInfo", {
			step,
			title: "Step Information",
			content: (
				<div className="space-y-4">
					<div>
						<h4 className="font-medium mb-2">Step Details</h4>
						<div className="space-y-2 text-sm">
							<p>
								<span className="text-muted-foreground">Name:</span> {step.name}
							</p>
							<p>
								<span className="text-muted-foreground">Type:</span> {step.operation_type}
							</p>
							<p>
								<span className="text-muted-foreground">Order:</span> {step.step_order}
							</p>
						</div>
					</div>
					{step.operation_type === "Restore virtual machine" && (
						<div>
							<h4 className="font-medium mb-2">VM Configuration</h4>
							<div className="space-y-2 text-sm">
								{Object.entries(step.configuration).map(([key, value]) => (
									<p key={key}>
										<span className="text-muted-foreground">{key}:</span> {String(value)}
									</p>
								))}
							</div>
						</div>
					)}
					{step.operation_type === "IaC" && (
						<div>
							<h4 className="font-medium mb-2">IaC Configuration</h4>
							<div className="space-y-2 text-sm">
								{Object.entries(step.configuration).map(([key, value]) => (
									<p key={key}>
										<span className="text-muted-foreground">{key}:</span> {String(value)}
									</p>
								))}
							</div>
						</div>
					)}
				</div>
			),
		});
	};

	useEffect(() => {
		const intervals: Record<string, NodeJS.Timeout> = {};

		steps.forEach((step) => {
			const status = stepStatuses[step.step_order.toString()];

			if (status === "IN_PROGRESS") {
				if (!stepProgress[step.id]) {
					setStepProgress((prev) => ({
						...prev,
						[step.id]: 0,
					}));
				}

				// Animate progress more smoothly for in-progress steps
				intervals[step.id] = setInterval(() => {
					setStepProgress((prev) => {
						const currentProgress = prev[step.id] || 0;
						// Cap at 90% for in-progress steps to show it's still working
						const newProgress = Math.min(90, currentProgress + 0.5);
						return {
							...prev,
							[step.id]: newProgress,
						};
					});
				}, 100);
			} else if (status === "COMPLETED" || status === "APPROVED") {
				// Set to 100% for completed or approved steps
				setStepProgress((prev) => ({
					...prev,
					[step.id]: 100,
				}));
			} else if (status === "FAILED" || status === "REJECTED") {
				// Set to 100% for failed or rejected steps
				setStepProgress((prev) => ({
					...prev,
					[step.id]: 100,
				}));
			} else if (planStatus === "COMPLETED") {
				// If the plan is completed, set all steps to 100%
				setStepProgress((prev) => ({
					...prev,
					[step.id]: 100,
				}));
			} else if (status === "AWAITING_APPROVAL") {
				// Set to 75% for steps awaiting approval
				setStepProgress((prev) => ({
					...prev,
					[step.id]: 75,
				}));
			} else {
				// Reset progress for other statuses
				setStepProgress((prev) => ({
					...prev,
					[step.id]: 0,
				}));
			}
		});

		return () => {
			Object.values(intervals).forEach(clearInterval);
		};
	}, [steps, stepStatuses]);

	const startExecution = () => {
		if (!planId || !datacenterId) {
			toast.error("Missing plan ID or datacenter ID");
			return;
		}

		// Always show the execution options modal
		// Find the current checkpoint ID if there's a paused execution
		const currentCheckpointId = Object.keys(checkpoints).find(
			(id) => checkpoints[id].status === "AWAITING_APPROVAL"
		);

		// Determine if we can resume
		const canResume = planStatus === "PAUSED" && !!currentCheckpointId;

		// Show the execution options modal
		onOpen("executionOptions", {
			title: "Execution Options",
			message: canResume
				? "Do you want to resume from where you left off or restart the plan from the beginning?"
				: "Choose how to execute the recovery plan:",
			canResume: canResume,
			currentCheckpointId,
			onRestart: () => {
				setStepProgress({});
				executeRecoveryPlan(datacenterId, false);
			},
			onResume: (checkpointId: string) => {
				handleResumeExecution(checkpointId);
			},
		});
	};

	const handleResumeExecution = async (checkpointId: string) => {
		if (!planId) {
			toast.error("Missing plan ID");
			return;
		}

		if (!datacenterId) {
			toast.error("Missing datacenter ID");
			return;
		}

		// Show a loading toast
		toast.loading("Resuming execution...", {
			id: "resume-execution",
			description: "Continuing with the next steps after approval",
		});

		try {
			// Call the resume function with the checkpoint ID and datacenter ID
			await resumeExecution(checkpointId, datacenterId);

			// Update the toast on success
			toast.success("Execution resumed successfully", {
				id: "resume-execution",
				description: "The recovery plan is continuing with the next steps",
			});

			// Set executing state to true to show the spinner
			// This will be updated by SSE events when the actual execution starts
			setStepProgress({});
		} catch (error) {
			console.error("Error resuming execution:", error);

			// Update the toast on error
			toast.error("Failed to resume execution", {
				id: "resume-execution",
				description: error instanceof Error ? error.message : "An unknown error occurred",
			});
		}
	};

	const onDragEnd = (result: any) => {
		if (!result.destination) return;

		const items = Array.from(steps);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Call parent handler to persist changes
		onReorderSteps(items);
	};

	const renderStep = (step: RecoveryStep, index: number) => {
		const stepStatus = stepStatuses[step.step_order.toString()];
		const isExecutingThisStep = stepStatus === "IN_PROGRESS";
		const isCompleted = stepStatus === "COMPLETED";
		const isFailed = stepStatus === "FAILED";
		const isAwaitingApproval = stepStatus === "AWAITING_APPROVAL";
		const isApproved = stepStatus === "APPROVED";
		const isRejected = stepStatus === "REJECTED";

		// Check if this step is a checkpoint
		const checkpoint = getCheckpointForStep(step.step_order.toString());
		const isCheckpoint = !!checkpoint;

		const currentProgress = stepProgress[step.id] || 0;

		// Determine border style based on execution status
		let borderStyle = "";
		if (isExecutingThisStep) {
			borderStyle = "border-blue-500 border-2";
		} else if (isCompleted) {
			borderStyle = "border-green-500 border-2";
		} else if (isFailed) {
			borderStyle = "border-red-500 border-2";
		} else if (isAwaitingApproval) {
			borderStyle = "border-yellow-500 border-2";
		} else if (isApproved) {
			borderStyle = "border-green-500 border-2";
		} else if (isRejected) {
			borderStyle = "border-red-500 border-2";
		}

		const stepContent = (
			<div className={`flex flex-col p-4 bg-card rounded-lg border ${borderStyle}`}>
				{/* Main content with improved responsive layout */}
				<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
					{/* Step info and badges - stacks on mobile, side by side on larger screens */}
					<div className="flex flex-col sm:flex-row sm:items-center gap-3 w-full">
						<div className="flex items-center gap-2 min-w-0">
							<GripVertical className="h-5 w-5 text-muted-foreground flex-shrink-0" />
							<span className="font-medium truncate">{step.name}</span>
						</div>

						{/* Badges in a scrollable container on small screens */}
						<div className="flex flex-wrap gap-1.5 mt-1 sm:mt-0">
							<Badge variant="outline" className="text-xs py-0 h-5">
								{step.operation_type}
							</Badge>

							{isCheckpoint && (
								<Badge
									variant="secondary"
									className="bg-blue-500/10 text-blue-600 border-blue-500/20 text-xs py-0 h-5"
								>
									Checkpoint
								</Badge>
							)}

							{/* Status badges - show only the current status badge */}
							{isAwaitingApproval && (
								<Badge
									variant="outline"
									className="bg-yellow-500/10 text-yellow-600 border-yellow-500/20 text-xs py-0 h-5"
								>
									<Clock className="h-3 w-3 mr-1" />
									Awaiting Approval
								</Badge>
							)}

							{isApproved && (
								<Badge
									variant="outline"
									className="bg-green-500/10 text-green-600 border-green-500/20 text-xs py-0 h-5"
								>
									<ThumbsUp className="h-3 w-3 mr-1" />
									Approved
								</Badge>
							)}

							{isRejected && (
								<Badge
									variant="destructive"
									className="bg-red-500/10 text-red-600 border-red-500/20 text-xs py-0 h-5"
								>
									<ThumbsDown className="h-3 w-3 mr-1" />
									Rejected
								</Badge>
							)}

							{isExecutingThisStep && (
								<Badge
									variant="outline"
									className="bg-blue-500/10 text-blue-600 border-blue-500/20 text-xs py-0 h-5"
								>
									<RefreshCw className="h-3 w-3 mr-1 animate-spin" />
									Executing
								</Badge>
							)}

							{isCompleted && (
								<Badge
									variant="outline"
									className="bg-green-500/10 text-green-600 border-green-500/20 text-xs py-0 h-5"
								>
									<CheckCircle className="h-3 w-3 mr-1" />
									Completed
								</Badge>
							)}

							{isFailed && (
								<Badge
									variant="destructive"
									className="bg-red-500/10 text-red-600 border-red-500/20 text-xs py-0 h-5"
								>
									<AlertCircle className="h-3 w-3 mr-1" />
									Failed
								</Badge>
							)}
						</div>
					</div>

					{/* Action buttons with improved responsive layout */}
					<div className="flex flex-wrap items-center gap-1.5 mt-1 sm:mt-0 sm:flex-nowrap sm:flex-shrink-0">
						{isAwaitingApproval && checkpoint && (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="outline"
											size="sm"
											className="text-yellow-600 border-yellow-500/20 bg-yellow-500/10 hover:bg-yellow-500/20 h-8 px-2"
											onClick={() =>
												onOpen("checkpointInfo", {
													checkpoint,
													step,
													onResume: () => handleResumeExecution(checkpoint.id),
												})
											}
										>
											<PauseCircle className="h-4 w-4" />
											<span className="sm:hidden md:inline">Approval</span>
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>This checkpoint is awaiting approval</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						)}

						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										size="icon"
										className="h-8 w-8"
										onClick={() =>
											onOpen("stepInfo", {
												step,
												title: "Step Information",
												content: (
													<div className="space-y-4">
														<div>
															<h4 className="font-medium mb-2">Step Details</h4>
															<div className="space-y-2 text-sm">
																<p>
																	<span className="text-muted-foreground">Name:</span> {step.name}
																</p>
																<p>
																	<span className="text-muted-foreground">Type:</span>{" "}
																	{step.operation_type}
																</p>
																<p>
																	<span className="text-muted-foreground">Order:</span>{" "}
																	{step.step_order}
																</p>
															</div>
														</div>
														{step.operation_type === "Restore virtual machine" && (
															<div>
																<h4 className="font-medium mb-2">VM Configuration</h4>
																<div className="space-y-2 text-sm">
																	{Object.entries(step.configuration).map(([key, value]) => (
																		<p key={key}>
																			<span className="text-muted-foreground">{key}:</span>{" "}
																			{String(value)}
																		</p>
																	))}
																</div>
															</div>
														)}
														{step.operation_type === "IaC" && (
															<div>
																<h4 className="font-medium mb-2">IaC Configuration</h4>
																<div className="space-y-2 text-sm">
																	{Object.entries(step.configuration).map(([key, value]) => (
																		<p key={key}>
																			<span className="text-muted-foreground">{key}:</span>{" "}
																			{String(value)}
																		</p>
																	))}
																</div>
															</div>
														)}
													</div>
												),
											})
										}
									>
										<Info className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>View step details</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>

						{!isExecuting && (
							<>
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="h-8 w-8"
												onClick={() => handleEditStep(step)}
											>
												<Pencil className="h-4 w-4" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>Edit step</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>

								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="h-8 w-8"
												onClick={() => handleDeleteStep(step)}
											>
												<Trash className="h-4 w-4" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>Delete step</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</>
						)}
					</div>
				</div>

				{/* Progress bar with improved styling */}
				{(isExecutingThisStep ||
					isCompleted ||
					isFailed ||
					isAwaitingApproval ||
					isApproved ||
					isRejected) && (
					<div className="mt-3">
						<div className="flex justify-between items-center text-xs mb-1">
							<span>
								{isExecutingThisStep
									? "Executing..."
									: isAwaitingApproval
									? "Awaiting approval"
									: isCompleted
									? "Completed"
									: isFailed
									? "Failed"
									: isApproved
									? "Approved"
									: "Rejected"}
							</span>
							<span>{Math.round(currentProgress)}%</span>
						</div>
						<Progress
							value={currentProgress}
							className={`h-2 ${
								isExecutingThisStep
									? "bg-blue-100"
									: isAwaitingApproval
									? "bg-yellow-100"
									: isCompleted || isApproved
									? "bg-green-100"
									: "bg-red-100"
							}`}
						/>
					</div>
				)}
			</div>
		);

		return (
			<Draggable key={step.id} draggableId={step.id} index={index} isDragDisabled={isExecuting}>
				{(provided, snapshot) => (
					<div
						ref={provided.innerRef}
						{...provided.draggableProps}
						className={`${snapshot.isDragging ? "opacity-50" : ""} w-full`}
					>
						<div className="flex items-center w-full">
							<div
								{...provided.dragHandleProps}
								className="cursor-grab active:cursor-grabbing w-full"
							>
								{stepContent}
							</div>
						</div>
					</div>
				)}
			</Draggable>
		);
	};

	return (
		<div className="space-y-4 w-full">
			{/* Execution Engine Toggle */}
			{planId && datacenterId && (
				<div className="bg-card/50 border border-border rounded-lg p-4 shadow-sm">
					<div className="flex items-center justify-between">
						<div className="flex items-center space-x-2">
							<Switch
								id="execution-engine"
								checked={useNewExecutionEngine}
								onCheckedChange={setUseNewExecutionEngine}
							/>
							<Label htmlFor="execution-engine" className="text-sm font-medium">
								Use Enhanced Execution Engine
							</Label>
						</div>
						<Badge variant="outline" className="text-xs">
							{useNewExecutionEngine ? "v2.0" : "Legacy"}
						</Badge>
					</div>
					<p className="text-xs text-muted-foreground mt-2">
						{useNewExecutionEngine
							? "Real-time progress tracking with modular step execution"
							: "Original execution system"}
					</p>
				</div>
			)}

			{/* New Execution System */}
			{planId && datacenterId && useNewExecutionEngine && (
				<ExecutionProgress
					planId={planId}
					datacenterId={datacenterId}
					onExecutionComplete={() => {
						// Refresh the legacy execution state for compatibility
						refreshExecutionState();
					}}
				/>
			)}

			{steps.length > 0 && !useNewExecutionEngine && (
				<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4 bg-card/50 p-3 rounded-lg border border-border">
					<div className="w-full sm:flex-1">
						<div className="flex flex-wrap items-center gap-2 mb-1">
							<span className="text-sm font-medium">Recovery Plan Progress</span>
							{planStatus === "PAUSED" && (
								<Badge
									variant="outline"
									className="bg-yellow-500/10 text-yellow-600 border-yellow-500/20 text-xs py-0 h-5"
								>
									<PauseCircle className="h-3 w-3 mr-1" />
									Paused
								</Badge>
							)}
							{planStatus === "IN_PROGRESS" && (
								<Badge
									variant="outline"
									className="bg-blue-500/10 text-blue-600 border-blue-500/20 text-xs py-0 h-5"
								>
									<RefreshCw className="h-3 w-3 mr-1 animate-spin" />
									In Progress
								</Badge>
							)}
							{planStatus === "COMPLETED" && (
								<Badge
									variant="outline"
									className="bg-green-500/10 text-green-600 border-green-500/20 text-xs py-0 h-5"
								>
									<CheckCircle className="h-3 w-3 mr-1" />
									Completed
								</Badge>
							)}
							{planStatus === "FAILED" && (
								<Badge
									variant="destructive"
									className="bg-red-500/10 text-red-600 border-red-500/20 text-xs py-0 h-5"
								>
									<AlertCircle className="h-3 w-3 mr-1" />
									Failed
								</Badge>
							)}
							<span className="text-xs ml-auto">{Math.round(planProgress)}%</span>
						</div>
						<Progress value={planProgress} className="h-2" />
					</div>

					<div className="flex gap-2 mt-3 sm:mt-0">
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										onClick={() => refreshExecutionState()}
										variant="outline"
										size="icon"
										className="h-8 w-8"
										disabled={isLoadingInitialState}
									>
										<RefreshCw
											className={`h-4 w-4 ${isLoadingInitialState ? "animate-spin" : ""}`}
										/>
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>Refresh execution state</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>

						<Button
							onClick={startExecution}
							disabled={isExecuting || steps.length === 0}
							variant="default"
							size="sm"
							className="bg-dr-purple hover:bg-dr-purple-dark"
						>
							{isExecuting ? (
								<>
									<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
									Executing...
								</>
							) : (
								<>
									<Play className="mr-2 h-4 w-4" />
									Execute Plan
								</>
							)}
						</Button>
					</div>
				</div>
			)}

			{!useNewExecutionEngine && isLoadingInitialState && (
				<div className="bg-blue-500/10 border border-blue-500/20 text-blue-500 p-4 rounded-lg mb-4 flex items-center shadow-sm">
					<RefreshCw className="h-5 w-5 mr-3 flex-shrink-0 animate-spin" />
					<p className="text-sm font-medium">Loading execution state...</p>
				</div>
			)}

			{!useNewExecutionEngine && executionError && (
				<div className="bg-red-500/10 border border-red-500/20 text-red-500 p-4 rounded-lg mb-4 flex items-center shadow-sm">
					<AlertCircle className="h-5 w-5 mr-3 flex-shrink-0" />
					<p className="text-sm font-medium">{executionError}</p>
				</div>
			)}

			{steps.length === 0 ? (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					className="text-center py-8 px-4 bg-card/50 rounded-lg border border-border"
				>
					<RefreshCw className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
					<p className="text-lg font-medium mb-2">No recovery steps added yet</p>
					<p className="text-muted-foreground mb-6">
						Add steps to create your recovery plan workflow
					</p>
					<Button
						onClick={handleAddStep}
						size="sm"
						variant="default"
						className="bg-dr-purple hover:bg-dr-purple-dark"
						disabled={isExecuting}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add First Step
					</Button>
				</motion.div>
			) : (
				<DragDropContext onDragEnd={onDragEnd}>
					<Droppable
						droppableId="steps"
						type="step"
						isDropDisabled={isExecuting}
						mode="standard"
						direction="vertical"
					>
						{(provided) => (
							<div
								{...provided.droppableProps}
								ref={provided.innerRef}
								className="space-y-2 w-full"
							>
								{steps.map((step, index) => renderStep(step, index))}
								{provided.placeholder}
							</div>
						)}
					</Droppable>
				</DragDropContext>
			)}
		</div>
	);
};

export default RecoveryStepList;
