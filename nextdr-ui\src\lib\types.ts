// Core entity types
export interface Datacenter {
	id: string;
	name: string;
	hypervisor_type: "GCP" | "AWS" | "VMware" | "Proxmox";
	project_id?: string;
	apitoken: string;
	status?: "connected" | "disconnected" | "error";
	created_at?: string;
	zone?: string;
}

export interface VM {
	id: string;
	name: string;
	vm_id: string;
	datacenter_id: string;
	power_state: string;
	cpu_count: number;
	memory_size_mb: number;
	status?: string;
	os_type?: string;
	ip_address?: string;
}

export interface ApplicationGroup {
	id: string;
	name: string;
	description: string;
	vm_ids: string[];
	created_at: string;
	vm_count?: number;
	data_center_id: string;
}

export interface SnapshotSchedule {
	id: string;
	group_id: string;
	vm_ids: string[];
	frequency: "hourly" | "daily" | "weekly" | "monthly";
	retention_period: number;
	start_time: string;
	day_of_week?: number;
	day_of_month?: number;
	status: "active" | "paused" | "error";
	next_run?: string;
	last_run?: string;
	datacenter_id: string;
}

export interface RecoveryPlan {
	id: string;
	name: string;
	description: string;
	app_group_id: string;
	created_at: string;
	steps_count?: number;
	status?: "pending" | "ready" | "in_progress" | "completed" | "failed";
}

export interface RecoveryStep {
	id: string;
	recovery_plan_id: string;
	step_order: number;
	name: string;
	operation_type:
		| "Restore virtual machine"
		| "IaC"
		| "Database restore"
		| "Virus check"
		| "Apply OS updates"
		| "Manual step"
		| "Approval"
		| "Verification"
		| "Script"
		| "Notification";
	configuration: Record<string, any>;
	created_at: string;
	status?:
		| "pending"
		| "in_progress"
		| "completed"
		| "failed"
		| "awaiting_approval"
		| "approved"
		| "rejected";
	requires_approval?: boolean;
	approval_metadata?: {
		approver_id?: string;
		approval_status?: "pending" | "approved" | "rejected";
		approval_comment?: string;
		approved_by?: string;
		approved_at?: string;
		approval_ip?: string;
	};
}

export interface License {
	id: string;
	customer_id: string;
	jwt: string;
	issued_at: string;
	expires_at: string;
	features: string[];
}

// Form input types
export interface DatacenterFormInput {
	name: string;
	hypervisor_type: "GCP" | "AWS" | "VMware" | "Proxmox";
	project_id?: string;
	apitoken: string;
}

export interface ApplicationGroupFormInput {
	name: string;
	description: string;
	vm_ids: string[];
	datacenter_id: string;
}

export interface RecoveryPlanFormInput {
	name: string;
	description: string;
	app_group_id: string;
}

export interface NetworkInterfaceResponse {
	networkInterfaces: NetworkInterface[];
}

export interface NetworkInterface {
	name: string;
	network: string;
	subnetwork: string;
	network_ip: string;
	access_configs: AccessConfig[];
}

export interface AccessConfig {
	name: string;
	type: string;
	network_tier: string;
}
export interface IAMAccountResponse {
	iamRoles: IAMRole[];
}

export interface IAMRole {
	role: string;
	members: string[];
	condition: any;
	roleType: string;
}

export interface SupabaseSnapshot {
	id: string;
	created_at: string;
	group_id: string;
	vm_ids: string[];
	frequency: "hourly" | "daily" | "weekly" | "monthly";
	retention_period: number;
	start_time: string;
	day_of_week: number;
	day_of_month: number;
	status: "active" | "paused" | "error";
	next_run: string;
	last_run: string;
	datacenter_id: string;
}

export interface InternalGroup {
	id: string;
	name: string;
	description: string;
	type: "internal";
	created_at: string;
	updated_at: string;
	created_by: string;
	member_count?: number;
}

export interface GroupMember {
	id: string;
	group_id: string;
	user_id: string;
	created_at: string;
	created_by: string;
	user?: {
		email: string;
		role: string;
		status: string;
	};
}

export interface Group {
	id: string;
	name: string;
	description?: string;
	created_at: string;
	members: {
		id: string;
		email: string;
		role: string;
		status: string;
	}[];
}
