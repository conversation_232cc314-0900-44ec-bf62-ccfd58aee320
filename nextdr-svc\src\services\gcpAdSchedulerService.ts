import { <PERSON>ron<PERSON><PERSON> } from "cron";
import { supabase } from "./supabaseService";
import { gcpAdSyncService } from "./gcpAdSyncService";

class GcpAdSchedulerService {
	private syncJob: CronJob | null = null;
	private isRunning = false;

	async initialize(): Promise<void> {
		console.log("Initializing GCP AD scheduler service...");
		await this.updateSchedule();
	}

	async updateSchedule(): Promise<void> {
		try {
			if (this.syncJob) {
				this.syncJob.stop();
				this.syncJob = null;
			}

			const { data: config, error } = await supabase
				.from("integration_configs")
				.select("*")
				.eq("source", "gcp_ad")
				.single();

			if (error || !config?.enabled) {
				console.log("GCP AD integration not enabled, scheduler stopped");
				return;
			}

			const intervalMinutes = config.config?.sync_interval_minutes || 15;
			let cronPattern: string;
			switch (intervalMinutes) {
				case 15:
					cronPattern = "*/15 * * * *";
					break;
				case 30:
					cronPattern = "*/30 * * * *";
					break;
				case 60:
					cronPattern = "0 * * * *";
					break;
				default:
					cronPattern = `*/${intervalMinutes} * * * *`;
			}

			this.syncJob = new CronJob(
				cronPattern,
				async () => {
					await this.performScheduledSync();
				},
				null,
				false,
				"UTC"
			);

			this.syncJob.start();
			console.log(`GCP AD sync scheduled every ${intervalMinutes} minutes`);
		} catch (error) {
			console.error("Error updating GCP AD sync schedule:", error);
		}
	}

	private async performScheduledSync(): Promise<void> {
		if (this.isRunning) {
			console.log("GCP AD sync already running, skipping scheduled sync");
			return;
		}

		this.isRunning = true;
		console.log("Starting scheduled GCP AD sync...");

		try {
			const { data: config, error } = await supabase
				.from("integration_configs")
				.select("*")
				.eq("source", "gcp_ad")
				.single();

			if (error || !config?.enabled) {
				console.log("GCP AD integration not enabled, skipping sync");
				return;
			}

			await supabase
				.from("integration_configs")
				.update({
					sync_status: "in_progress",
					error_message: null,
					updated_at: new Date().toISOString(),
				})
				.eq("source", "gcp_ad");

			const syncResult = await gcpAdSyncService.performSync(config);

			await supabase
				.from("integration_configs")
				.update({
					sync_status: "success",
					last_sync_at: new Date().toISOString(),
					error_message: null,
					updated_at: new Date().toISOString(),
				})
				.eq("source", "gcp_ad");

			console.log("Scheduled GCP AD sync completed successfully:", syncResult);

			await supabase.from("audit_logs").insert({
				user_id: null,
				action: "scheduled_sync",
				entity_type: "integration_config",
				entity_id: config.id,
				details: {
					source: "gcp_ad",
					trigger_type: "scheduled",
					stats: syncResult,
				},
			});
		} catch (error: any) {
			console.error("Scheduled GCP AD sync failed:", error);

			await supabase
				.from("integration_configs")
				.update({
					sync_status: "error",
					error_message: error.message,
					updated_at: new Date().toISOString(),
				})
				.eq("source", "gcp_ad");

			await supabase.from("audit_logs").insert({
				user_id: null,
				action: "scheduled_sync_error",
				entity_type: "integration_config",
				details: {
					source: "gcp_ad",
					trigger_type: "scheduled",
					error: error.message,
				},
			});
		} finally {
			this.isRunning = false;
		}
	}

	stop(): void {
		if (this.syncJob) {
			this.syncJob.stop();
			this.syncJob = null;
			console.log("GCP AD scheduler stopped");
		}
	}

	getStatus(): { isScheduled: boolean; isRunning: boolean; nextRun: Date | null } {
		return {
			isScheduled: this.syncJob !== null && this.syncJob.isActive,
			isRunning: this.isRunning,
			nextRun: this.syncJob?.nextDate().toJSDate() || null,
		};
	}
}

export const gcpAdSchedulerService = new GcpAdSchedulerService();
