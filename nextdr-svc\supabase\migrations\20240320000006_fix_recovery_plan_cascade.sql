-- Fix foreign key constraints to enable CASCADE DELETE for recovery plans

-- 1. Drop existing foreign key constraint on recovery_steps_new
ALTER TABLE recovery_steps_new
DROP CONSTRAINT IF EXISTS recovery_steps_new_recovery_plan_id_fkey;

ALTER TABLE recovery_steps_new
DROP CONSTRAINT IF EXISTS recovery_steps_new_recovery_plan_id_fkey1;

-- 2. Add the constraint back with CASCADE DELETE
ALTER TABLE recovery_steps_new
ADD CONSTRAINT recovery_steps_new_recovery_plan_id_fkey
FOREIGN KEY (recovery_plan_id) REFERENCES recovery_plans_new(id) ON DELETE CASCADE;

-- 3. Ensure all related tables also have CASCADE DELETE for recovery_plan_id
-- Fix recovery_plan_progress
ALTER TABLE recovery_plan_progress
DROP CONSTRAINT IF EXISTS recovery_plan_progress_recovery_plan_id_fkey;

ALTER TABLE recovery_plan_progress
ADD CONSTRAINT recovery_plan_progress_recovery_plan_id_fkey
FOREIGN KEY (recovery_plan_id) REFERENCES recovery_plans_new(id) ON DELETE CASCADE;

-- Fix recovery_plan_checkpoints
ALTER TABLE recovery_plan_checkpoints
DROP CONSTRAINT IF EXISTS recovery_plan_checkpoints_recovery_plan_id_fkey;

ALTER TABLE recovery_plan_checkpoints
ADD CONSTRAINT recovery_plan_checkpoints_recovery_plan_id_fkey
FOREIGN KEY (recovery_plan_id) REFERENCES recovery_plans_new(id) ON DELETE CASCADE;

-- 4. Create indexes if they don't exist for better performance
CREATE INDEX IF NOT EXISTS idx_recovery_steps_recovery_plan_id ON recovery_steps_new(recovery_plan_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_progress_recovery_plan_id ON recovery_plan_progress(recovery_plan_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_checkpoints_recovery_plan_id ON recovery_plan_checkpoints(recovery_plan_id);
