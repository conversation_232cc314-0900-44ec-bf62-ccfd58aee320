#!/bin/bash
# ndrCommon.sh - A Bash module providing common install-related functions

# company/product naming
export gCOMPANY_NAME="orKestrate.Ai"
export gPRODUCT_NAME="NextDR"

# common
export gPrereqCheckComplete=0 # flag to indicate if prereq checks have been completed and not needed to be run again on subsequent interactive commands
export gNEXTDR_HOME_DIR="$PWD"
export gSCRIPT_HOME_DIR=$PWD
export gLOG_FILE=""
export gExpressMode=0
export gExpressOption=""
export gDebugMode=0

[[ -n "$_NDR_COMMON_LOADED" ]] && return
_NDR_COMMON_LOADED=1

# git
export NDR_GITHUB_REPO_USERNAME="kamlad"
export NDR_GITHUB_REPO_NAME="nextdr"
export NDR_GIT_REPO_BRANCH_OR_TAG="master" # could be a branch or tag
export NDR_GIT_LOCAL_REPO_DEST_DIR="" #"$HOME/dev/$NDR_GITHUB_REPO_NAME"
export NDR_GITHUB_REPO_URL="**************:$NDR_GITHUB_REPO_USERNAME/$NDR_GITHUB_REPO_NAME.git"
export NDR_GIT_SSH_KEY_USER_EMAIL="<EMAIL>"

export NDR_GIT_LOCAL_REPO_USER_NAME="jfutey"
export NDR_GIT_LOCAL_REPO_USER_EMAIL="<EMAIL>"

# ssh
export gSSH_Passphrase="nextdr!12"
export SSH_KEY_FILE_NAME="id_ed25519"
export SSH_KEY_DIR="$HOME/.ssh"
export SSH_KEY_FILE="$SSH_KEY_DIR/$SSH_KEY_FILE_NAME"
export SSH_PUB_KEY_FILE="${SSH_KEY_FILE}.pub"
export SSH_PUBLIC_KEY=""
export gSSH_KEY_AUTHENTICATED=0

# supabase variables
export supabaseCmd="npx supabase"

# Os type
export osTypeMajor=""
export distroFamily=""
export distroId=""

# docker repo
export NDR_DOCKER_REPO_ACCOUNT="jfutey"
export NDR_DOCKER_REPO_ACCESS_TOKEN="************************************"
export NDR_DOCKER_SERVICE_REPO_NAME="orkestrate-ai" #"orkestrate-svc"
export NDR_DOCKER_UI_REPO_NAME="orkestrate-ai" #"orkestrate-ui"

# docker image repo/storage type
export NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB=1 # this mode directs images to be uploaded to the docker hub repo
export NDR_DOCKER_IMAGE_REPO_TYPE_GIT=2 # this mode directs images to be uploaded to git
export NDR_DOCKER_IMAGE_REPO_TYPE=$NDR_DOCKER_IMAGE_REPO_TYPE_GIT # current default image upload mode -- change as needed in production.

# docker app build enum
export NDR_DOCKER_APP_MANAGE_OPTIONS_NONE
export NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE=0x1 # build/remove image only.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER=0x2 # build/remove container only. In build mode, does not interactively prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL=0x4 # Optional flag for container. In build mode, this triggers an interactive prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT=0x8 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT=0x10 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_BUILD_IMAGE_AND_CONTAINER=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )) # build image and container (does not prompt for container creation).
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT )) # everything.
export NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL )) # standard behavior of create image, then prompt for optional operations such as module preclean and continer build/start.

# docker service app
export NDR_SERVICE_HOME_LOC="nextdr-svc"
export NDR_SERVICE_IMAGE_NAME="nextdr-svc-img"
export NDR_SERVICE_IMAGE_VERSION="latest"
export NDR_SERVICE_CONTAINER_NAME="nextdr-svc-app"
export NDR_SERVICE_CONTAINER_PORT="8081"

# docker ui app
export NDR_UI_HOME_LOC="nextdr-ui"
export NDR_UI_IMAGE_NAME="nextdr-ui-img"
export NDR_UI_IMAGE_VERSION="latest"
export NDR_UI_CONTAINER_NAME="nextdr-ui-app"
export NDR_UI_CONTAINER_PORT="80"

# older version of Docker
dockerRemovePkgs=(
  docker.io
  docker-doc
  docker-compose
  docker-compose-v2
  podman-docker
  containerd
  runc
)

# newer version of Docker
dockerInstallPackages=(
  "docker-ce" \
  "docker-ce-cli" \
  "containerd.io" \
  "docker-buildx-plugin" \
  "docker-compose-plugin"
)
  
  
# ANSI color codes or logging
RESET="\033[0m"
BOLD="\033[1m"
RED="\033[31m"
YELLOW="\033[33m"
TEAL="\033[36m"
GREEN="\033[32m"
BLUE="\033[34m"

# ----------------------

# --- PROTOS ---

# Function prototypes

#ndr_logSecStart()                # (section)
#ndr_logSecEnd()                  # (section)
#ndr_logInfo()                    # ([section], message)
#ndr_logWarn()                    # ([section], message)
#ndr_logError()                   # ([section], message)
#ndr_osTypeCheck                  # ()
#ndr_parseCommandLineArgs         # ([args...])
#ndr_checkAndInstallDocker        # ()
#ndr_checkAndInstallPrerequisites # ([checkOnly])
#ndr_packagePrereqCheck           # ()
#ndr_removeDockerPackages         # ()
#ndr_verifyDockerImageExists      # (image:tag)
#ndr_verifyDockerContainerExists  # (container_name)
#ndr_cleanupDockerImage           # (image_name)
#ndr_cleanupDockerContainer       # (container_name)
#ndr_mainCleanupServiceApplication # ()
#ndr_mainCleanupUIApplication     # ()
#ndr_cleanupDockerApplication     # (folder, image_name, image_version, container_name)
#ndr_buildDockerApplicationContainer # (folder, image_name, image_version, container_name, container_port)

# --- FUNCTIONS ---

function ndr_logSecStart () 
{
  echo -e "${BLUE}+++ START [${RESET} $1 ${BLUE}] +++${RESET}"
}

function ndr_logSecEnd () 
{
  echo -e "${GREEN}--- COMPLETE [${RESET} $1 ${GREEN}] ---${RESET}"
}

function _log_format () 
{
  local caller="$1" # optional, can be blank.
  local color="$2" # required
  local level="$3" # required
  local message="$4" # required

  local datetime="[$(date '+%Y-%m-%d %H:%M:%S')]"
  
  if [[ -n "$caller" ]]; then
    caller=" $caller"
  fi

  echo -e "${datetime}${caller} ${color}${level}${RESET} ${message}"
}

function ndr_logInfo () 
{
  #echo -e "\033[36mℹ️ INFO: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$TEAL" "ℹ️ INFO" "$1"
}

function ndr_logWarn () 
{
  #echo -e "\033[33m⚠️ WARNING: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$YELLOW" "⚠️ WARNING" "$1"
}

function ndr_logError () 
{
  #echo -e "\033[31m❌ ERROR: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$RED" "❌ ERROR" "$1"
}

# Usage: function <value> <flag>
# Returns 0 (true) if the flag is set, 1 (false) otherwise
function ndr_checkFlag () 
{
  local value="$1"
  local flag="$2"

  if (( (value & flag) != 0 )); then
    return 0  # flag is set
  else
    return 1  # flag is not set
  fi
}

function ndr_osTypeCheck () 
{
  local logSectionDesc="Operating system type check"
  ndr_logSecStart "$logSectionDesc"

  os_type=$(uname -s)
  case "$os_type" in
    Linux)
      osTypeMajor="Linux"
      supabaseCmd="npx supabase"
      ;;
    unix*)
      osTypeMajor="Unix"
      supabaseCmd="npx supabase"
      ;;
    Windows*)
      osTypeMajor="Windows"
      supabaseCmd="npx supabase"
      ;;
    *NT*)
      osTypeMajor="Windows"
      supabaseCmd="npx supabase"
      ;;
    *)
      osTypeMajor="Unknown"
      ;;
  esac

  ndr_logInfo "Operating system is [$osTypeMajor], type [$os_type | $OSTYPE]"
  
  if [[ "$osTypeMajor" == "Windows" ]]; then
    distroFamily="Windows"
    distroId="Windows"
    ndr_logSecEnd "$logSectionDesc"
    return 0
  fi

  # Try to read from /etc/*release
  if grep -q "^ID=" /etc/*release 2>/dev/null; then
    distroId=$(grep "^ID=" /etc/*release | head -n1 | cut -d= -f2 | tr -d '"' | tr '[:upper:]' '[:lower:]')
  elif [[ "$(uname)" == "Darwin" ]]; then
    distroFamily="osx"
    distroId="Darwin"
  else
    ndr_logError "Unable to detect distribution."
    return 1
  fi

  # Map to main distro families
  case "$distroId" in
    ubuntu|debian|linuxmint|elementary|pop|zorin)
      distroFamily="debian"
      ;;
    rhel|redhat|centos|fedora|rocky|almalinux|scientific)
      distroFamily="redhat"
      ;;
    arch|manjaro|endeavouros)
      distroFamily="arch"
      ;;
    opensuse|suse|sles|opensuse-tumbleweed|suse-leap|opensuse-leap|suse-sles)
      distroFamily="suse"
      ;;
    alpine)
      distroFamily="alpine"
      ;;
    osx|darwin)
      distroFamily="osx"
      ;;
    *)
      distroFamily="unknown"
      ;;
  esac

  ndr_logInfo "Detected distro: $distroId, family: $distroFamily."

  # set os/disto specific commands

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_parseCommandLineArgs ()
{
  local logSectionDesc="Parsing common command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Enable logging only if gLOG_FILE is non-empty
  if [[ -n "$gLOG_FILE" ]]; then
    exec > >(tee -a "$gLOG_FILE") 2>&1
  fi

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --express|-e)
        if [[ -n "$2" && "$2" != --* ]]; then
          gExpressMode=1
          gExpressOption="$2"
          ndr_logInfo "Express option set to [$gExpressOption]"
          shift 2
        else
          ndr_logError "--express requires a value."
          return 1
        fi
        ;;
      --debug)
        gDebugMode=1
        shift
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_checkAndInstallDocker ()
{
  # -------------------------------------
  # cleanup old docker packages

  local logSectionDesc="Conflicting Docker package removal"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"
  
  # -------------------------------------
  # 🚮 Remove any stale Docker APT source files

  local logSectionDesc="🧹 Cleaning up old Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Remove docker.list if it exists
  docker_list="/etc/apt/sources.list.d/docker.list"
  if [[ -f "$docker_list" ]]; then
    sudo rm -f "$docker_list"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "file $docker_list not removed, please manually remove file before proceeding."
      return 1
    fi
    ndr_logInfo "🗑️ Removed stale Docker source file."
  fi

  # Optional: grep and warn about any other Docker-related entries
  if grep -r "download.docker.com" /etc/apt/sources.list.d/ > /dev/null; then
    ndr_logWarn "Found other Docker source entries in [/etc/apt/sources.list.d/]."
  fi

  if grep -q "download.docker.com" /etc/apt/sources.list; then
    ndr_logWarn "Found Docker source entry in [/etc/apt/sources.list]  You may want to clean that manually.033[0m"
  fi

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # Add Docker's official GPG key:

  local logSectionDesc="Adding Official Docker GPG Key"
  ndr_logSecStart "$logSectionDesc"

  sudo apt-get update
  pkgs=(
    ca-certificates
    curl
  )
  # Loop through each package and install it
  for pkg in "${pkgs[@]}"; do
    ndr_logInfo "💾 Installing $pkg..."
    sudo apt-get install --assume-yes "$pkg"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "$pkg package not installed, please manually install package before proceeding."
      return 1
    fi
  done

  local cmds=(
    "sudo install -m 0755 -d /etc/apt/keyrings" \
    "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
    "sudo chmod a+r /etc/apt/keyrings/docker.asc"
    )
  
  for cmd in "${cmds[@]}"; do
    ndr_logInfo "💾 Executing command [$cmd]..."
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Command [$cmd] failure, please run manually before proceeding."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # add deb line to apt sources list

  local logSectionDesc="Adding Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"
  
  # single line execution
  #echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list

  # multi line execution
  # Step 1: Get system architecture
  arch=$(dpkg --print-architecture)

  # Step 2: Get Ubuntu codename (e.g., focal, jammy)
  . /etc/os-release
  codename="${UBUNTU_CODENAME:-$VERSION_CODENAME}"

  # Step 3: Construct the deb line
  deb_line="deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable"

  # Step 4: Write to APT sources list with sudo 
  apt_source_file="/etc/apt/sources.list.d/docker.list"
  echo "$deb_line" | sudo tee "$apt_source_file" > /dev/null
  
  ndr_logInfo "Adding deb line [$deb_line] to list file [$apt_source_file]."

  sudo apt-get update

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # verify apt sources

  local logSectionDesc="Verifying Docker APT source entries"
  ndr_logSecStart "$logSectionDesc"

  # Step 1: Verify the deb line exists
  if [[ ! -f "$apt_source_file" ]]; then
    ndr_logError "APT source file not found: [$apt_source_file]."
    return 1
  fi

  if grep -Fxq "$deb_line" "$apt_source_file"; then
    ndr_logInfo "Docker APT source entry is present."
  else
    ndr_logError "Docker APT source entry is missing or incorrect."
    return 1
  fi

  # Step 2: Update APT
  ndr_logInfo "🔄 Running apt-get update..."
  sudo apt-get update -qq
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "apt-get update failed."
    return 1
  fi

  # Step 3: Check for Docker packages
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Checking if Docker package [$pkg] is available..."
    repo_line=$(apt-cache policy "$pkg" 2>/dev/null | grep "https://download.docker.com/linux/ubuntu $codename")
    return_code=$?
    if [[ $return_code -eq 0 && -n "$repo_line" ]]; then
      ndr_logInfo "✅ Docker package [$pkg] is available from Docker's APT repository."
    else
      ndr_logError "Docker package [$pkg] not found in the expected repository."
      return 1
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  # -------------------------------------
  # install docker packages
  
  local logSectionDesc="Installing Docker packages"
  ndr_logSecStart "$logSectionDesc"

  #sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
  for pkg in "${dockerInstallPackages[@]}"; do
    ndr_logInfo "🔍 Installing Docker engine package [$pkg]..."
    cmd="sudo apt-get install --assume-yes $pkg"
    $cmd
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to install Docker package [$pkg]."
      return 1
    else
      ndr_logInfo "✅ Docker package [$pkg] installed successfully."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_checkAndInstallPrerequisites () 
{
  local logSectionDesc="Checking prerequisites"
  ndr_logSecStart "$logSectionDesc"

  if [[ $gPrereqCheckComplete -eq 1 ]]; then
    ndr_logInfo "Prerequesite check complete, skipping."
    return 0
  fi

  local checkOnlyFlag=false
  if [[ "$1" == "checkOnly" ]]; then
    checkOnlyFlag=true
  fi

  local prereqPackages=("gawk" \
                        "npm" \
                        "npx" \
                        "git" \
                        "docker")

  # assume all packages are installed by default, then check one by one.
  local packageInstalled=true
  for pkg in "${prereqPackages[@]}"; do
    if ! command -v "$pkg" &> /dev/null; then
      ndr_logWarn "$pkg is not installed."
      # indicate that the package is not installed
      packageInstalled=false

      if [[ "$checkOnlyFlag" == "true" ]]; then
        continue
      fi

      
      read -p "Would you like to install $pkg now? [Y/n] " reply
      if [[ -z "$reply" || "$reply" =~ ^[Yy] ]]; then
        ndr_logInfo "Installing [$pkg] package..."
        case "$pkg" in
           awk|gawk)
            packageURL="https://www.gnu.org/software/gawk/manual/gawk.html"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt-get install --assume-yes gawk"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install gawk"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm gawk"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install gawk"
            fi
            
            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          npm|npx)
            packageURL="https://docs.npmjs.com/downloading-and-installing-node-js-and-npm"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt install --assume-yes npm"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install nodejs"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm nodejs"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install node"
            fi
            
            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          git)
            packageURL="https://git-scm.com/downloads"
            
            if [[ "$distroFamily" == "debian" ]]; then
              cmd="sudo apt install --assume-yes git"
            elif [[ "$distroFamily" == "redhat" ]]; then
              cmd="sudo dnf install git"
            elif [[ "$distroFamily" == "suse" ]]; then
              cmd="sudo zypper install --no-confirm git"
            elif [[ "$distroFamily" == "osx" ]]; then
              cmd="brew install git"
            fi

            $cmd
            return_code=$?
            if [ $return_code != 0 ]; then
              ndr_logError "[$cmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
              return 1
            fi
            ndr_logInfo "$pkg package installed successfully."
            ;;

          docker)
            packageURL="https://docs.docker.com/engine/install/"
            if [[ "$distroFamily" == "debian" ]]; then
              local installCmds=(
                ndr_checkAndInstallDocker
              )
            elif [[ "$distroFamily" == "debianOLD" ]]; then
              local installCmds=(
                    "sudo apt-get install --assume-yes gnome-terminal"
                    "sudo apt-get update" \
                    "sudo apt-get install --assume-yes ca-certificates" \
                    "sudo apt-get install --assume-yes curl" \
                    "sudo install -m 0755 -d /etc/apt/keyrings" \
                    "sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc" \
                    "sudo chmod a+r /etc/apt/keyrings/docker.asc" \
                    #"echo deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo ${UBUNTU_CODENAME:-$VERSION_CODENAME}) stable | sudo tee /etc/apt/sources.list.d/docker.list"
                    # Step 1: Get system architecture
                    "arch=$(dpkg --print-architecture)" \
                    # Step 2: Get Ubuntu codename (e.g., focal, jammy)
                    ". /etc/os-release" \
                    "codename=\"${UBUNTU_CODENAME:-$VERSION_CODENAME}\"" \
                    # Step 3: Construct the deb line
                    "deb_line=\"deb [arch=$arch signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $codename stable\"" \
                    # Step 4: Write to APT sources list with sudo
                    "echo \"$deb_line\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null" \
                    "sudo apt-get update" \
                    "sudo apt-get install --assume-yes docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin")
            elif [[ "$distroFamily" == "redhat" ]]; then
              local installCmds=(
                    "sudo dnf install gnome-terminal" \
                    "sudo dnf -y install dnf-plugins-core" \
                    "sudo dnf-3 config-manager --add-repo https://download.docker.com/linux/fedora/docker-ce.repo" \
                    "sudo dnf install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin" \
                    "sudo systemctl enable --now docker")
            elif [[ "$distroFamily" == "suse" ]]; then
              local installCmds=(
                    "sudo zypper addrepo https://download.opensuse.org/repositories/Virtualization:containers/openSUSE_Tumbleweed_and_d_l_g/Virtualization:containers.repo" \
                    "sudo zypper addrepo https://download.docker.com/linux/sles/docker-ce.repo" \
                    "sudo zypper refresh" \
                    "sudo zypper install docker-compose" \
                    "sudo systemctl enable docker.service" \
                    "sudo usermod -aG docker $USER" \
                    "sudo systemctl start docker.service" \
                    "sudo zypper install docker")
            fi

            for installCmd in "${installCmds[@]}"; do
            ndr_logInfo "Executing installation command [$installCmd]..."
              $installCmd
              return_code=$?
              if [ $return_code != 0 ]; then
                ndr_logError "[$installCmd] failure, $pkg package not installed, please manually install package before proceeding ($packageURL)"
                # skip apt-get command failures
                if [[ "$installCmd" == *"apt-get update"* || "$installCmd" == *addrepo* ]]; then
                  continue
                fi
                return 1
              fi
            done
            
            ndr_logInfo "$pkg package installed successfully."            
            ;;
          *)
            ndr_logWarn "Unknown package [$pkg] "
            continue
            ;;
        esac

        # indicate that the package is now successfully installed
        packageInstalled=true  
      else
        ndr_logWarn "Skipping installation of $pkg."
      fi
    else
      ndr_logInfo "✅ $pkg is installed."
      # indicate that the package was already installed
      packageInstalled=true
    fi
  done

  # the prereq check complete flag needs to be qualfied by all packages installed before setting
  if [ $packageInstalled == true ]; then
    ndr_logInfo "All required packages are installed."
    gPrereqCheckComplete=1
  else
    ndr_logError "Some required packages are not installed. Please install them before proceeding."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Running Supabase locally in a Docker container
# to create build env prerequsiites, some prerequisites must be interactively installed before the scripting can proceed.
# https://supabase.com/docs/guides/self-hosting/docker
# 1. install NPM/NPX (https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)
# 2. install GIT (https://git-scm.com/downloads)
# 3. install docker (https://docs.docker.com/desktop/setup/install/windows-install/ or https://docs.docker.com/desktop/setup/install/linux-install/)
# 4. create docker account and log in (docker desktop and pulls will not function without this). With the Docker UI open, you can observe the images and containers getting created and onlined in real time.
function ndr_packagePrereqCheck ()
{
  ndr_checkAndInstallPrerequisites checkOnly
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Package prereq only check failed."
    return 1
  fi

  return 0
}

function ndr_removeDockerPackages ()
{
  local logSectionDesc="Cleaning up packages"
  ndr_logSecStart "$logSectionDesc"

  # Loop through each package and remove it
  for pkg in "${dockerRemovePkgs[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  # Loop through each package and remove it
  for pkg in "${dockerInstallPackages[@]}"; do
    if dpkg -l | grep -q "$pkg"; then
      ndr_logInfo "🗑️ Removing $pkg..."
      sudo apt-get remove --assume-yes "$pkg"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "$pkg package not removed, please manually remove package before proceeding."
        return 1
      fi
    else
      ndr_logInfo "$pkg is not installed, skipping."
    fi
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# returns 2 for usage or general error
# returns 1 for image not found
# returns 0 for image found
# usage: function <image:tag>
function ndr_verifyDockerImageExists () 
{
  local logSectionDesc="Verifying Docker Image Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  local image_name="$1"
  if [[ -z "$image_name" ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerImageExists <image:tag>"
    return 2
  fi

  # Run command and capture both output and return status
  while true; do
    
    local output
    #output=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -Fx "$image_name")
    output=$(docker images --format "{{.Repository}}:{{.Tag}}")
    return_code=$?

    # Check if docker failed
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to query for Docker images."
      retVal=2
      break
    fi

    # check if output is empty (no images at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker images found."
      retVal=1
      break
    fi

    #if [[ -n "$output" ]]; then
    #  ndr_logInfo "✅ Docker image '${image_name}' exists."
    #  retVal=0
    #else
    #  ndr_logWarn "No matching image output returned for '${image_name}'."
    #  retVal=1
    #fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$image_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker image '${image_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching image output returned for '${image_name}'."
      retVal=1
    fi
    
    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# returns 2 for usage or general error
# returns 1 for container not found
# returns 0 for container found
# usage: function <container_name>
function ndr_verifyDockerContainerExists () 
{
  local logSectionDesc="Verifying Docker Container Exists"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  if [[ $# -lt 1 ]]; then
    ndr_logWarn "Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  local container_name="$1"
  if [[ -z "$container_name" ]]; then
    echo "❌ Usage: ndr_verifyDockerContainerExists <container_name>"
    return 2
  fi

  # Capture the output and return code separately
  while true; do
    
    local output
    #output=$(docker ps -a --format '{{.Names}}' | grep -Fx "$container_name")
    output=$(docker ps -a --format '{{.Names}}')
    local status=$?

    # Check if docker failed
    if [[ $status -ne 0 ]]; then
      ndr_logError "Failed to query for Docker containers."
      retVal=2
      break
    fi

    # check if output is empty (no containers at all present)
    if [[ -z "$output" ]]; then
      ndr_logInfo "No Docker containers found."
      retVal=1
      break
    fi

    # there is output, check for target item.
    echo "$output" | grep -Fx "$container_name"
    return_code=$?
    if [[ $return_code -eq 0 ]]; then
      ndr_logInfo "✅ Docker container '${container_name}' exists."
      retVal=0
    else
      ndr_logWarn "No matching container output returned for '${container_name}'."
      retVal=1
    fi

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return $retVal
}

# usage: function <image_name> [build_mode_options]
function ndr_cleanupDockerImage() 
{
  local logSectionDesc="Cleaning up Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do

    local image_name="$1"
    if [[ -z "$image_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerImage <image_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker image '${image_name}' not found, no cleanup needed."
      retVal=0
      break
    fi
    
    # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker image '${image_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker image removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🧹 Forcing removal of image '${image_name}'..."
    docker image rm -f "$image_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove image '${image_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker image '${image_name}' removal command succeeded."

    docker buildx prune -f
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logWarn "Failed to prune dangling docker build cache entries."
    fi
    ndr_logInfo "Successfully pruned dangling docker build cache entries."

    ndr_verifyDockerImageExists "$image_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      # image still exists after removal attempt
      ndr_logError "Error, image still present after removal '${image_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Image '${image_name}' successfully removed and verified."

    break
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function  <image_name> [build_mode_options]
function ndr_cleanupDockerContainer ()
{
  local logSectionDesc="Cleaning up Docker Container"
  ndr_logSecStart "$logSectionDesc"

  local retVal=0

  while true; do
    local container_name="$1"
    
    if [[ -z "$container_name" ]]; then
      ndr_logWarn "Usage: ndr_cleanupDockerContainer <container_name> [build_mode_options]"
      retVal=2
      break
    fi

    local dockerAppManageOptions="${2:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code != 0 ]]; then
      # skip if not found
      ndr_logInfo "Docker container '${container_name}' not found, nothing to remove."
      retVal=0
      break
    fi

     # if option to remove without prompting is present, skip interactive prompt and remove.
    ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logInfo "Existing Docker container '${container_name}' found. Removing it will erase it permanently."
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker container removal."
        retVal=0
        break
      fi
    fi

    ndr_logInfo "🛑 Stopping container '${container_name}'..."

    docker stop "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logWarn "Failed to stop Docker container '${container_name}'."
      #retVal=1
      #break
    fi

    ndr_logInfo "🧹 Removing container '${container_name}'..."
    
    docker container rm -f "$container_name"
    return_code=$?
    if [[ $return_code != 0 ]]; then
      ndr_logError "Failed to remove Docker container '${container_name}'."
      retVal=1
      break
    fi
    ndr_logInfo "Docker container removal command succeeded."

    ndr_verifyDockerContainerExists "$container_name"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [[ $return_code -eq 0 ]]; then
      ndr_logError "Error, container still present after removal '${container_name}'."
      retVal=1
      break
    fi

    ndr_logInfo "✅ Container '${container_name}' successfully removed and verified."

    break
  done
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupServiceApplication ()
{
  local logSectionDesc="Cleanup Service Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_SERVICE_HOME_LOC
  local dockerImageName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerContainerName=$NDR_SERVICE_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function ndr_mainCleanupUIApplication ()
{
  local logSectionDesc="Cleanup UI Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder=$NDR_UI_HOME_LOC
  local dockerImageName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerContainerName=$NDR_UI_CONTAINER_NAME
  
  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL}"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application [$dockerContainerName]."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> [build_mode_options]
function ndr_cleanupDockerApplication ()
{
  local logSectionDesc="Cleanup Docker Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName="$4"
  #local dockerContainerURL="http://localhost:$dockerContainerPort"

  local dockerAppManageOptions="${5:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: ndr_cleanupDockerApplication <folder> <image_name> <image_version> <container_name>"
    return 1
  fi

  # move from the install directory to the container/module directory
  cd "$gSCRIPT_HOME_DIR/../$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }

  # check if the Dockerfile exists
  if [[ ! -f Dockerfile ]]; then
    ndr_logWarn "Dockerfile not found in $containerFolder directory."
    #return 1
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )); then
    # remove any existing Docker container with the same name
    ndr_cleanupDockerContainer "$dockerContainerName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker container."
      return 1
    fi
  fi

  if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE )); then
    # remove any existing image with the same name
    ndr_cleanupDockerImage "$dockerImageName" "$dockerAppManageOptions"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to remove existing Docker image."
      return 1
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> <container_port> [build_mode_options]
function ndr_buildDockerApplicationContainer ()
{
  local logSectionDesc="Building Docker Application Container"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName=$4
  local dockerContainerPort=$5
  local dockerContainerURL="http://localhost:$dockerContainerPort"

  local dockerAppManageOptions="${6:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 5 ]]; then
    ndr_logError "Usage: buildDockerApplication <folder> <image_name> <image_version> <container_name> <container_port>"
    return 1
  fi

  # move from the install directory to the container/module directory
  cd "$gSCRIPT_HOME_DIR/../$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }
  
  # check if the Dockerfile exists
  if [[ ! -f Dockerfile ]]; then
    ndr_logError "Dockerfile not found in $containerFolder directory."
    return 1
  fi

  dockerAppManageOptions=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT ))
  # remove any existing Docker container with the same name
  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application container."
    return 1
  fi

  # run the Docker container
  docker run -d --name "$dockerContainerName" -p "$dockerContainerPort:$dockerContainerPort" "$dockerImageName"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to run Docker container [$dockerContainerName]."
    return 1
  fi

  # check if the newly built Docker container exists
  ndr_verifyDockerContainerExists "$dockerContainerName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker container verification failed."
    return 1
  fi
  # check if the container is running
  docker ps -a | grep "$dockerContainerName" | grep "Up"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is not running."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running."

  # check if the container is running on specified container port
  docker ps -a | grep "$dockerContainerName" | grep "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker container [$dockerContainerName] is NOT running on port $dockerContainerPort."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] is running on port $dockerContainerPort."
  
  # execute curl command to check if the container is running
  curl -s -o /dev/null -w "%{http_code}" "$dockerContainerURL"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to access the container at $dockerContainerURL"
    return 1
  fi
  ndr_logInfo "Successfully accessed the container at $dockerContainerURL"
  
  ndr_logInfo "Docker image [$dockerImageName] is built and ready to use."
  ndr_logInfo "Docker container [$dockerContainerName] is running and accessible."
  
  ndr_logInfo "You can access the container at $dockerContainerURL"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
