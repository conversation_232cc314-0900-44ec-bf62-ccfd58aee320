#!/bin/bash
# ndrCommon.sh - A Bash module providing common definitions and functions

# company/product naming
export gCOMPANY_NAME="orKestrate.Ai"
export gPRODUCT_NAME="NextDR"
export gPRODUCT_NAME_SHORT="ndr"
export gPRODUCT_VERSION="v1.0"

# registry
export gREGISTRY_DIR="/var/lib/$gPRODUCT_NAME"
export gREGISTRY_FILE="$gREGISTRY_DIR/registry.json"
export gREGISTRY_ENTRY_VERSION="version"
export gREGISTRY_ENTRY_INSTALL_DATE="install_date"
export gREGISTRY_ENTRY_UPDATE_DATE="update_date"
export gREGISTRY_ENTRY_HOME_DIR="home_dir"

export gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME="module_status_supabase"
export gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME="module_status_service"
export gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME="module_status_ui"
export gREGISTRY_ENTRY_MODULE_STATUS_INSTALLED="installed"
export gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED="not_installed"

export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICES="supabase_docker_services"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_NAME="service_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_CONTAINER_NAME="container_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_IMAGE_NAME="image_name"
export gREGISTRY_ENTRY_MODULE_SUPABASE_SECTION_DOCKER_SERVICE_IMAGE_TAG="image_tag"

# env files
export gNDR_ENV_MASTER_FILENAME=".env.nextdr" # master environment file for NextDR
export gNDR_ENV_MODULE_FILENAME=".env"

# supabase
# Global arrays
declare -a ndr_supabase_container_services=()
declare -A ndr_supabase_container_service_container_names=()
declare -A ndr_supabase_container_service_image_names=()
declare -A ndr_supabase_container_service_image_tags=()


# common
export gPrereqCheckComplete=0 # flag to indicate if prereq checks have been completed and not needed to be run again on subsequent interactive commands
export gNEXTDR_HOME_DIR="/opt/$gPRODUCT_NAME_SHORT/home" #"$PWD"
export gSCRIPT_HOME_DIR="$PWD"
export gLOG_FILE=""
export gExpressMode=0
export gExpressOption=""
export gDebugMode=0

[[ -n "$_NDR_COMMON_LOADED" ]] && return
_NDR_COMMON_LOADED=1

# git
export NDR_GITHUB_REPO_USERNAME="kamlad"
export NDR_GITHUB_REPO_NAME="nextdr"
export NDR_GIT_REPO_BRANCH_OR_TAG="master" # could be a branch or tag
export NDR_GIT_LOCAL_REPO_DEST_DIR="" #"$HOME/dev/$NDR_GITHUB_REPO_NAME"
export NDR_GITHUB_REPO_URL="**************:$NDR_GITHUB_REPO_USERNAME/$NDR_GITHUB_REPO_NAME.git"
export NDR_GIT_SSH_KEY_USER_EMAIL="<EMAIL>"

export NDR_GIT_LOCAL_REPO_USER_NAME="jfutey"
export NDR_GIT_LOCAL_REPO_USER_EMAIL="<EMAIL>"

# ssh
export gSSH_Passphrase="nextdr!12"
export SSH_KEY_FILE_NAME="id_ed25519"
export SSH_KEY_DIR="$HOME/.ssh"
export SSH_KEY_FILE="$SSH_KEY_DIR/$SSH_KEY_FILE_NAME"
export SSH_PUB_KEY_FILE="${SSH_KEY_FILE}.pub"
export SSH_PUBLIC_KEY=""
export gSSH_KEY_AUTHENTICATED=0

# supabase variables
export gSUPABASE_CLI_CMD="npx supabase"

# Os type
export osTypeMajor=""
export distroFamily=""
export distroId=""

# docker repo
export NDR_DOCKER_REPO_ACCOUNT="jfutey"
export NDR_DOCKER_REPO_ACCESS_TOKEN="************************************"
export NDR_DOCKER_SERVICE_REPO_NAME="orkestrate-ai" #"orkestrate-svc"
export NDR_DOCKER_UI_REPO_NAME="orkestrate-ai" #"orkestrate-ui"

# docker image repo/storage type
export NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB=1 # this mode directs images to be uploaded to the docker hub repo
export NDR_DOCKER_IMAGE_REPO_TYPE_GIT=2 # this mode directs images to be uploaded to git
export NDR_DOCKER_IMAGE_REPO_TYPE=$NDR_DOCKER_IMAGE_REPO_TYPE_GIT # current default image upload mode -- change as needed in production.

# docker bridge network
export NDR_DOCKER_BRIDGE_NETWORK_NAME="ndr_bridge_net"

# docker app build enum
export NDR_DOCKER_APP_MANAGE_OPTIONS_NONE
export NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE=0x1 # build/remove image only.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER=0x2 # build/remove container only. In build mode, does not interactively prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL=0x4 # Optional flag for container. In build mode, this triggers an interactive prompt for container creation.
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT=0x8 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT=0x10 # remove existing container without prompting.
export NDR_DOCKER_APP_MANAGE_OPTIONS_BUILD_IMAGE_AND_CONTAINER=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )) # build image and container (does not prompt for container creation).
export NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT )) # everything.
export NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL )) # standard behavior of create image, then prompt for optional operations such as module preclean and continer build/start.

# docker service app
export NDR_SERVICE_HOME_LOC="nextdr-svc"
export NDR_SERVICE_IMAGE_NAME="nextdr-svc-img"
export NDR_SERVICE_IMAGE_VERSION="latest" #"1.0.0"
export NDR_SERVICE_CONTAINER_NAME="nextdr-svc-app"
export NDR_SERVICE_CONTAINER_PORT="8081"

# docker ui app
export NDR_UI_HOME_LOC="nextdr-ui"
export NDR_UI_IMAGE_NAME="nextdr-ui-img"
export NDR_UI_IMAGE_VERSION="latest" #"1.0.0"
export NDR_UI_CONTAINER_NAME="nextdr-ui-app"
export NDR_UI_CONTAINER_PORT="80"

# older version of Docker
dockerRemovePkgs=(
  docker.io
  docker-doc
  docker-compose
  docker-compose-v2
  podman-docker
  containerd
  runc
)

# newer version of Docker
dockerInstallPackages=(
  "docker-ce" \
  "docker-ce-cli" \
  "containerd.io" \
  "docker-buildx-plugin" \
  "docker-compose-plugin"
)
  
  
# ANSI color codes or logging
RESET="\033[0m"
BOLD="\033[1m"
RED="\033[31m"
YELLOW="\033[33m"
TEAL="\033[36m"
GREEN="\033[32m"
BLUE="\033[34m"

# ----------------------

# --- PROTOS ---

# Function prototypes


# --- FUNCTIONS ---

function ndr_logSecStart () 
{
  echo -e "${BLUE}+++ START [${RESET} $1 ${BLUE}] +++${RESET}"
}

function ndr_logSecEnd () 
{
  echo -e "${GREEN}--- COMPLETE [${RESET} $1 ${GREEN}] ---${RESET}"
}

function _log_format () 
{
  local caller="$1" # optional, can be blank.
  local color="$2" # required
  local level="$3" # required
  local message="$4" # required

  local datetime="[$(date '+%Y-%m-%d %H:%M:%S')]"
  
  if [[ -n "$caller" ]]; then
    caller=" $caller"
  fi

  echo -e "${datetime}${caller} ${color}${level}${RESET} ${message}"
}

function ndr_logInfo () 
{
  #echo -e "\033[36mℹ️ INFO: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$TEAL" "ℹ️ INFO" "$1"
}

function ndr_logWarn () 
{
  #echo -e "\033[33m⚠️ WARNING: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$YELLOW" "⚠️ WARNING" "$1"
}

function ndr_logError () 
{
  #echo -e "\033[31m❌ ERROR: $1\033[0m"
  local caller_info=""
  if [[ "$gDebugMode" -eq 1 ]]; then
    caller_info="${BASH_SOURCE[1]}::${FUNCNAME[1]:-main}(${BASH_LINENO[0]})"
  fi
  
  _log_format "$caller_info" "$RED" "❌ ERROR" "$1"
}

# Usage: function <value> <flag>
# Returns 0 (true) if the flag is set, 1 (false) otherwise
function ndr_checkFlag () 
{
  local value="$1"
  local flag="$2"

  if (( (value & flag) != 0 )); then
    return 0  # flag is set
  else
    return 1  # flag is not set
  fi
}

function ndr_osTypeCheck () 
{
  local logSectionDesc="Operating system type check"
  ndr_logSecStart "$logSectionDesc"

  os_type=$(uname -s)
  case "$os_type" in
    Linux)
      osTypeMajor="Linux"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    unix*)
      osTypeMajor="Unix"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    Windows*)
      osTypeMajor="Windows"
      gSUPABASE_CLI_CMD="npx supabase"
      ;;
    *NT*)
      osTypeMajor="Windows"
      gSUPABASE_CLI_CMD="sudo npx supabase"
      ;;
    *)
      osTypeMajor="Unknown"
      ;;
  esac

  ndr_logInfo "Operating system is [$osTypeMajor], type [$os_type | $OSTYPE]"
  
  if [[ "$osTypeMajor" == "Windows" ]]; then
    distroFamily="Windows"
    distroId="Windows"
    ndr_logSecEnd "$logSectionDesc"
    return 0
  fi

  # Try to read from /etc/*release
  if grep -q "^ID=" /etc/*release 2>/dev/null; then
    distroId=$(grep "^ID=" /etc/*release | head -n1 | cut -d= -f2 | tr -d '"' | tr '[:upper:]' '[:lower:]')
  elif [[ "$(uname)" == "Darwin" ]]; then
    distroFamily="osx"
    distroId="Darwin"
  else
    ndr_logError "Unable to detect distribution."
    return 1
  fi

  # Map to main distro families
  case "$distroId" in
    ubuntu|debian|linuxmint|elementary|pop|zorin)
      distroFamily="debian"
      ;;
    rhel|redhat|centos|fedora|rocky|almalinux|scientific)
      distroFamily="redhat"
      ;;
    arch|manjaro|endeavouros)
      distroFamily="arch"
      ;;
    opensuse|suse|sles|opensuse-tumbleweed|suse-leap|opensuse-leap|suse-sles)
      distroFamily="suse"
      ;;
    alpine)
      distroFamily="alpine"
      ;;
    osx|darwin)
      distroFamily="osx"
      ;;
    *)
      distroFamily="unknown"
      ;;
  esac

  ndr_logInfo "Detected distro: $distroId, family: $distroFamily."

  # set os/disto specific commands

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_parseCommandLineArgs ()
{
  local logSectionDesc="Parsing common command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Enable logging only if gLOG_FILE is non-empty
  if [[ -n "$gLOG_FILE" ]]; then
    #exec > >(tee -a "$gLOG_FILE") 2>&1
    exec &> >(tee -a "$gLOG_FILE")
  fi

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --express|-e)
        if [[ -n "$2" && "$2" != --* ]]; then
          gExpressMode=1
          gExpressOption="$2"
          ndr_logInfo "Express option set to [$gExpressOption]"
          shift 2
        else
          ndr_logError "--express requires a value."
          return 1
        fi
        ;;
      --debug)
        gDebugMode=1
        shift
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


# Running Supabase locally in a Docker container
# to create build env prerequsiites, some prerequisites must be interactively installed before the scripting can proceed.
# https://supabase.com/docs/guides/self-hosting/docker
# 1. install NPM/NPX (https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)
# 2. install GIT (https://git-scm.com/downloads)
# 3. install docker (https://docs.docker.com/desktop/setup/install/windows-install/ or https://docs.docker.com/desktop/setup/install/linux-install/)
# 4. create docker account and log in (docker desktop and pulls will not function without this). With the Docker UI open, you can observe the images and containers getting created and onlined in real time.
function ndr_packagePrereqCheck ()
{
  ndr_checkAndInstallPrerequisites checkOnly
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Package prereq only check failed."
    return 1
  fi

  return 0
}

# Usage: ndr_BuildModuleEnvFile <folder>
function ndr_BuildModuleEnvFile () 
{
  local logSectionDesc="Parsing common command line arguments"
  ndr_logSecStart "$logSectionDesc"

  if [[ $# -lt 1 ]]; then
    ndr_logError "Usage: ndr_BuildModuleEnvFile <folder>"
    return 1
  fi

  local containerFolder="$1"
  local sourceEnvFile="${gSCRIPT_HOME_DIR}/${gNDR_ENV_MASTER_FILENAME}"
  
  # Check source file
  if [[ ! -f "$sourceEnvFile" ]]; then
    ndr_logError "Source env file '$sourceEnvFile' not found."
    return 1
  fi

  # check destination folder
  local destFolder="$gSCRIPT_HOME_DIR/../${containerFolder}"
  if [[ ! -d "$destFolder" ]]; then
    ndr_logError "Destination folder '$destFolder' does not exist."
    return 1
  fi
  local destEnvFile="${destFolder}/${gNDR_ENV_MODULE_FILENAME}"
  
  local moduleEnvVars=(
  "SITE_URL" \
  "POSTGRES_PASSWORD" \
  "ANON_KEY"
  )
  
  # Empty or create output file
  rm -f "$destEnvFile"
  > "$destEnvFile"

   # Copy specified variables
  for var in "${moduleEnvVars[@]}"; do
    line=$(grep -E "^${var}=" "$sourceEnvFile" | head -n1)
    if [[ -n "$line" ]]; then
      echo "$line" >> "$destEnvFile"
    else
      ndr_logError "Warning: Variable '$var' not found in $sourceEnvFile"
      return 1
    fi
  done

  ndr_logInfo "Module environment file created at '$destEnvFile'."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_CleanupModuleEnvFile ()
{
  local logSectionDesc="Cleaning up module environment file"
  ndr_logSecStart "$logSectionDesc"

  if [[ $# -lt 1 ]]; then
    ndr_logError "Usage: ndr_CleanupModuleEnvFile <folder>"
    return 1
  fi

  local containerFolder="$1"
  local destFolder="$gSCRIPT_HOME_DIR/../${containerFolder}"
  local destEnvFile="${destFolder}/${gNDR_ENV_MODULE_FILENAME}"

  # Check destination file
  if [[ ! -f "$destEnvFile" ]]; then
    ndr_logError "Destination env file '$destEnvFile' not found."
    return 1
  fi

  # Remove the file
  rm -f "$destEnvFile"
  ndr_logInfo "Module environment file '$destEnvFile' removed."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
