import React, { useState, useEffect } from "react";
import { Database, RefreshCw, HardDrive } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { getProjectResources } from "@/lib/api/api-client";

interface DatabaseStorageDiscoveryProps {
    projectId: string;
    datacenterId: string;
}

interface StorageResource {
    id: string;
    name: string;
    type: string;
    size: string;
    location: string;
}

export default function DatabaseStorageDiscovery({
    projectId,
    datacenterId,
}: DatabaseStorageDiscoveryProps) {
    const [storageResources, setStorageResources] = useState<StorageResource[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const { toast } = useToast();

    const fetchDatabaseAndStorage = async () => {
        if (!projectId) return;
        
        setLoading(true);
        setError(null);
        try {
            const data = await getProjectResources(projectId, datacenterId);
            
            if (data) {
                setStorageResources(data.storage || []);
            }
        } catch (err: any) {
            setError(err.message || "Failed to fetch database and storage resources");
            toast({
                title: "Error",
                description: "Failed to fetch database and storage resources",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (projectId) {
            fetchDatabaseAndStorage();
        }
    }, [projectId, datacenterId]);

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                    <Database className="h-6 w-6 text-blue-500" />
                    <h2 className="text-xl font-semibold">Databases and Storage</h2>
                    {storageResources.length > 0 && (
                        <span className="px-2 py-1 bg-secondary rounded-full text-xs text-muted-foreground">
                            {storageResources.length} {storageResources.length === 1 ? "resource" : "resources"}
                        </span>
                    )}
                </div>

                <Button
                    onClick={() => fetchDatabaseAndStorage()}
                    variant="outline"
                    size="sm"
                    disabled={loading}
                >
                    {loading ? (
                        <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
                    ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                    )}
                    Refresh
                </Button>
            </div>

            {error && (
                <div className="bg-red-900/20 border border-red-800/30 text-red-400 p-4 rounded-md">
                    {error}
                </div>
            )}

            {loading ? (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-spin h-6 w-6 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
                    <span>Loading storage resources...</span>
                </div>
            ) : storageResources.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {storageResources.map((resource) => (
                        <Card key={resource.id} className="overflow-hidden">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2 mb-3">
                                    <HardDrive className="h-5 w-5 text-blue-500" />
                                    <h3 className="font-medium">{resource.name}</h3>
                                </div>
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Type</span>
                                        <span className="text-sm font-medium">{resource.type}</span>
                                    </div>
                                    
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Size</span>
                                        <span className="text-sm font-medium">{resource.size}</span>
                                    </div>
                                    
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Location</span>
                                        <span className="text-sm font-medium">{resource.location}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            ) : (
                <div className="text-center p-8 bg-secondary rounded-lg">
                    <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h4 className="text-lg font-medium mb-2">No storage resources found</h4>
                    <p className="text-muted-foreground">
                        This project has no database or storage resources configured
                    </p>
                </div>
            )}
        </div>
    );
}
