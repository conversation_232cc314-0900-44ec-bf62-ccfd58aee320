-- Migration to enhance approval security and auto-resume functionality

-- Update approval_tokens table with additional security fields
ALTER TABLE approval_tokens
ADD COLUMN IF NOT EXISTS client_ip TEXT,
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS approval_method TEXT CHECK (approval_method IN ('email', 'ui')),
ADD COLUMN IF NOT EXISTS approval_comment TEXT,
ADD COLUMN IF NOT EXISTS approval_decision TEXT CHECK (approval_decision IN ('approved', 'rejected')),
ADD COLUMN IF NOT EXISTS approval_timestamp TIMESTAMPTZ;

-- <PERSON>reate function to validate approver permissions
CREATE OR REPLACE FUNCTION can_approve_checkpoint(user_id UUID, checkpoint_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    checkpoint_record RECORD;
BEGIN
    -- Get user role
    SELECT role INTO user_role FROM user_profiles WHERE id = user_id;
    
    -- If user is admin, they can approve any checkpoint
    IF user_role = 'admin' THEN
        RETURN TRUE;
    END IF;
    
    -- If user is not an approver, they cannot approve
    IF user_role != 'approver' THEN
        RETURN FALSE;
    END IF;
    
    -- Check if user is the assigned approver for this checkpoint
    SELECT * INTO checkpoint_record 
    FROM recovery_plan_checkpoints 
    WHERE id = checkpoint_id;
    
    -- If checkpoint doesn't exist, return false
    IF checkpoint_record IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- If user is the assigned approver, they can approve
    IF checkpoint_record.approver_id = user_id THEN
        RETURN TRUE;
    END IF;
    
    -- If checkpoint requires a specific role and user has that role, they can approve
    IF checkpoint_record.approver_role IS NOT NULL AND user_role = checkpoint_record.approver_role THEN
        RETURN TRUE;
    END IF;
    
    -- Otherwise, user cannot approve
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to auto-resume recovery plan after approval
CREATE OR REPLACE FUNCTION auto_resume_after_approval()
RETURNS TRIGGER AS $$
BEGIN
    -- Only trigger auto-resume if status changed to 'approved'
    IF NEW.approval_status = 'approved' AND 
       (OLD.approval_status IS NULL OR OLD.approval_status != 'approved') THEN
        
        -- Update recovery plan status to 'in_progress'
        UPDATE recovery_plans_new
        SET 
            execution_status = 'in_progress',
            execution_metadata = jsonb_set(
                execution_metadata, 
                '{resumed_at}', 
                to_jsonb(NOW()::text)
            )
        WHERE id = NEW.recovery_plan_id;
        
        -- Log the auto-resume action
        INSERT INTO audit_logs (
            user_id,
            action,
            entity_type,
            entity_id,
            details,
            ip_address,
            user_agent
        ) VALUES (
            NEW.approved_by,
            'auto_resume_recovery_plan',
            'recovery_plan',
            NEW.recovery_plan_id,
            jsonb_build_object(
                'checkpoint_id', NEW.id,
                'resumed_at', NOW(),
                'approved_by', NEW.approved_by
            ),
            NEW.approval_metadata->>'ip_address',
            NEW.approval_metadata->>'user_agent'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto-resume
DROP TRIGGER IF EXISTS auto_resume_after_approval ON recovery_plan_checkpoints;
CREATE TRIGGER auto_resume_after_approval
AFTER UPDATE ON recovery_plan_checkpoints
FOR EACH ROW
EXECUTE FUNCTION auto_resume_after_approval();

-- Update RLS policies for approval_tokens
DROP POLICY IF EXISTS "Users can view their own approval tokens" ON approval_tokens;
CREATE POLICY "Users can view their own approval tokens"
    ON approval_tokens FOR SELECT
    USING (
        approver_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Users can create approval tokens" ON approval_tokens;
CREATE POLICY "Users can create approval tokens"
    ON approval_tokens FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role IN ('admin', 'operator')
        )
    );

DROP POLICY IF EXISTS "Users can delete their own approval tokens" ON approval_tokens;
CREATE POLICY "Users can delete their own approval tokens"
    ON approval_tokens FOR DELETE
    USING (
        approver_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Update RLS policies for recovery_plan_checkpoints
DROP POLICY IF EXISTS "Users can view recovery plan checkpoints" ON recovery_plan_checkpoints;
CREATE POLICY "Users can view recovery plan checkpoints"
    ON recovery_plan_checkpoints FOR SELECT
    USING (true);  -- All authenticated users can view checkpoints

DROP POLICY IF EXISTS "Only admins and operators can create checkpoints" ON recovery_plan_checkpoints;
CREATE POLICY "Only admins and operators can create checkpoints"
    ON recovery_plan_checkpoints FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role IN ('admin', 'operator')
        )
    );

DROP POLICY IF EXISTS "Only admins and operators can update checkpoints" ON recovery_plan_checkpoints;
CREATE POLICY "Only admins and operators can update checkpoints"
    ON recovery_plan_checkpoints FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role IN ('admin', 'operator')
        ) OR
        (
            -- Approvers can update only the approval status of checkpoints assigned to them
            EXISTS (
                SELECT 1 FROM user_profiles
                WHERE id = auth.uid() AND role = 'approver'
            ) AND
            (
                approver_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM user_profiles
                    WHERE id = auth.uid() AND role = approver_role
                )
            )
        )
    );

DROP POLICY IF EXISTS "Only admins can delete checkpoints" ON recovery_plan_checkpoints;
CREATE POLICY "Only admins can delete checkpoints"
    ON recovery_plan_checkpoints FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
