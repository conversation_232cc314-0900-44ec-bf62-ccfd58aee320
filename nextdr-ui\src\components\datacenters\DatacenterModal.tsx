import React from "react";
import { X } from "lucide-react";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useModalStore } from "@/lib/store/useStore";
import DatacenterForm from "./DatacenterForm";

const DatacenterModal = () => {
	const { isOpen, modalType, onClose } = useModalStore();
	const showModal = isOpen && modalType === "datacenter-form";

	return (
		<Dialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="bg-card border-border max-w-lg mx-auto max-h-[90vh] overflow-auto">
				<DialogHeader>
					<div className="flex items-center justify-between">
						<DialogTitle>Add Datacenter</DialogTitle>
					</div>
					<DialogDescription>Connect to a hypervisor to manage virtual machines</DialogDescription>
				</DialogHeader>
				<DatacenterForm />
			</DialogContent>
		</Dialog>
	);
};

export default DatacenterModal;
