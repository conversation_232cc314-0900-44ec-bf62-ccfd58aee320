import { Request, Response, NextFunction } from "express";
import { supabase } from "../services/supabaseService";

/**
 * Authentication middleware that extracts the user from the Authorization header
 * and sets it on the request object
 */
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
	try {
		// Skip authentication for specific public routes
		const publicRoutes = [
			"/api/license/verify",
			"/api/approve",
			"/api/gcp/recovery/approval", // Allow unauthenticated access to approval routes
		];

		if (publicRoutes.some((route) => req.path.startsWith(route))) {
			console.log(`Skipping authentication for public route: ${req.path}`);
			next();
			return;
		}

		// Get token from Authorization header
		const authHeader = req.headers.authorization;
		if (!authHeader || !authHeader.startsWith("Bearer ")) {
			// No token provided, continue without user
			// For recovery plan execution and resume endpoints, require authentication
			// But allow unauthenticated access to approval endpoints
			if (
				req.path.includes("/api/gcp/recovery/execute") ||
				req.path.includes("/api/gcp/recovery/resume")
			) {
				console.log(`Authentication required for protected route: ${req.path}`);
				res.status(401).json({ error: "Authentication required" });
				return;
			}
			next();
			return;
		}

		const token = authHeader.substring(7);

		// Verify token with Supabase
		const { data, error } = await supabase.auth.getUser(token);

		if (error || !data.user) {
			// Invalid token
			console.warn("Invalid auth token:", error?.message);

			// For recovery plan execution and resume endpoints, require valid authentication
			// But allow unauthenticated access to approval endpoints
			if (
				req.path.includes("/api/gcp/recovery/execute") ||
				req.path.includes("/api/gcp/recovery/resume")
			) {
				console.log(`Invalid authentication token for protected route: ${req.path}`);
				res.status(401).json({ error: "Invalid authentication token" });
				return;
			}
			next();
			return;
		}

		// Get user role from user_profiles
		const { data: userProfile, error: profileError } = await supabase
			.from("user_profiles")
			.select("role, status")
			.eq("id", data.user.id)
			.single();

		if (profileError || !userProfile) {
			console.warn("User profile not found:", profileError?.message);
			res.status(403).json({ error: "User profile not found" });
			return;
		}

		// Check if user is active
		if (userProfile.status !== "active") {
			res.status(403).json({ error: "User account is not active" });
			return;
		}

		// Set user on request object with role from profile
		(req as any).user = {
			id: data.user.id,
			email: data.user.email,
			role: userProfile.role,
		};

		next();
	} catch (error) {
		console.error("Auth middleware error:", error);
		res.status(500).json({ error: "Authentication error" });
	}
};

export default authMiddleware;
