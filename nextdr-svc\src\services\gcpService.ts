import { ProjectsClient } from "@google-cloud/resource-manager";

const projectsClient = new ProjectsClient();
const PROJECT_ID = process.env.GCP_PROJECT_ID || "nextdr";

// Function to generate a valid new project ID
function generateNewProjectId(parentProjectId: string): string {
  const timestamp = Date.now().toString().slice(-6);
  let newProjectId = `copy-${parentProjectId}-${timestamp}`;
  return newProjectId.toLowerCase().replace(/[^a-z0-9-]/g, "").slice(0, 30);
}

export async function createProjectCopy() {
  const NEW_PROJECT_ID = generateNewProjectId(PROJECT_ID);
  const [operation] = await projectsClient.createProject({
    project: { projectId: NEW_PROJECT_ID, name: "DR Project" },
  });
  await operation.promise();
  return NEW_PROJECT_ID;
} 