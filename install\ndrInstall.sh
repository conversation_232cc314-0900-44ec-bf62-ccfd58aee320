#!/bin/bash

source "$(dirname "${BASH_SOURCE[0]}")/ndrCommon.sh"

# --- CONFIGURATION ---

gLOG_FILE="ndrInstall.log"

# global variables
MIGRATION_TIMESTAMP=""
SUPABASE_MIGRATION_DIR="./supabase/migrations"
NDR_SQL_SCHEMA_FILENAME="NDR-schema.sql"
NDR_SUPABASE_DOCKER_COMPOSE_FILE="NDR-docker-compose.yaml"
SUPABASE_COMPOSE_COPY_MODE=0
ACTIVE_NDR_SQL_SCHEMA_FILE=""
supabaseRemotePwd=""

# supabase command variables
supabaseCmd="npx supabase"
supabaseProjDir="supabase-NDR_DB"
supabaseRemoteToken="********************************************" # Current NextDR Production remote DB token. Should never change but edit if needed.
supabaseRemoteProjRef="utcestwwfefexcnmjcjr" #Prod DB
#supabaseRemotePwd="@nextdr!123" #Prod DB new human readable

EXPRESS_MENU_OPTIONS="installprerequisites | pullschema | installsupabase | installservice | installui | installall | removesupabase | removeservice | removeui | removeall"

# ----------------------

# --- FUNCTIONS ---

function supabaseInstallHomeCheck ()
{
  local logSectionDesc="Supabase Docker install home check"
  ndr_logSecStart "$logSectionDesc"
  
  if [ "$gExpressMode" -eq 1 ]; then
    ndr_logInfo "Skipping Supabase Docker install home check in express mode."
    return 0
  fi

  echo "This script will install the supabase docker container under the directory [$gNEXTDR_HOME_DIR]. If this is not the desired location for this container, please exit and re-run from the correct location."
  
  read -p "Are you sure you want to proceed? (Y/n) " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Nn]$ ]]
  then
      ndr_logError "Operation cancelled."
      return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function supabaseContainerCleanup ()
{
  # Cleanup code snippet taken from "projectDir/reset.sh"
  # clean up prior container
  local logSectionDesc="Cleaning up prior container"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$supabaseProjDir" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  #echo "WARNING: This will remove all containers and container data, and will reset the .env file. This action cannot be undone!"
  #read -p "Are you sure you want to proceed? (y/N) " -n 1 -r
  #echo    # Move to a new line
  #if [[ $REPLY =~ ^[Nn]$ ]]
  #then
  #    ndr_logError "Operation cancelled."
  #    return 1
  #fi
  
  docker compose -p "ndr-supabase" -f ./$supabaseProjDir/docker-compose.yml -f ./$supabaseProjDir/dev/docker-compose.dev.yml down -v --remove-orphans
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "Failed to stop and remove Supabase Docker container"
    return 0
  fi
  ndr_logInfo "Stopped and removed Supabase Docker container"
  
  echo "Cleaning up bind-mounted directories..."
  BIND_MOUNTS=(
    "./$supabaseProjDir/volumes/db/data"
  )

  for DIR in "${BIND_MOUNTS[@]}"; do
    if [ -d "$DIR" ]; then
      echo "Deleting $DIR..."
      rm -rf "$DIR"
    else
      echo "Directory $DIR does not exist. Skipping bind mount deletion step..."
    fi
  done

  rm --recursive --force "$gNEXTDR_HOME_DIR/$supabaseProjDir"
  rm --recursive --force "$gNEXTDR_HOME_DIR/supabase"
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# To install supabase CLI:
# https://supabase.com/docs/guides/local-development
function supabaseCLIInstall ()
{
  local logSectionDesc="Installing supabase CLI"
  ndr_logSecStart "$logSectionDesc"

  cd "$gNEXTDR_HOME_DIR"  || { ndr_logError "Failed to cd into home dir"; return 1; }
  
  # check if supabase cli is already installed
  $supabaseCmd -v
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "Supabase CLI already installed"
    return 0
  fi

  npm install supabase --save-dev
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to install supabase CLI."
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"
}

function supabaseLocalSchemaFileCheck ()
{
  local logSectionDesc="Local NDR schema file check"
  ndr_logSecStart "$logSectionDesc"
  
  if [ "$gExpressMode" -eq 1 ]; then
    ndr_logInfo "Skipping local NDR schema file check in express mode."
    return 0
  fi

  # check for local NDR scehma file. 
  # If not present, prompt user to exit and run the supabase db pull command to generate the file.
  if [ -z "$ACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    # active schema file variable not populated. populate and recheck
    ACTIVE_NDR_SQL_SCHEMA_FILE=$gNEXTDR_HOME_DIR/$NDR_SQL_SCHEMA_FILENAME
  fi
  
  if [ ! -f "$ACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    ndr_logWarn "WARNING: Local NDR schema file not found in home directory [$ACTIVE_NDR_SQL_SCHEMA_FILE]. This file is required to seed the supabase docker container with the schema upon startup. Proceeding without this file will produce an empty container DB and will require manual injection of the SQL schema after installation."
    read -p "Do you wish to proceed without the file or exit container install to generate or supply the file (yes, proceed without | no, exit container install? (y|N)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Yy]$ ]]; then
      ndr_logInfo "Proceeding without local NDR schema file."
    else
      ndr_logError "Operation cancelled"
      return 1
    fi
  else
    ACTIVE_NDR_SQL_SCHEMA_FILE=$gNEXTDR_HOME_DIR/$NDR_SQL_SCHEMA_FILENAME
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function installSupabaseApplication ()
{
  # Create your desired working directory:
  # Tree should look like this
  # .
  # ├── supabase
  # └── supabase-project
  local logSectionDesc="Cleaning and creating new application folders"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$supabaseProjDir" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  rm --recursive --force "$gNEXTDR_HOME_DIR/$supabaseProjDir"
  rm --recursive --force "$gNEXTDR_HOME_DIR/supabase"
  mkdir "$supabaseProjDir"
  ndr_logSecEnd "$logSectionDesc"

  # To pull supabase docker code from GIT
  # This is a very lengthy process and will take several minutes or more depending on connectivity speed.
  local logSectionDesc="Cloning a local supabase container from GIT"
  ndr_logSecStart "$logSectionDesc"
  # basic clone command
  #git clone --depth 1 https://github.com/supabase/supabase
  # advanced clone command
  git clone --filter=blob:none --no-checkout https://github.com/supabase/supabase
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Git clone failed."
    return 1
  fi
  ndr_logInfo "Git clone for Supabase completed."

  cd supabase || { ndr_logError "Failed to cd into supabase dir"; return 1; }

  git sparse-checkout set --cone docker && git checkout master
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "GIT sparse checkout failed."
    return 1
  fi
  ndr_logInfo "Git sparse checkout for Supabase completed."

  cd ".." || { ndr_logError "Failed to cd into parent dir"; return 1; }
  ndr_logSecEnd "$logSectionDesc"

  # Copy the compose files over to your project
  local logSectionDesc="Copying the compose files to project"
  ndr_logSecStart "$logSectionDesc"
  composeSrcLocation="$gNEXTDR_HOME_DIR/supabase/docker/*"
  composeDestLocation="$supabaseProjDir"
  cp -rf "$composeSrcLocation" "$composeDestLocation"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy Base docker compose files [$composeSrcLocation]  to [$PWD/$composeDestLocation] "
    return 1
  fi
  ndr_logInfo "Base docker compose files [$composeSrcLocation] copied to [$PWD/$composeDestLocation]"
  
  if [[ $SUPABASE_COMPOSE_COPY_MODE -eq 1 ]]; then
    # Copy the NextDR specific docker compose file to the project. This custom sql script will be used to create the NDR tables and RLS policies.
    # This mode is not currently the default behavior since we now prefer to edit the base docker package compose file directly.
    # Using the base docker compose file for the NDR container is a better practice since the file could evolve over time and this would ensure we always have the latest version.
    cp "$gNEXTDR_HOME_DIR/$NDR_SUPABASE_DOCKER_COMPOSE_FILE" "$composeDestLocation/docker-compose.yml"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Docker compose file [$gNEXTDR_HOME_DIR/$NDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$PWD/$composeDestLocation]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Docker compose file [$gNEXTDR_HOME_DIR/$NDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$PWD/$composeDestLocation]"
  else
    customizeSupabaseDockerContainerFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to customize $gCOMPANY_NAME Docker compose file."
      return 1
    fi
    ndr_logInfo "Successfully customized $gCOMPANY_NAME Docker compose file."
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Copying the $gCOMPANY_NAME schema file to project"
  ndr_logSecStart "$logSectionDesc"
  # if var is not empy AND file exists
  if [ -n "$ACTIVE_NDR_SQL_SCHEMA_FILE" ] && [ -f "$ACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    # Copy the NDR schema file to the project.
    # This custom sql script will be absorbed by the postgres db docker container at initial startup and will create the NDR tables and RLS policies.
    schemaDestLocation=$gNEXTDR_HOME_DIR/$supabaseProjDir/db-init-scripts
    mkdir "$schemaDestLocation"
    cp -f "$ACTIVE_NDR_SQL_SCHEMA_FILE" "$schemaDestLocation"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Supabase schema file [$ACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Supabase schema file [$ACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"

  else
    ndr_logWarn "No active $gCOMPANY_NAME Supabase schema file present, skipping file copy."
  fi
  ndr_logSecEnd "$logSectionDesc"

  # Copy the custom env vars
  local logSectionDesc="Copying the $gCOMPANY_NAME env file to project"
  ndr_logSecStart "$logSectionDesc"
  envSrcFile="$gNEXTDR_HOME_DIR/.env.nextdr"
  envDestFile="$gNEXTDR_HOME_DIR/$supabaseProjDir/.env"
  cp -f "$envSrcFile" "$envDestFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Docker ENV file [$envSrcFile] to [$envDestFile]"
    return 1
  fi
  ndr_logInfo "$gCOMPANY_NAME Docker ENV file [$envSrcFile] copied to [$envDestFile]"
  ndr_logSecEnd "$logSectionDesc"

  # Go to the docker folder
  cd "$supabaseProjDir" || { ndr_logError "Failed to cd into supabase proj dir"; return 1; }
  
  # Pull the latest images
  local logSectionDesc="Pulling latest images for container"
  ndr_logSecStart "$logSectionDesc"
  docker compose pull
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose pull failed"
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  # Start the services (in detached mode)
  local logSectionDesc="Starting container images services"
  ndr_logSecStart "$logSectionDesc"
  docker compose -p "ndr-supabase" up -d
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose up failed"
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  #After all the services have started you can see them running in the background:
  local logSectionDesc="Querying container image service status"
  ndr_logSecStart "$logSectionDesc"
  docker compose ps
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose ps failed"
    return 1
  fi
  
  
  # Accessing Supabase Studio
  # You can access Supabase Studio through the API gateway on port 8000. For example: http://<your-ip>:8000, or localhost:8000 if you are running Docker locally.

  # You will be prompted for a username and password. By default, the credentials are:

  # Username: supabase
  # Password: this_password_is_insecure_and_should_be_updated

  # Need to change credentials and secrets to secure values
  # Update the ./docker/.env file with your own secrets. In particular, these are required:

  # POSTGRES_PASSWORD: the password for the postgres role.
  # JWT_SECRET: used by PostgREST and GoTrue, among others.

  # Dashboard authentication
  # The Dashboard is protected with basic authentication. The default user and password MUST be updated before using Supabase in production.
  # Update the following values in the ./docker/.env file:

  # DASHBOARD_PASSWORD: The default password for the Dashboard

  ndr_logInfo "Local supabase container installation is complete. You can access the Supabase Studio through the API gateway on port 8000."
  ndr_logInfo "http://<your-ip>:8000, or http://localhost:8000 if you are running # Docker locally"
  ndr_logInfo "Please check the .env file in the $supabaseProjDir for dashboard credentials."

  ndr_logSecEnd "$logSectionDesc"
  
  return 0
}

function supabaseLogoutRemote ()
{
  local logSectionDesc="Unlinking local supabase project"
  ndr_logSecStart "$logSectionDesc"

  cmd="$supabaseCmd unlink"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase unlink failed."
    #return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  
  local logSectionDesc="Executing supabase remote logout"
  ndr_logSecStart "$logSectionDesc"

  cmd="$supabaseCmd logout"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd <<< "y"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase logout failed."
    #return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# To begin linking to the remote instance
# eg. supabase link --project-ref <project-id> -password <password>
# You can get <project-id> from your project's dashboard URL: https://supabase.com/dashboard/project/<project-id>
# Alternatively, omitting the project ID will cause supabase to show all available remote db's to choose from.
# password is the master project password (not the supabase email account login)

function supabaseConnectRemote ()
{
  local logSectionDesc="Executing supabase remote login"
  ndr_logSecStart "$logSectionDesc"

  cmd="$supabaseCmd login --token $supabaseRemoteToken"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase login with token failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Executing supabase init"
  ndr_logSecStart "$logSectionDesc"

  cmd="$supabaseCmd init --force"
  $cmd <<< "n"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase init failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Linking local supabase project to remote master"
  ndr_logSecStart "$logSectionDesc"

  cmd="$supabaseCmd link --project-ref $supabaseRemoteProjRef"
  if [ -n "$supabaseRemotePwd" ]; then
    cmd="$cmd --password '$supabaseRemotePwd'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do
    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase link failed."
    # immediately run a supabase unlink command to remove the local link and clean up.
    $supabaseCmd unlink
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function supabasePullDatabaseReferenceSchemaFile ()
{
  local logSectionDesc="Executing supabase DB pull"
  ndr_logSecStart "$logSectionDesc"

  # todo: this fn should have 2 modes: 1. pull from git (default), 2. pull from remote db (optional, via express mode option or prompt?)

  cd "$gNEXTDR_HOME_DIR" || { ndr_logError "Failed to cd into home dir"; return 1; }

  # pre check for any existing migrations. If any exist, the DB pull may fail.
  # If this is the first time running this script, there should be no migrations present.
  # If there are migrations present, the user will need to run the migration repair command to remove the entry from the remote migration history table.
  # Set the directory to query
  
  
  # Check if the directory exists. Directory will not exist for fresh installs so skip check if not present.
  if [ ! -d "$SUPABASE_MIGRATION_DIR" ]; then
    ndr_logInfo "Directory $SUPABASE_MIGRATION_DIR does not exist, skipping precheck. "
    #return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$SUPABASE_MIGRATION_DIR"/*)
  COUNT=0
  
  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ndr_logInfo "Removing existing migration file: [$FILE]"
      # delete original migration file from migrations folder
      rm "$FILE"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove preexisting migration file [$FILE]. Please remove manually."
        #return 1
      fi
      
      # Extract just the filename portion (strip path)
      BASENAME=$(basename "$FILE")

      # Check if filename contains an underscore
      if [[ "$BASENAME" != *_* ]]; then
        ndr_logWarn "Invalid filename format. No underscore found in [$BASENAME], skipping."
        continue
      fi

      # Extract the timestamp value before the first underscore
      MIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

      if [ -z "$MIGRATION_TIMESTAMP" ]; then
        ndr_logWarn "Failed to extract migration timestamp from filename [$BASENAME], skipping. "
        continue
      fi

      ndr_logInfo "Using migration timestamp: [$MIGRATION_TIMESTAMP]"

      # run migration entry reset against remote db with the timestamp
      # this will remove the entry from the remote migration history table
      cmd="$supabaseCmd migration repair --status reverted $MIGRATION_TIMESTAMP"
      if [ -n "$supabaseRemotePwd" ]; then
        cmd="$cmd --password '$supabaseRemotePwd'"
      fi    
      if [[ "$gDebugMode" -eq 1 ]]; then
        cmd="$cmd --debug"
      fi
      
      max_retries=3
      attempt=1

      while true; do
        eval "$cmd"
        
        return_code=$?

        if [[ $return_code -eq 0 ]]; then
          # success
          break
        fi

        if [[ $attempt -ge $max_retries ]]; then
          ndr_logError "Max supabase command retries reached. Exiting. "
          return 1
        fi

        ndr_logWarn "Supabase command attempt $attempt failed, retrying."
        
        
        attempt=$((attempt + 1))
        sleep 1  # Optional: wait before retrying
      done

      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove migration entry [$MIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$supabaseCmd migration repair --status reverted $MIGRATION_TIMESTAMP]"
        #return 1
      fi

      ndr_logInfo "Removing remote migration entry for timestamp: [$MIGRATION_TIMESTAMP]"
      
      ((COUNT++))
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logInfo "No preexisting migration files found in directory. Proceeding with db pull."
  else
    ndr_logInfo "Removed [$COUNT] migration files from dir [$SUPABASE_MIGRATION_DIR]"
  fi

  # The auth and storage schemas are excluded by default. Run supabase db pull --schema auth,storage again to diff them.
  max_retries=3
  attempt=1

  cmd="$supabaseCmd db pull --linked"
  if [ -n "$supabaseRemotePwd" ]; then
    cmd="$cmd --password '$supabaseRemotePwd'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  while true; do
    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase db pull failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Capture any changes that you have made to your remote database before you went through in the process of pulling the schema.
# may prompt to update remote migration history table. select default.
# this process is lengthy and can take several minutes.

# If successful, Will output schema sql file to a folder stated in CLI output.
# supabase/migrations is now populated with a migration in <timestamp>_remote_schema.sql.
# eg. "Schema written to supabase\migrations\20250421192748_remote_schema.sql"

# query the output folder for the generated sql file. This will be under supabase/migrations.
# copy this file to the root project folder and delete the original from the migrations folder.
# parse the timestamp from the file name.
# Run the migration entry reset against the remote db with the timestamp. This will remove the entry from the remote migration history table.
# eg.  npx supabase migration repair --status reverted 20250422185759
function supabaseProcessRemoteMigration ()
{
  local logSectionDesc="Processing supabase remote migration file"
  ndr_logSecStart "$logSectionDesc"

  # Set the directory to query
  DIR="./supabase/migrations"
  
  # Check if the directory exists
  if [ ! -d "$DIR" ]; then
    ndr_logError "Directory $DIR does not exist. "
    return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$DIR"/*)
  COUNT=0
  MIGRATION_FILENAME=""

  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ((COUNT++))
      MIGRATION_FILENAME="$FILE"
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logError "No migration files found in directory. "
    return 1
  elif [ "$COUNT" -gt 1 ]; then
    ndr_logError "Too many files migration found in directory. "
    return 1
  else
    ndr_logInfo "Found migration file: [$MIGRATION_FILENAME]"
    # You can now use $MIGRATION_FILENAME as needed
  fi

  # Extract just the filename portion (strip path)
  BASENAME=$(basename "$MIGRATION_FILENAME")

  # Extract the timestamp value before the first underscore
  MIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

  ndr_logInfo "Using migration timestamp: [$MIGRATION_TIMESTAMP]"

  #destMigrationFile=$MIGRATION_TIMESTAMP"_"$NDR_SQL_SCHEMA_FILENAME
  destMigrationFile=$NDR_SQL_SCHEMA_FILENAME
  # check for existing migration refrence file in root project folder.
  # if one exists, backup/rename with timestamp appended.
  if [[ -f "$destMigrationFile" ]]; then
    # Get the UNIX timestamp (modification time) of the file
    timestamp=$(stat -c %Y "$destMigrationFile")

    # Convert timestamp to readable format: YYYYMMDD_HHMMSS
    readable_time=$(date -d @"$timestamp" +"%Y%m%d_%H%M%S")
    
    # Build the new filename with timestamp appended
    filename_base="${destMigrationFile%.*}"
    filename_ext="${destMigrationFile##*.}"

    if [[ "$filename_base" -eq "$destMigrationFile" ]]; then
      # No extension case
      backup_filename="${filename_base}_${readable_time}"
    else
      backup_filename="${filename_base}_${readable_time}.${filename_ext}"
    fi

    # Move the file to the new filename
    mv "$destMigrationFile" "$backup_filename"

    ndr_logInfo "Backing up preexisting NDR SQL schema file [$destMigrationFile] to [$backup_filename]"
  fi

  # copy migration file to root project folder
  cp -f "$MIGRATION_FILENAME" "$gNEXTDR_HOME_DIR/$destMigrationFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy migration file [$MIGRATION_FILENAME]"
    return 1
  fi
  ndr_logInfo "Copied migration file [$MIGRATION_FILENAME] to [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # cache copied migration file name for later use in docker container construction
  ACTIVE_NDR_SQL_SCHEMA_FILE=$gNEXTDR_HOME_DIR/$destMigrationFile
  ndr_logInfo "Caching local NDR SQL schema file for use in docker container construction [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # delete original migration file from migrations folder
  rm "$MIGRATION_FILENAME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove original migration file [$MIGRATION_FILENAME]. Please remove manually."
    #return 1
  fi

  # run migration entry reset against remote db with the timestamp
  # this will remove the entry from the remote migration history table
  cmd="$supabaseCmd migration repair --status reverted $MIGRATION_TIMESTAMP"
  if [ -n "$supabaseRemotePwd" ]; then
    cmd="$cmd --password '$supabaseRemotePwd'"
  fi    
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do
    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove migration entry [$MIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$supabaseCmd migration repair --status reverted $MIGRATION_TIMESTAMP]"
    #return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function supabasePushLocalMigration ()
{
  local logSectionDesc="Processing supabase local migration"
  ndr_logSecStart "$logSectionDesc"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function customizeSupabaseDockerContainerFile () 
{
  local logSectionDesc="Customizing NDR supabase docker container file"
  ndr_logSecStart "$logSectionDesc"
  
  local file=$supabaseProjDir/"docker-compose.yml"
  temp_file="${file}.tmp"

  if [[ ! -f $file ]]; then
    ndr_logError "Docker container file [$file] not found."
    return 1
  fi

  ndr_logInfo "Customizing $gCOMPANY_NAME project Docker container file [$file]."

  # Replace first occurrence of 'name: supabase' with 'name: ndr-supabase'
  sed -i '0,/name: supabase/s//name: ndr-supabase/' "$file"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to change top level container stack entry to \"ndr-supabase\" in container file."
    #return 1
  fi
  ndr_logInfo "Changed top level container stack entry to \"ndr-supabase\" in container file."

  # Define the exact lines we want to insert
  insert_line1="# Initialize the database with NDR schema"
  #insert_line2="- ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"

  # Skip if both lines are already present
  grep -qF -- "$insert_line1" "$file"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "NDR schema file entries already present in container file, skipping addition."
  else
    # if var is not empy AND file exists
    temp_flag="/tmp/awk_entries_added.flag"
    rm -f "$temp_flag"
    if [ -n "$ACTIVE_NDR_SQL_SCHEMA_FILE" ] && [ -f "$ACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
      # Use awk to inject new lines at the end of the correct 'volumes:' section under 'services -> db'
      entriesAdded=$(awk -v flagfile="$temp_flag" '
        BEGIN {
          in_services = 0
          in_db = 0
          in_volumes = 0
          printed_extra = 0
          entriesAdded = 0
        }

        /^[[:space:]]*services:/ {
          in_services = 1
          print
          next
        }

        in_services && /^[[:space:]]{2}db:$/ {
          in_db = 1
          print
          next
        }

        in_db && /^[[:space:]]{2}[a-z0-9_-]+:$/ {
          in_db = 0
        }

        in_db && /^[[:space:]]+volumes:$/ {
          in_volumes = 1
          volume_indent = gensub(/[^[:space:]].*/, "", "g")
          print
          next
        }

        in_volumes {
          if ($0 ~ "^" volume_indent "  " && ($0 ~ /^ *- / || $0 ~ /^ *#|^ *$/)) {
            print
            next
          } else {
            if (!printed_extra) {
              print volume_indent "  # Initialize the database with NDR schema"
              print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
              printed_extra = 1
              entriesAdded = 1
            }
            in_volumes = 0
            print
            next
          }
        }

        {
          print
        }

        END {
          if (in_volumes && !printed_extra) {
            print volume_indent "  # Initialize the database with NDR schema"
            print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
            entriesAdded = 1
          }

          # Output result to a separate file
          print entriesAdded > flagfile
        }
      ' "$file" > "$temp_file")

      # Read the flag from temp file
      entriesAdded=$(cat "$temp_flag")
      rm -f "$temp_flag"
      if [ "$gDebugMode" -eq 1 ]; then
        ndr_logInfo "Entries added: $entriesAdded"
      fi
      
      if [ "$entriesAdded" -eq 0 ]; then
        ndr_logError "Awk failed to process container file to add NDR schema file entry to DB volumes section."
        rm -f "$temp_file"
        return 1
      fi

      mv "$temp_file" "$file"
      ndr_logInfo "Moved temp container file [$temp_file] to [$file]."
      ndr_logInfo "Successfully added NDR schema file entry DB volumes section in container file."
      
    else
      ndr_logWarn "No active NDR supabase schema file present, skipping sql file injection."
    fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# https://supabase.com/docs/guides/self-hosting/docker#accessing-postgres
#supabaseLocalURL="postgres://postgres.your-tenant-id:your-super-secret-and-long-postgres-password@127.0.0.1:3000/postgres"
#npx supabase db push --local --db-url $supabaseLocalURL

# *** TODO - create NDR tables and rls/access configuration. This is best done via supabase CLI and using the link and db pull commands. This process requires the supabase CLI to be installed to run commands locally. This process also requires the supabase master project password and ref ID. If the master PWD is not available for scripting, the admin will need to run the link/pull and generate the sql script and share with group. From there the master sql schema script can be checked otu and run on the local instance to populate. If the master pwd is shared, teh user can use the This will generate the entire sql script to create all tables and This could be done either periodically when the schema changes or every time


# Creating docker container - example
# https://docs.docker.com/get-started/workshop/02_our_app/
# https://www.digitalocean.com/community/tutorials/how-to-build-a-node-js-application-with-docker#introduction
# create "dockerfile" file in project root and customize

# docker build -t getting-started .
# cd /path/to/getting-started-app (fill in your path here)

# ====================================================
# Main script execution
# ====================================================

function mainPullSupabaseReferenceSchema ()
{
  local logSectionDesc="Pulling Latest Supabase Reference Schema"
  ndr_logSecStart "$logSectionDesc"

  ndr_packagePrereqCheck
  supabaseCLIInstall
  supabaseConnectRemote
  supabasePullDatabaseReferenceSchemaFile
  supabaseProcessRemoteMigration
  supabaseLogoutRemote

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallSupabaseApplication ()
{
  local logSectionDesc="Installing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  ndr_packagePrereqCheck
  supabaseInstallHomeCheck
  supabaseLocalSchemaFileCheck
  supabaseContainerCleanup
  installSupabaseApplication

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <image_name> <image_version> <repo_name> <repo_type>
function downloadDockerImage ()
{
  local logSectionDesc="Downloading Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  local imageRepoType="$4"
  
  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: downloadDockerImage <image_name> <image_version> <repo_name> <repo_type>"
    return 1
  fi

  if [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_GIT ]]; then
    ndr_logWarn "Not implemented"
  
  elif  [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB ]]; then
    
    docker login -u "$NDR_DOCKER_REPO_ACCOUNT" -p "$NDR_DOCKER_REPO_ACCESS_TOKEN"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker login failed for [$NDR_DOCKER_REPO_ACCOUNT]."
      return 1
    fi
    ndr_logInfo "Docker login success for account [$NDR_DOCKER_REPO_ACCOUNT]"

    # repo tag format: <account>/<repo_name>:<{image_base_name}_{image_version}>
    local repoImageTag="$NDR_DOCKER_REPO_ACCOUNT/$imageRepoName:${dockerImageBaseName}_${dockerImageVersion}"

    # pull image from repo
    docker pull "$repoImageTag"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker pull failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker pull success for [$repoImageTag]."

    # check if the newly pulled Docker image is present
    ndr_verifyDockerImageExists "$repoImageTag"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker image verification failed for local copy of remote tag [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker remote repo image tag found [$repoImageTag]."

    # re tag it from remote format to local.
    docker tag "$repoImageTag" "$dockerImageName"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker tag failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker tag success for [$repoImageTag] to [$dockerImageName]."
    
    # cleanup remote repo tag reference to local image.
    ndr_logInfo "Removing remote repo tag [$repoImageTag] from local image [$dockerImageName]"
    docker rmi "$repoImageTag"
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker rmi failed for [$repoImageTag]."
      #return 1
    fi
    
  else
    ndr_logError "Error, unknown Docker image repo mode."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallServiceApplication ()
{
  local logSectionDesc="Installing service application"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_SERVICE_HOME_LOC"
  local dockerContainerName="$NDR_SERVICE_CONTAINER_NAME"
  local dockerContainerPort="$NDR_SERVICE_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # pull image from repo
  downloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallUIApplication ()
{
  local logSectionDesc="Installing UI application"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_UI_HOME_LOC"
  local dockerContainerName="$NDR_UI_CONTAINER_NAME"
  local dockerContainerPort="$NDR_UI_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # pull image from repo
  downloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallAllApplications ()
{
  local logSectionDesc="Installing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainInstallSupabaseApplication
  mainInstallServiceApplication
  mainInstallUIApplication

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveSupabaseApplication ()
{
  local logSectionDesc="Removing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  supabaseContainerCleanup

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


function mainRemoveServiceApplication ()
{
  local logSectionDesc="Removing Service Application"
  ndr_logSecStart "$logSectionDesc"

  ndr_mainCleanupServiceApplication

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveUIApplication ()
{
  local logSectionDesc="Removing UI Application"
  ndr_logSecStart "$logSectionDesc"

  ndr_mainCleanupUIApplication

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveAllApplications ()
{
  local logSectionDesc="Removing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainRemoveSupabaseApplication
  mainRemoveServiceApplication
  mainRemoveUIApplication

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# command line parsing
# ====================================================
function mainParseCommandLineArgs ()
{
  local logSectionDesc="Parsing command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Default values
  
  # Enable logging only if gLOG_FILE is non-empty
  if [[ -n "$gLOG_FILE" ]]; then
    exec > >(tee -a "$gLOG_FILE") 2>&1
  fi

  ndr_parseCommandLineArgs "$@"

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --password|-p)
        if [[ -n "$2" && "$2" != --* ]]; then
          supabaseRemotePwd="$2"
          #ndr_logInfo "Supabase set to [$supabaseRemotePwd]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --remote-token|-rt)
        if [[ -n "$2" && "$2" != --* ]]; then
          supabaseRemotetoken="$2"
          ndr_logInfo "Supabase $1 set to [$supabaseRemoteToken]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --help|-h)
        ndr_logWarn "Usage: $0 --express <$EXPRESS_MENU_OPTIONS> --password <password> [--debug]."
        return 1
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Express install mode
# ====================================================
function mainExpressInstallMode ()
{
  local logSectionDesc="Executing express install mode"
  ndr_logSecStart "$logSectionDesc"

  # Check if express mode is enabled and an option is provided
  if [ "$gExpressMode" != 1 ]; then
    ndr_logError "Express mode is not enabled. Use --express to enable."
    return 1
  fi
  if [ -z "$gExpressOption" ]; then
    ndr_logError "No express option provided. Use --express <option>."
    return 1
  fi

  # Set default values for variables
  gNEXTDR_HOME_DIR="$PWD"
  NDR_SQL_SCHEMA_FILENAME="NDR-schema.sql"
  ACTIVE_NDR_SQL_SCHEMA_FILE="$gNEXTDR_HOME_DIR/$NDR_SQL_SCHEMA_FILENAME"
  supabaseProjDir="supabase-NDR_DB"
  gExpressMode=1
  
  case "$gExpressOption" in
    installprerequisites)
      ndr_checkAndInstallPrerequisites
      ;;
    pullschema)
      mainPullSupabaseReferenceSchema
      ;;
    installsupabase)
      mainInstallSupabaseApplication
      ;;
    installservice)
      mainInstallServiceApplication
      ;;
    installui)
      mainInstallUIApplication
      ;;
    installall)
      mainInstallAllApplications
      ;;
    removesupabaseapp|removesupabase)
      mainRemoveSupabaseApplication
      ;;
    removeserviceapp|removeservice)
      mainRemoveServiceApplication
      ;;
    removeuiapp|removeui)
      mainRemoveUIApplication
      ;;
    removeall)
      mainRemoveAllApplications
      ;;
    test)
      ndr_checkAndInstallDocker
      ;;
    *)
      # print usage and exit
      ndr_logWarn "Invalid option. Please use one of the following options: installprerequisites, pullschema, installcontainer, pullandinstall or cleancontainer."
      return 1
      ;;
  esac
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Interactive menu mode
# ====================================================
function mainInteractiveMenuMode ()
{
  local logSectionDesc="Executing interactive menu mode"
  ndr_logSecStart "$logSectionDesc"
  
  MENU_ITEM_1="Check and install software package prerequisites"
  MENU_ITEM_2="Pull Supabase reference schema file from repository"
  MENU_ITEM_3="Install the $gCOMPANY_NAME Supabase database in a local Docker container"
  MENU_ITEM_4="Install the $gCOMPANY_NAME Service application in a local Docker container"
  MENU_ITEM_5="Install the $gCOMPANY_NAME UI applcation in a local Docker container"
  MENU_ITEM_6="Install all modules"
  MENU_ITEM_7="Remove existing $gCOMPANY_NAME Supabase Docker application"
  MENU_ITEM_8="Remove existing $gCOMPANY_NAME Service Docker application"
  MENU_ITEM_9="Remove existing $gCOMPANY_NAME UI Docker application"
  MENU_ITEM_10="Remove all modules"

  while true; do
    echo "===================================================================="
    echo "$gCOMPANY_NAME Software Installation and Package Management Menu"
    echo "===================================================================="
    echo "Please select an option:"
    echo ""
    echo "1. $MENU_ITEM_1"
    echo "2. $MENU_ITEM_2"
    echo "3. $MENU_ITEM_3"
    echo "4. $MENU_ITEM_4"
    echo "5. $MENU_ITEM_5"
    echo "6. $MENU_ITEM_6"
    echo "7. $MENU_ITEM_7"
    echo "8. $MENU_ITEM_8"
    echo "9. $MENU_ITEM_9"
    echo "10. $MENU_ITEM_10"
    echo "q. Exit"
    echo ""
    read -p "Enter your choice [1-n]: " choice

    case $choice in
      1)
        echo "$MENU_ITEM_1. This will check for NPM/NPX, GIT, Docker, etc and prompt for interactive install."
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        # default YES logic
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_checkAndInstallPrerequisites
        fi
        ;;
      2)
        echo "$MENU_ITEM_2"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainPullSupabaseReferenceSchema
        fi
        ;;
      3)
        echo "$MENU_ITEM_3. Warning, this will overwrite any existing instance and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallSupabaseApplication
        fi
        ;;
      4)
        echo "$MENU_ITEM_4. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallServiceApplication
        fi
        ;;
      5)
        echo "$MENU_ITEM_5. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallUIApplication
        fi
        ;;
      6)
        echo "$MENU_ITEM_6. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallAllApplications
        fi
        ;;
      7)
        echo "$MENU_ITEM_7. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveSupabaseApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
     8)
        echo "$MENU_ITEM_8. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveServiceApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      9)
        echo "$MENU_ITEM_9. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveUIApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      10)
        echo "$MENU_ITEM_10. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveAllApplications
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      Q|q)
        echo "Exiting..."
        break
        ;;
      t)
        #test section
        # unit test - remove for production
        ACTIVE_NDR_SQL_SCHEMA_FILE="$gNEXTDR_HOME_DIR/$NDR_SQL_SCHEMA_FILENAME" 
        customizeSupabaseDockerContainerFile
        break
        ;;
      *)
        echo "Invalid option. Please try again."
        ;;
    esac

    echo ""
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function main ()
{
  echo "🕓 Started at $(date)"

  mainParseCommandLineArgs "$@"
  return_code=$?
  if [ $return_code != 0 ]; then
    return 1
  fi

  ndr_osTypeCheck

  if [[ "$gExpressMode" -eq 1  && -n "$gExpressOption" ]]; then
    ndr_logInfo "Express install option selected. Skipping all prompts and running with supplied values."
    mainExpressInstallMode
    echo "🕓 Finished at $(date)"
    return 0
  fi

  mainInteractiveMenuMode

  echo "🕓 Finished at $(date)"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
  exit 0
fi

# TC 1: fresh docker only install, skip pull
# TC 2: existing docker only install (remove and install), skip pull
# TC 3: fresh docker install with db pull
# TC 4: existing docker install (skip) with db pull
# tc 5: test express options

# issues
# change install log file to /var/log/ndrInstall.log
# move supabase cli install to main prereq function?
# move env file check before cleanup?
# pullschema would either eventually go away or be hidden. We shhould hide in the interactive menu, but still expose in express so we can administer discretely.
# user should not be able to perform this action and schema file should only be available from GITHUB eventually for production (schema file pull should eventually be done as part of supabase app install and not separate step).
# make script more folder independent? Currently it relies on running from install dir so we have access to ancillary config files.

# notes
# docker desktop in linux requires KVM support. This is not available in VirtualBox when running a linux VM on windows bare metal (requires linux on linux host, then you cna enable the KVM option in virtual box in the processor tab).
# Docker does not provide support for running Docker Desktop for Linux in nested virtualization scenarios. 
# We recommend that you run Docker Desktop for Linux natively on supported distributions.
# docker core services are fine and will run all required modules for NextDR supabase.
# https://docs.docker.com/desktop/setup/install/linux/

# Main linux distro families. test with one of each for coverage of all distros.
# debian: debian, ubuntu, linux mint, pop!_os
# redhat: redhat, centos, fedora, rocky linux
# suse: suse, opensuse
# less common, not required for testing
# arch: arch linux, manjaro
# alpine: alpine linux
# osx: macos

# VM notes
# after copying initial file
# sudo chown -R vboxuser /path/to/directory
# sudo chmod -R 777 /path/to/directory
# dos2unix /path/to/directory/filename.sh