#!/bin/bash

source "$(dirname "${BASH_SOURCE[0]}")/ndrRegistry.sh"
source "$(dirname "${BASH_SOURCE[0]}")/ndrDocker.sh"

# --- CONFIGURATION ---

gLOG_FILE="/var/log/ndrInstall.log"

# global variables
gMIGRATION_TIMESTAMP=""
gSUPABASE_MIGRATION_DIR="./supabase/migrations"
gNDR_SQL_SCHEMA_FILENAME="NDR-schema.sql"
gNDR_SUPABASE_DOCKER_COMPOSE_FILE="NDR-docker-compose.yaml"
gSUPABASE_COMPOSE_COPY_MODE=0
gACTIVE_NDR_SQL_SCHEMA_FILE=""
gSUPABASE_REMOTE_PASSWORD=""

# supabase command variables
gSUPABASE_CLI_CMD="sudo npx supabase"
gSUPABASE_NEXTDR_SUB="supabase-NDR_DB"
gSUPABASE_NEXTDR_HOME=""
gSUPABASE_TEMPLATE_SUB="supabase"
gSUPABASE_TEMPLATE_HOME=""
gSUPABASE_REMOTE_TOKEN="********************************************" # Current NextDR Production remote DB token. Should never change but edit if needed.
gSUPABASE_REMOTE_PROJECT_REFERENCE="utcestwwfefexcnmjcjr" #Prod DB
#gSUPABASE_REMOTE_PASSWORD="@nextdr!123" #Prod DB new human readable

EXPRESS_MENU_OPTIONS="installprerequisites | pullschema | installsupabase | installservice | installui | installall | removesupabase | removeservice | removeui | removeall"

# ----------------------

# --- FUNCTIONS ---

function installHomeCheck ()
{
  local logSectionDesc="$gCOMPANY_NAME install home check"
  ndr_logSecStart "$logSectionDesc"
  
  # read from registry if package already present.
  local homeDir=""
  homeDir=$(ndr_getHomeDirReg)
  return_code=$?
  if [[ $return_code -eq 0 && -n "$homeDir" ]]; then
    ndr_logInfo "Found home directory entry [$homeDir] in registry."
    gNEXTDR_HOME_DIR="$homeDir"
    return 0
  fi

  if [ "$gExpressMode" -eq 1 ]; then
    if [ -z "$gNEXTDR_HOME_DIR" ]; then
      ndr_logError "gNEXTDR_HOME_DIR is not set in express mode. Please set it before proceeding."
      return 1
    fi
    
    # we should also create if it does not exist
    if [ ! -d "$gNEXTDR_HOME_DIR" ]; then
      ndr_logInfo "Creating directory [$gNEXTDR_HOME_DIR] for product and container installation."
      
      mkdir -p "$gNEXTDR_HOME_DIR"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to create directory [$gNEXTDR_HOME_DIR]. Please check permissions and try again."
        return 1
      fi
    fi

    #ndr_logInfo "Skipping install home check in express mode to directory [$gNEXTDR_HOME_DIR]."
    return 0
  fi

  echo "This will install $gCOMPANY_NAME and its Supabase Docker container under the directory [default: $gNEXTDR_HOME_DIR]."
  echo "If this is not the desired installation location, please select No and enter the correct location or select quit to return to the main menu."
  
  read -p "Are you sure you want to proceed? (Yes/no/quit) " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Qq]$ ]]; then
      ndr_logWarn "Operation cancelled."
      return 1

  elif [[ $REPLY =~ ^[Nn]$ ]]; then

    while true; do
      inputDir=""
      read -r -p "Enter the directory where you want to install the softare package: " inputDir
      if [ -z "$inputDir" ]; then
        ndr_logError "No directory entered. Please enter a valid directory."
        continue;
      fi
      
      confirm=""
      read -r -p "You entered: [$inputDir], is this correct? (Y/n)" confirm
      if [[ "$confirm" =~ ^[Nn]$ ]]; then
        ndr_logInfo "Please re-enter the directory."
        continue
      fi

      if [ ! -d "$inputDir" ]; then
        ndr_logWarn "Directory [$inputDir] does not exist, creating..."
        mkdir -p "$inputDir"
        return_code=$?
        if [ $return_code -ne 0 ]; then
          ndr_logError "Failed to create directory [$inputDir]. Please check permissions and try again."
          continue
        fi
        ndr_logInfo "Directory [$inputDir] created successfully."
      fi

      # Check if the directory is writable
      if [ ! -w "$inputDir" ]; then
        ndr_logError "Directory [$inputDir] is not writable. Please choose a different directory."
        continue
      fi

      # If we reach here, the directory is valid and writable
      ndr_logInfo "Using directory [$inputDir] for $gCOMPANY_NAME installation."
      gNEXTDR_HOME_DIR="$inputDir"
      
      break
    done
  else

    if [ ! -d "$gNEXTDR_HOME_DIR" ]; then
      ndr_logWarn "Directory [$gNEXTDR_HOME_DIR] does not exist, creating..."
      mkdir -p "$gNEXTDR_HOME_DIR"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to create directory [$gNEXTDR_HOME_DIR]. Please check permissions and try again."
        return 1
      fi
      ndr_logInfo "Directory [$gNEXTDR_HOME_DIR] created successfully."
    fi

    # Check if the directory is writable
    if [ ! -w "$gNEXTDR_HOME_DIR" ]; then
      ndr_logError "Directory [$gNEXTDR_HOME_DIR] is not writable. Please choose a different directory."
      return 1
    fi

    # If we reach here, the directory is valid and writable
    ndr_logInfo "Using directory [$gNEXTDR_HOME_DIR] for $gCOMPANY_NAME installation."

  fi
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# there are 2 types of objects that need to be removed:
# 1. the supabase container and its data. This can be done with the docker file and compose commands if present. Otherwise we can remove images and containers by name.
# 2. the supabase template and project directories and files.
function supabaseContainerCleanup ()
{
  # Cleanup code snippet taken from "projectDir/reset.sh"
  # clean up supbase container
  local logSectionDesc="Cleaning up Supabase Container"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" ]]; then
    # read from registry if package already present.
    local homeDir=""
    homeDir=$(ndr_getHomeDirReg)
    return_code=$?
    if [[ $return_code -eq 0 && -n "$homeDir" ]]; then
      ndr_logInfo "Found existing directory [$homeDir] in registry."
      gNEXTDR_HOME_DIR="$homeDir"
    fi
  fi
  
  if [[ -n "$gNEXTDR_HOME_DIR" ]]; then
    gSUPABASE_NEXTDR_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_NEXTDR_SUB}"
    gSUPABASE_TEMPLATE_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_TEMPLATE_SUB}"
  fi

  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  if [[ ! -d "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logInfo "Supabase project directory [$gSUPABASE_NEXTDR_HOME] does not exist. Nothing to clean up."
    return 0
  fi

  # step 1: stop and remove the supabase docker containers and images
  ndr_logInfo "Stopping and removing Supabase Docker container..."

  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }

  docker compose -p "ndr-supabase" -f "$gSUPABASE_NEXTDR_HOME/docker-compose.yml" -f "$gSUPABASE_NEXTDR_HOME/dev/docker-compose.dev.yml" down -v --remove-orphans
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logInfo "Failed to stop and remove Supabase Docker container"
    return 0
  fi
  ndr_logInfo "Stopped and removed Supabase Docker container"
  
  # this block is currently disabled.
  if true; then
    ndr_RegistryReadServiceEntries
    return_code=$?
    if [[ $return_code -ne 0 ]]; then
      ndr_logError "Failed to read service entries from registry."
      #return 1
    fi

    imageFailed=0

    for service in "${ndr_supabase_container_services[@]}"; do
    
      #local container_name="${ndr_supabase_container_service_container_names[$service]}"
      local image_name="${ndr_supabase_container_service_image_names[$service]}"
      local image_tag="${ndr_supabase_container_service_image_tags[$service]}"

      local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT"
      local dockerImage="$image_name:$image_tag"

      ndr_cleanupDockerImage "$dockerImage" "$dockerAppManageOptions"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to remove Docker image [$dockerImage]."
        imageFailed=1
        continue
      fi
    done

    if [ $imageFailed -eq 1 ]; then
      ndr_logError "One or more Supabase Docker images were not removed. Please check the logs for details."
      #return 1
    else
      ndr_logInfo "All Supabase Docker images removed."
    fi
  fi

  # step 2: remove all directories and files related to the supabase project
  ndr_logInfo "Removing Supabase project directories and files..."

  nnr_logInfo "Cleaning up bind-mounted directories."
  BIND_MOUNTS=(
    "$gSUPABASE_NEXTDR_HOME/volumes/db/data"
  )

  for DIR in "${BIND_MOUNTS[@]}"; do
    if [ -d "$DIR" ]; then
      ndr_logInfo "Deleting $DIR..."
      rm -rf "$DIR"
    else
      ndr_logWarn "Directory $DIR does not exist. Skipping bind mount deletion."
    fi
  done

  ndr_logInfo "Removing Supabase project directory [$gSUPABASE_NEXTDR_HOME] and template directory [$gSUPABASE_TEMPLATE_HOME]..."
  rm --recursive --force "$gSUPABASE_NEXTDR_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove Supabase project directory [$gSUPABASE_NEXTDR_HOME]. Please check permissions and try again."
    #return 1
  fi

  rm --recursive --force "$gSUPABASE_TEMPLATE_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove Supabase template directory [$gSUPABASE_TEMPLATE_HOME]. Please check permissions and try again."
    #return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# To install supabase CLI:
# https://supabase.com/docs/guides/local-development
function supabaseCLIInstall ()
{
  local logSectionDesc="Installing supabase CLI"
  ndr_logSecStart "$logSectionDesc"

  cd "$gNEXTDR_HOME_DIR"  || { ndr_logError "Failed to cd into home dir"; return 1; }
  
  # check if supabase cli is already installed
  $gSUPABASE_CLI_CMD -v
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "Supabase CLI already installed"
    return 0
  fi

  npm install supabase --save-dev
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to install supabase CLI."
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"
}

function supabaseLocalSchemaFileCheck ()
{
  local logSectionDesc="Local $gCOMPANY_NAME schema file check"
  ndr_logSecStart "$logSectionDesc"
  
  #if [ "$gExpressMode" -eq 1 ]; then
  #  ndr_logInfo "Skipping local $gCOMPANY_NAME schema file check in express mode."
  #  return 0
  #fi

  # check for local NDR schema file. 
  # first check if the file is present in the home directory.
  # If not in the home folder, check if the file is present in the same folder as the script.

  # If not present, prompt user to exit and run the supabase db pull command to generate the file.
  if [ -z "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    # active schema file variable not populated. populate and recheck
    if [ -f "${gNEXTDR_HOME_DIR}/${gNDR_SQL_SCHEMA_FILENAME}" ]; then
      gACTIVE_NDR_SQL_SCHEMA_FILE="${gNEXTDR_HOME_DIR}/${gNDR_SQL_SCHEMA_FILENAME}"
      ndr_logInfo "Found local $gCOMPANY_NAME schema file in home directory [$gACTIVE_NDR_SQL_SCHEMA_FILE]"
    elif [ -f "${PWD}/${gNDR_SQL_SCHEMA_FILENAME}" ]; then
      gACTIVE_NDR_SQL_SCHEMA_FILE="$PWD/$gNDR_SQL_SCHEMA_FILENAME"
      ndr_logInfo "Found local $gCOMPANY_NAME schema file in script folder [$gACTIVE_NDR_SQL_SCHEMA_FILE]"
    else
      ndr_logError "Local $gCOMPANY_NAME schema file not found in home directory or script folder."
    fi
  fi
  
  if [ ! -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    ndr_logWarn "WARNING: Local copy of $gCOMPANY_NAME schema file [$gNDR_SQL_SCHEMA_FILENAME] not found."
    ndr_logInfo "This file is required to seed the Supabase Docker container with the $gCOMPANY_NAME schema upon startup and without it the container DB would be empty and require manual injection of the SQL schema after installation."
    ndr_logInfo "Returning to the main menu to generate or supply the file."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function installSupabaseApplication ()
{
  # Create your desired working directory:
  # Tree should look like this
  # .
  # ├── supabase
  # └── supabase-project
  local logSectionDesc="Cleaning and creating new application folders"
  ndr_logSecStart "$logSectionDesc"
  
  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  # check up front if schema var is empy or file does not exist before doing any irreversable work
  if [ -z "$gACTIVE_NDR_SQL_SCHEMA_FILE" ] || [ ! -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
    ndr_logError "No active $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] found."
    return 1
  fi
  
  rm --recursive --force "$gSUPABASE_NEXTDR_HOME"
  rm --recursive --force "$gSUPABASE_TEMPLATE_HOME"
  mkdir "$gSUPABASE_NEXTDR_HOME"
  mkdir "$gSUPABASE_TEMPLATE_HOME"
  ndr_logSecEnd "$logSectionDesc"

  # To pull supabase docker code from GIT
  # This is a very lengthy process and will take several minutes or more depending on connectivity speed.
  local logSectionDesc="Cloning a local supabase container from GIT"
  ndr_logSecStart "$logSectionDesc"

  cd "$gSUPABASE_TEMPLATE_HOME" || { ndr_logError "Failed to cd into supabase template dir [$gSUPABASE_TEMPLATE_HOME]"; return 1; }

  # basic clone command
  #git clone --depth 1 https://github.com/supabase/supabase
  # advanced clone command
  git clone --filter=blob:none --no-checkout https://github.com/supabase/supabase "$gSUPABASE_TEMPLATE_HOME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Git clone failed."
    return 1
  fi
  ndr_logInfo "Git clone for Supabase completed."

  git sparse-checkout set --cone docker && git checkout master
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "GIT sparse checkout failed."
    return 1
  fi
  ndr_logInfo "Git sparse checkout for Supabase completed."

  cd "$gSCRIPT_HOME_DIR" || { ndr_logError "Failed to cd into install dir"; return 1; }
  ndr_logSecEnd "$logSectionDesc"

  # Copy the compose files over to your project
  local logSectionDesc="Copying the compose files to project"
  ndr_logSecStart "$logSectionDesc"
  composeSrcLocation="$gSUPABASE_TEMPLATE_HOME/docker/*"
  composeDestLocation="$gSUPABASE_NEXTDR_HOME"
  cp -rf $composeSrcLocation $composeDestLocation # note: DO NOT quote the args for cp command, otherwise it will not copy the files correctly with a stat failure.
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy Base docker compose files [$composeSrcLocation] to [$composeDestLocation]"
    return 1
  fi
  ndr_logInfo "Base docker compose files [$composeSrcLocation] copied to [$composeDestLocation]"
  
  if [[ $gSUPABASE_COMPOSE_COPY_MODE -eq 1 ]]; then
    # Copy the NextDR specific docker compose file to the project. This custom sql script will be used to create the NDR tables and RLS policies.
    # This mode is not currently the default behavior since we now prefer to edit the base docker package compose file directly.
    # Using the base docker compose file for the NDR container is a better practice since the file could evolve over time and this would ensure we always have the latest version.
    cp "$gSCRIPT_HOME_DIR/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE" "$composeDestLocation/docker-compose.yml"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to copy $gCOMPANY_NAME Docker compose file [$PWD/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$composeDestLocation]"
      return 1
    fi
    ndr_logInfo "Successfully copied $gCOMPANY_NAME Docker compose file [$PWD/$gNDR_SUPABASE_DOCKER_COMPOSE_FILE] to [$composeDestLocation]"
  else
    customizeSupabaseDockerComposeFile
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to customize $gCOMPANY_NAME Docker compose file."
      return 1
    fi
    ndr_logInfo "Successfully customized $gCOMPANY_NAME Docker compose file."
  fi

  ndr_ParseComposeFileServices "$composeDestLocation/docker-compose.yml"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to parse Docker compose file [$composeDestLocation/docker-compose.yml] service entries."
    return 1
  fi
  ndr_logInfo "Successfully parsed Docker compose file [$composeDestLocation/docker-compose.yml] service entries."

  ndr_RegistryAddKeyServiceEntries
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to add Docker service entries to registry"
    return 1
  fi
  ndr_logInfo "Successfully added Docker compose file service entries to registry."

  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Copying the $gCOMPANY_NAME schema file to project"
  ndr_logSecStart "$logSectionDesc"
  
  # Copy the NDR schema file to the project.
  # This custom sql script will be absorbed by the postgres db docker container at initial startup and will create the NDR tables and RLS policies.
  schemaDestLocation="${gSUPABASE_NEXTDR_HOME}/db-init-scripts"
  mkdir "$schemaDestLocation"
  cp -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" "$schemaDestLocation"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"
    return 1
  fi
  ndr_logInfo "Successfully copied $gCOMPANY_NAME Supabase schema file [$gACTIVE_NDR_SQL_SCHEMA_FILE] to [$schemaDestLocation]"

  ndr_logSecEnd "$logSectionDesc"

  # Copy the custom env vars
  local logSectionDesc="Copying the $gCOMPANY_NAME env file to project"
  ndr_logSecStart "$logSectionDesc"
  envSrcFile="${PWD}/${gNDR_ENV_MASTER_FILENAME}"
  envDestFile="${gSUPABASE_NEXTDR_HOME}/${gNDR_ENV_MODULE_FILENAME}"
  cp -f "$envSrcFile" "$envDestFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy $gCOMPANY_NAME Docker ENV file [$envSrcFile] to [$envDestFile]"
    return 1
  fi
  ndr_logInfo "$gCOMPANY_NAME Docker ENV file [$envSrcFile] copied to [$envDestFile]"
  ndr_logSecEnd "$logSectionDesc"

  # Go to the docker folder
  cd "$gSUPABASE_NEXTDR_HOME" || { ndr_logError "Failed to cd into supabase project dir [$gSUPABASE_NEXTDR_HOME]"; return 1; }
  
  # Pull the latest images
  local logSectionDesc="Pulling latest images for container"
  ndr_logSecStart "$logSectionDesc"
  docker compose pull
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose pull failed"
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  dockerContainerName="ndr-supabase"

  # Start the services (in detached mode)
  local logSectionDesc="Starting container images services"
  ndr_logSecStart "$logSectionDesc"
  
  # create the bridge network if it does not exist
  ndr_createDockerBridgeNetwork
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to create Docker bridge network."
    return 1
  fi
  ndr_logInfo "Docker bridge network created."

  docker compose -p "$dockerContainerName" up -d
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Docker compose up failed"
    return 1
  fi
  
  ndr_logSecEnd "$logSectionDesc"

  # check if the newly built Docker containers exist
  verifySupabaseContainersExist
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker container verification failed."
    return 1
  fi

  #After all the services have started you can see them running in the background:
  #local logSectionDesc="Querying container image service status"
  #ndr_logSecStart "$logSectionDesc"
  #docker compose ps
  #return_code=$?
  #if [ $return_code != 0 ]; then
  #  ndr_logError "Docker compose ps failed"
  #  return 1
  #fi
  
  # todo - clean up supabase folders?

  # Accessing Supabase Studio
  # You can access Supabase Studio through the API gateway on port 8000. For example: http://<your-ip>:8000, or localhost:8000 if you are running Docker locally.

  # You will be prompted for a username and password. By default, the credentials are:

  # Username: supabase
  # Password: this_password_is_insecure_and_should_be_updated

  # Need to change credentials and secrets to secure values
  # Update the ./docker/.env file with your own secrets. In particular, these are required:

  # POSTGRES_PASSWORD: the password for the postgres role.
  # JWT_SECRET: used by PostgREST and GoTrue, among others.

  # Dashboard authentication
  # The Dashboard is protected with basic authentication. The default user and password MUST be updated before using Supabase in production.
  # Update the following values in the ./docker/.env file:

  # DASHBOARD_PASSWORD: The default password for the Dashboard

  ndr_logInfo "Local supabase container installation is complete. You can access the Supabase Studio through the API gateway on port 8000."
  ndr_logInfo "http://<your-ip>:8000, or http://localhost:8000 if you are running # Docker locally"
  ndr_logInfo "Please check the .env file in the $gSUPABASE_NEXTDR_HOME for dashboard credentials."

  ndr_logSecEnd "$logSectionDesc"
  
  return 0
}

function verifySupabaseContainersExist
{
  local logSectionDesc="Verifying Supabase containers exist"
  ndr_logSecStart "$logSectionDesc"

  if [[ -z "$gNEXTDR_HOME_DIR" || -z "$gSUPABASE_NEXTDR_HOME" ]]; then
    ndr_logError "Invalid home or project directory."
    return 1
  fi

  ndr_RegistryReadServiceEntries
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to resd Supabase Docker service entries from registry."
    return 1
  fi

  ndr_logInfo "Verifying ${#ndr_supabase_container_services[@]} Supabase service entries."

  local containerFailed=0

  for service in "${ndr_supabase_container_services[@]}"; do
    local container_name="${ndr_supabase_container_service_container_names[$service]}"
    local image_name="${ndr_supabase_container_service_image_names[$service]}"
    local image_tag="${ndr_supabase_container_service_image_tags[$service]}"

    # check if the supabase container exists
    local containerCheck="$container_name" #"$image_name:$image_tag"
    ndr_verifyDockerContainerExists "$containerCheck"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Supabase Docker container [$containerCheck] does not exist."
      containerFailed=1
    fi
  done

  if [ $containerFailed -eq 0 ]; then
    ndr_logInfo "All Supabase Docker containers exist."
  else
    ndr_logError "One or more Supabase Docker containers do not exist. Please check the logs for details."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

}

function supabaseLogoutRemote ()
{
  local logSectionDesc="Unlinking local supabase project"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD unlink"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase unlink failed."
    #return 1
  fi

  # clean up a supabase folder if it exists
  supabaseCLILocalConfigDir="$gSCRIPT_HOME_DIR/supabase"
  if [ -d "$supabaseCLILocalConfigDir" ]; then
    ndr_logInfo "Removing any existing Supabase CLI init folder [$supabaseCLILocalConfigDir]"
    sudo rm --recursive --force "$supabaseCLILocalConfigDir"
  fi

  ndr_logSecEnd "$logSectionDesc"

  
  local logSectionDesc="Executing supabase remote logout"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD logout"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd <<< "y"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase logout failed."
    #return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# To begin linking to the remote instance
# eg. supabase link --project-ref <project-id> -password <password>
# You can get <project-id> from your project's dashboard URL: https://supabase.com/dashboard/project/<project-id>
# Alternatively, omitting the project ID will cause supabase to show all available remote db's to choose from.
# password is the master project password (not the supabase email account login)

function supabaseLinkRemote ()
{
  local logSectionDesc="Executing supabase remote login"
  ndr_logSecStart "$logSectionDesc"

  # perform a remote logout in case the user is already logged in.
  supabaseLogoutRemote

  cmd="$gSUPABASE_CLI_CMD login --token $gSUPABASE_REMOTE_TOKEN"
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase login with token failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Executing supabase init"
  ndr_logSecStart "$logSectionDesc"

  cmd="$gSUPABASE_CLI_CMD init --force"
  $cmd <<< "n"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Supabase init failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  local logSectionDesc="Linking local supabase project to remote master"
  ndr_logSecStart "$logSectionDesc"

  # There seems to be a bug in the supbase CLI where the link command (And others) still prompts for password even when the initial login with token was successful.
  # This results in several redundant prompts for password from a number of CLI commands.
  # As a workaround, lets prompt for the password interactively here and then pass it to the link command. If link command fails, the password was incorrect and retry the prompt up to 3 times.

  cmd="$gSUPABASE_CLI_CMD link --project-ref $gSUPABASE_REMOTE_PROJECT_REFERENCE"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do

    if [[ -z "$gSUPABASE_REMOTE_PASSWORD" ]]; then
      read -s -p "Enter the Supabase remote project password: " gSUPABASE_REMOTE_PASSWORD
      echo  # Print a newline after the password input
      
      if [[ -z "$gSUPABASE_REMOTE_PASSWORD" ]]; then
        ndr_logError "Supabase remote project password is required."
        continue
      else 
        cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
      fi
    fi

    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    # command failure could be due to an incoorect password so clear that to trigger a re-prompt.
    gSUPABASE_REMOTE_PASSWORD=""
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase link failed."
    # immediately run a supabase unlink command to remove the local link and clean up.
    $gSUPABASE_CLI_CMD unlink
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function supabasePullDatabaseReferenceSchemaFile ()
{
  local logSectionDesc="Executing supabase DB pull"
  ndr_logSecStart "$logSectionDesc"

  # todo: this fn should have 2 modes: 1. pull from git (default), 2. pull from remote db (optional, via express mode option or prompt?)

  cd "$gNEXTDR_HOME_DIR" || { ndr_logError "Failed to cd into home dir"; return 1; }

  # pre check for any existing migrations. If any exist, the DB pull may fail.
  # If this is the first time running this script, there should be no migrations present.
  # If there are migrations present, the user will need to run the migration repair command to remove the entry from the remote migration history table.
  # Set the directory to query
  
  
  # Check if the directory exists. Directory will not exist for fresh installs so skip check if not present.
  if [ ! -d "$gSUPABASE_MIGRATION_DIR" ]; then
    ndr_logInfo "Directory $gSUPABASE_MIGRATION_DIR does not exist, skipping precheck. "
    #return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$gSUPABASE_MIGRATION_DIR"/*)
  COUNT=0
  
  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ndr_logInfo "Removing existing migration file: [$FILE]"
      # delete original migration file from migrations folder
      rm "$FILE"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove preexisting migration file [$FILE]. Please remove manually."
        #return 1
      fi
      
      # Extract just the filename portion (strip path)
      BASENAME=$(basename "$FILE")

      # Check if filename contains an underscore
      if [[ "$BASENAME" != *_* ]]; then
        ndr_logWarn "Invalid filename format. No underscore found in [$BASENAME], skipping."
        continue
      fi

      # Extract the timestamp value before the first underscore
      gMIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

      if [ -z "$gMIGRATION_TIMESTAMP" ]; then
        ndr_logWarn "Failed to extract migration timestamp from filename [$BASENAME], skipping. "
        continue
      fi

      ndr_logInfo "Using migration timestamp: [$gMIGRATION_TIMESTAMP]"

      # run migration entry reset against remote db with the timestamp
      # this will remove the entry from the remote migration history table
      cmd="$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP"
      if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
        cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
      fi    
      if [[ "$gDebugMode" -eq 1 ]]; then
        cmd="$cmd --debug"
      fi
      
      max_retries=3
      attempt=1

      while true; do
        eval "$cmd"
        
        return_code=$?

        if [[ $return_code -eq 0 ]]; then
          # success
          break
        fi

        if [[ $attempt -ge $max_retries ]]; then
          ndr_logError "Max supabase command retries reached. Exiting. "
          return 1
        fi

        ndr_logWarn "Supabase command attempt $attempt failed, retrying."
        
        
        attempt=$((attempt + 1))
        sleep 1  # Optional: wait before retrying
      done

      if [ $return_code != 0 ]; then
        ndr_logWarn "Failed to remove migration entry [$gMIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP]"
        #return 1
      fi

      ndr_logInfo "Removing remote migration entry for timestamp: [$gMIGRATION_TIMESTAMP]"
      
      ((COUNT++))
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logInfo "No preexisting migration files found in directory. Proceeding with db pull."
  else
    ndr_logInfo "Removed [$COUNT] migration files from dir [$gSUPABASE_MIGRATION_DIR]"
  fi

  # The auth and storage schemas are excluded by default. Run supabase db pull --schema auth,storage again to diff them.
  max_retries=3
  attempt=1

  cmd="$gSUPABASE_CLI_CMD db pull --linked"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  while true; do
    eval "$cmd" <<< "n"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logError "Supabase db pull failed."
    return 1
  fi
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Capture any changes that you have made to your remote database before you went through in the process of pulling the schema.
# may prompt to update remote migration history table. select default.
# this process is lengthy and can take several minutes.

# If successful, Will output schema sql file to a folder stated in CLI output.
# supabase/migrations is now populated with a migration in <timestamp>_remote_schema.sql.
# eg. "Schema written to supabase\migrations\20250421192748_remote_schema.sql"

# query the output folder for the generated sql file. This will be under supabase/migrations.
# copy this file to the root project folder and delete the original from the migrations folder.
# parse the timestamp from the file name.
# Run the migration entry reset against the remote db with the timestamp. This will remove the entry from the remote migration history table.
# eg.  npx supabase migration repair --status reverted 20250422185759
function supabaseProcessRemoteMigration ()
{
  local logSectionDesc="Processing supabase remote migration file"
  ndr_logSecStart "$logSectionDesc"

  # Set the directory to query
  DIR="./supabase/migrations"
  
  # Check if the directory exists
  if [ ! -d "$DIR" ]; then
    ndr_logError "Directory $DIR does not exist. "
    return 1
  fi

  # Get list of regular files (excluding directories) in the directory
  FILES=("$DIR"/*)
  COUNT=0
  MIGRATION_FILENAME=""

  # Count only regular files (skip directories and others)
  for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
      ((COUNT++))
      MIGRATION_FILENAME="$FILE"
    fi
  done

  # Check count and respond accordingly
  if [ "$COUNT" -eq 0 ]; then
    ndr_logError "No migration files found in directory. "
    return 1
  elif [ "$COUNT" -gt 1 ]; then
    ndr_logError "Too many files migration found in directory. "
    return 1
  else
    ndr_logInfo "Found migration file: [$MIGRATION_FILENAME]"
    # You can now use $MIGRATION_FILENAME as needed
  fi

  # Extract just the filename portion (strip path)
  BASENAME=$(basename "$MIGRATION_FILENAME")

  # Extract the timestamp value before the first underscore
  gMIGRATION_TIMESTAMP=$(echo "$BASENAME" | cut -d'_' -f1)

  ndr_logInfo "Using migration timestamp: [$gMIGRATION_TIMESTAMP]"

  #destMigrationFile=$gMIGRATION_TIMESTAMP"_"$gNDR_SQL_SCHEMA_FILENAME
  destMigrationFile=$gNDR_SQL_SCHEMA_FILENAME
  # check for existing migration refrence file in root project folder.
  # if one exists, backup/rename with timestamp appended.
  if [[ -f "$destMigrationFile" ]]; then
    # Get the UNIX timestamp (modification time) of the file
    timestamp=$(stat -c %Y "$destMigrationFile")

    # Convert timestamp to readable format: YYYYMMDD_HHMMSS
    readable_time=$(date -d @"$timestamp" +"%Y%m%d_%H%M%S")
    
    # Build the new filename with timestamp appended
    filename_base="${destMigrationFile%.*}"
    filename_ext="${destMigrationFile##*.}"

    if [[ "$filename_base" -eq "$destMigrationFile" ]]; then
      # No extension case
      backup_filename="${filename_base}_${readable_time}"
    else
      backup_filename="${filename_base}_${readable_time}.${filename_ext}"
    fi

    # Move the file to the new filename
    mv "$destMigrationFile" "$backup_filename"

    ndr_logInfo "Backing up preexisting $gCOMPANY_NAME SQL schema file [$destMigrationFile] to [$backup_filename]"
  fi

  # copy migration file to root project folder
  cp -f "$MIGRATION_FILENAME" "$gNEXTDR_HOME_DIR/$destMigrationFile"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to copy migration file [$MIGRATION_FILENAME]"
    return 1
  fi
  ndr_logInfo "Copied migration file [$MIGRATION_FILENAME] to [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # cache copied migration file name for later use in docker container construction
  gACTIVE_NDR_SQL_SCHEMA_FILE=$gNEXTDR_HOME_DIR/$destMigrationFile
  ndr_logInfo "Caching local $gCOMPANY_NAME SQL schema file for use in docker container construction [$gNEXTDR_HOME_DIR/$destMigrationFile]"

  # delete original migration file from migrations folder
  rm "$MIGRATION_FILENAME"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove original migration file [$MIGRATION_FILENAME]. Please remove manually."
    #return 1
  fi

  # run migration entry reset against remote db with the timestamp
  # this will remove the entry from the remote migration history table
  cmd="$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP"
  if [ -n "$gSUPABASE_REMOTE_PASSWORD" ]; then
    cmd="$cmd --password '$gSUPABASE_REMOTE_PASSWORD'"
  fi    
  if [[ "$gDebugMode" -eq 1 ]]; then
    cmd="$cmd --debug"
  fi
  
  max_retries=3
  attempt=1

  while true; do
    eval "$cmd"
    
    return_code=$?

    if [[ $return_code -eq 0 ]]; then
      # success
      break
    fi

    if [[ $attempt -ge $max_retries ]]; then
      ndr_logError "Max supabase command retries reached. Exiting. "
      return 1
    fi

    ndr_logWarn "Supabase command attempt $attempt failed, retrying."
    
    
    attempt=$((attempt + 1))
    sleep 1  # Optional: wait before retrying
  done

  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to remove migration entry [$gMIGRATION_TIMESTAMP] from remote DB. Please run command to remove manually [$gSUPABASE_CLI_CMD migration repair --status reverted $gMIGRATION_TIMESTAMP]"
    #return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function supabasePushLocalMigration ()
{
  local logSectionDesc="Processing supabase local migration"
  ndr_logSecStart "$logSectionDesc"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function customizeSupabaseDockerComposeFile () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file"
  ndr_logSecStart "$logSectionDesc"

  # customize the docker compose file to add the bridge network
  customizeSupabaseDockerComposeFileAddBridgeNetwork
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add bridge network."
    return 1
  fi
  ndr_logInfo "Successfully customized $gCOMPANY_NAME supabase docker compose file to add bridge network."
  
  # customize the docker compose file to add the schema file
  customizeSupabaseDockerComposeFileAddSchema
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to customize supabase docker compose file to add schema file."
    return 1
  fi
  ndr_logInfo "Successfully customized $gCOMPANY_NAME supabase docker compose file to add schema file."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function customizeSupabaseDockerComposeFileAddBridgeNetwork () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file to add bridge network"
  ndr_logSecStart "$logSectionDesc"

  local COMPOSE_FILE="${gSUPABASE_NEXTDR_HOME}/docker-compose.yml"
  
  if [[ ! -f $COMPOSE_FILE ]]; then
    ndr_logError "Docker compose file [$COMPOSE_FILE] not found."
    return 1
  fi

  # Add the root-level external network if it doesn't exist
  # Check if the network 'ndr_bridge_net' is already defined
  network_check=$(yq eval ".networks.${NDR_DOCKER_BRIDGE_NETWORK_NAME}" "$COMPOSE_FILE")
  
  # Now check if it's null or empty
  if [[ "$network_check" == "null" || -z "$network_check" ]]; then
    yq eval ".networks.${NDR_DOCKER_BRIDGE_NETWORK_NAME}.external = true" "$COMPOSE_FILE" -i
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to add root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME'."
      return 1
    else
      ndr_logInfo "Root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' added."
    fi
  else
    ndr_logInfo "Root-level network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' already exists in compose file."
  fi

  # Get list of services
  mapfile -t services < <(yq eval '.services | keys | .[]' "$COMPOSE_FILE")
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to retrieve services from compose file [$COMPOSE_FILE]."
    return 1
  fi

  if [[ ${#services[@]} -eq 0 ]]; then
    echo "No services found in compose file."
    return 1
  fi

  # Iterate over services and add network assignment
  for service_name in "${services[@]}"; do
    # Check if service includes the bridge network
    yq eval ".services.${service_name}.networks[]" "$COMPOSE_FILE" | grep -qx "$NDR_DOCKER_BRIDGE_NETWORK_NAME"
    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Service '$service_name' already uses network '$NDR_DOCKER_BRIDGE_NETWORK_NAME', skipping."
      continue
    fi

    yq eval ".services.\"$service_name\".networks += [\"$NDR_DOCKER_BRIDGE_NETWORK_NAME\"]" "$COMPOSE_FILE" -i
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Error adding network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' to service '$service_name'. Aborting."
      return 1
    else
      ndr_logInfo "Network '$NDR_DOCKER_BRIDGE_NETWORK_NAME' added to service '$service_name'."
    fi
  done

  ndr_logInfo "All services updated successfully in compose file [$COMPOSE_FILE]."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function customizeSupabaseDockerComposeFileAddSchema () 
{
  local logSectionDesc="Customizing $gCOMPANY_NAME supabase docker compose file to add schema file"
  ndr_logSecStart "$logSectionDesc"
  
  local COMPOSE_FILE="${gSUPABASE_NEXTDR_HOME}/docker-compose.yml"
  temp_COMPOSE_FILE="${COMPOSE_FILE}.tmp"

  if [[ ! -f $COMPOSE_FILE ]]; then
    ndr_logError "Docker compose file [$COMPOSE_FILE] not found."
    return 1
  fi

  ndr_logInfo "Customizing $gCOMPANY_NAME project Docker compose file [$COMPOSE_FILE]."

  # Replace first occurrence of 'name: supabase' with 'name: ndr-supabase'
  #sed -i '0,/name: supabase/s//name: ndr-supabase/' "$COMPOSE_FILE"
  yq eval -i '.name = "ndr-supabase"' "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logWarn "Failed to change top level container stack entry to \"ndr-supabase\" in compose file."
    #return 1
  fi
  ndr_logInfo "Changed top level container stack entry to \"ndr-supabase\" in compose file."

  # Skip if both lines are already present
  
  # Check if insert_line1 exists
  # line 1 is a comment line that is used to initialize the database with NDR schema.
  # yq has a limitation with adding comments to an array and it is not critical to have this line in the compose file.
  if false; then
  insert_line1="# Initialize the database with NDR schema"
  grep -qF -- "$insert_line1" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "[$insert_line1] already exists in db volumes"
  else
    yq eval -i ".services.db.volumes += [\"$insert_line1\"]" "$COMPOSE_FILE"
    #yq eval ".services.db.volumes[-1] |= (. style=\"double\" | .comment = \"$insert_line1\")" -i "$COMPOSE_FILE"

    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Added [$insert_line1] to db volumes"
    else
      ndr_logError "Failed to add [$insert_line1] to db volumes"
    fi    
  fi
  fi

  # Check if insert_line2 exists
  insert_line2="./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
  grep -qF -- "$insert_line2" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "[$insert_line2] already exists in db volumes"
  else
    yq eval -i ".services.db.volumes += [\"$insert_line2\"]" "$COMPOSE_FILE"
    return_code=$?
    if [ $return_code -eq 0 ]; then
      ndr_logInfo "Added [$insert_line2] to db volumes"
    else
      ndr_logError "Failed to add [$insert_line2] to db volumes"
    fi
  fi

  # removing complicated awk logic and using the yq util to simplify the script
  if false; then
  grep -qF -- "$insert_line2" "$COMPOSE_FILE"
  return_code=$?
  if [ $return_code -eq 0 ]; then
    ndr_logInfo "$gCOMPANY_NAME schema file entries already present in compose file, skipping addition."
  else
    # if var is not empy AND file exists
    temp_flag="/tmp/awk_entries_added.flag"
    rm -f "$temp_flag"
    if [ -n "$gACTIVE_NDR_SQL_SCHEMA_FILE" ] && [ -f "$gACTIVE_NDR_SQL_SCHEMA_FILE" ]; then
      # Use awk to inject new lines at the end of the correct 'volumes:' section under 'services -> db'
      entriesAdded=$(awk -v flagfile="$temp_flag" '
        BEGIN {
          in_services = 0
          in_db = 0
          in_volumes = 0
          printed_extra = 0
          entriesAdded = 0
        }

        /^[[:space:]]*services:/ {
          in_services = 1
          print
          next
        }

        in_services && /^[[:space:]]{2}db:$/ {
          in_db = 1
          print
          next
        }

        in_db && /^[[:space:]]{2}[a-z0-9_-]+:$/ {
          in_db = 0
        }

        in_db && /^[[:space:]]+volumes:$/ {
          in_volumes = 1
          volume_indent = gensub(/[^[:space:]].*/, "", "g")
          print
          next
        }

        in_volumes {
          if ($0 ~ "^" volume_indent "  " && ($0 ~ /^ *- / || $0 ~ /^ *#|^ *$/)) {
            print
            next
          } else {
            if (!printed_extra) {
              print volume_indent "  # Initialize the database with NDR schema"
              print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
              printed_extra = 1
              entriesAdded = 1
            }
            in_volumes = 0
            print
            next
          }
        }

        {
          print
        }

        END {
          if (in_volumes && !printed_extra) {
            print volume_indent "  # Initialize the database with NDR schema"
            print volume_indent "  - ./db-init-scripts/NDR-schema.sql:/docker-entrypoint-initdb.d/NDR-schema.sql:Z"
            entriesAdded = 1
          }

          # Output result to a separate file
          print entriesAdded > flagfile
        }
      ' "$COMPOSE_FILE" > "$temp_COMPOSE_FILE")

      # Read the flag from temp file
      entriesAdded=$(cat "$temp_flag")
      rm -f "$temp_flag"
      if [ "$gDebugMode" -eq 1 ]; then
        ndr_logInfo "Entries added: $entriesAdded"
      fi
      
      if [ "$entriesAdded" -eq 0 ]; then
        ndr_logError "Awk failed to process compose file to add $gCOMPANY_NAME schema file entry to DB volumes section."
        rm -f "$temp_COMPOSE_FILE"
        return 1
      fi

      mv "$temp_COMPOSE_FILE" "$COMPOSE_FILE"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to move temp compose file [$temp_COMPOSE_FILE] to [$COMPOSE_FILE]."
        return 1
      fi
      ndr_logInfo "Moved temp compose file [$temp_COMPOSE_FILE] to [$COMPOSE_FILE]."
      ndr_logInfo "Successfully added $gCOMPANY_NAME schema file entry DB volumes section in compose file."
      
    else
      ndr_logWarn "No active $gCOMPANY_NAME supabase schema file present, skipping sql file injection."
    fi
  fi
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# https://supabase.com/docs/guides/self-hosting/docker#accessing-postgres
#supabaseLocalURL="postgres://postgres.your-tenant-id:your-super-secret-and-long-postgres-password@127.0.0.1:3000/postgres"
#npx supabase db push --local --db-url $supabaseLocalURL

# *** TODO - create NDR tables and rls/access configuration. This is best done via supabase CLI and using the link and db pull commands. This process requires the supabase CLI to be installed to run commands locally. This process also requires the supabase master project password and ref ID. If the master PWD is not available for scripting, the admin will need to run the link/pull and generate the sql script and share with group. From there the master sql schema script can be checked otu and run on the local instance to populate. If the master pwd is shared, teh user can use the This will generate the entire sql script to create all tables and This could be done either periodically when the schema changes or every time


# Creating docker container - example
# https://docs.docker.com/get-started/workshop/02_our_app/
# https://www.digitalocean.com/community/tutorials/how-to-build-a-node-js-application-with-docker#introduction
# create "dockerfile" file in project root and customize

# docker build -t getting-started .
# cd /path/to/getting-started-app (fill in your path here)

# ====================================================
# Main script execution
# ====================================================

function mainPullSupabaseReferenceSchema ()
{
  local logSectionDesc="Pulling Latest Supabase Reference Schema"
  ndr_logSecStart "$logSectionDesc"

  ndr_packagePrereqCheck
  
  supabaseCLIInstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  supabaseLinkRemote
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  supabasePullDatabaseReferenceSchemaFile
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  supabaseProcessRemoteMigration
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  supabaseLogoutRemote

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallSupabaseApplication ()
{
  local logSectionDesc="Installing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  ndr_packagePrereqCheck

  installHomeCheck
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  gSUPABASE_NEXTDR_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_NEXTDR_SUB}"
  gSUPABASE_TEMPLATE_HOME="${gNEXTDR_HOME_DIR}/${gSUPABASE_TEMPLATE_SUB}"
  
  supabaseLocalSchemaFileCheck
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  supabaseContainerCleanup
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  installSupabaseApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <image_name> <image_version> <repo_name> <repo_type>
function downloadDockerImage ()
{
  local logSectionDesc="Downloading Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  local imageRepoType="$4"
  
  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: downloadDockerImage <image_name> <image_version> <repo_name> <repo_type>"
    return 1
  fi

  if [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_GIT ]]; then
    ndr_logWarn "Mode not supported"
  
  elif  [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB ]]; then
    
    docker login -u "$NDR_DOCKER_REPO_ACCOUNT" -p "$NDR_DOCKER_REPO_ACCESS_TOKEN"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker login failed for [$NDR_DOCKER_REPO_ACCOUNT]."
      return 1
    fi
    ndr_logInfo "Docker login success for account [$NDR_DOCKER_REPO_ACCOUNT]"

    # repo tag format: <account>/<repo_name>:<{image_base_name}_{image_version}>
    local repoImageTag="$NDR_DOCKER_REPO_ACCOUNT/$imageRepoName:${dockerImageBaseName}_${dockerImageVersion}"

    # pull image from repo
    docker pull "$repoImageTag"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker pull failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker pull success for [$repoImageTag]."

    # check if the newly pulled Docker image is present
    ndr_verifyDockerImageExists "$repoImageTag"
    return_code=$?
    #2-error, 1-not found, 0-found
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker image verification failed for local copy of remote tag [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker remote repo image tag found [$repoImageTag]."

    # re tag it from remote format to local.
    docker tag "$repoImageTag" "$dockerImageName"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker tag failed for [$repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker tag success for [$repoImageTag] to [$dockerImageName]."
    
    # cleanup remote repo tag reference to local image.
    ndr_logInfo "Removing remote repo tag [$repoImageTag] from local image [$dockerImageName]"
    docker rmi "$repoImageTag"
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker rmi failed for [$repoImageTag]."
      #return 1
    fi

  else
    ndr_logError "Error, unknown Docker image repo mode."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallServiceApplication ()
{
  local logSectionDesc="Installing service application"
  ndr_logSecStart "$logSectionDesc"

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  local dbInstalled=$?
  if [[ "$dbInstalled" -ne 0 ]]; then
    read -p "The prerequisite $gCOMPANY_NAME Supabase database module does not appear to be currently installed. This module has an operational dependency on that module and may not be fully operable until that prerequisite is also installed. Would you like continue installing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with installation."
    fi
  fi

  local dockerImageBaseName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_SERVICE_HOME_LOC"
  local dockerContainerName="$NDR_SERVICE_CONTAINER_NAME"
  local dockerContainerPort="$NDR_SERVICE_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # pull image from repo
  downloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."

  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to create application registry."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallUIApplication ()
{
  local logSectionDesc="Installing UI application"
  ndr_logSecStart "$logSectionDesc"

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  local svcInstalled=$?
  if [[ "$svcInstalled" -ne 0 ]]; then
    read -p "The prerequisite $gCOMPANY_NAME service module does not appear to be currently installed. This module has an operational dependency on that module and may not be fully operable until that prerequisite is also installed. Would you like continue installing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with installation."
    fi
  fi

  local dockerImageBaseName=$NDR_UI_IMAGE_NAME
  local dockerImageVersion=$NDR_UI_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerRepoName="$NDR_DOCKER_SERVICE_REPO_NAME"
  local dockerRepoType="$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
  local containerFolder="$NDR_UI_HOME_LOC"
  local dockerContainerName="$NDR_UI_CONTAINER_NAME"
  local dockerContainerPort="$NDR_UI_CONTAINER_PORT"

  local dockerAppManageOptions="$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker application cleanup failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleanup success for account [$dockerImageName]"

  # pull image from repo
  downloadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$dockerRepoName" "$dockerRepoType"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image download failed for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker image download succeeded for [$dockerImageName]."

  # build container from image
  ndr_buildDockerApplicationContainer  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker container for [$dockerContainerName]."
    return 1
  fi
  ndr_logInfo "Docker container [$dockerContainerName] successfully built."
  
  ndr_RegistryCreate "$gPRODUCT_VERSION" "$gNEXTDR_HOME_DIR" "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainInstallAllApplications ()
{
  local logSectionDesc="Installing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainInstallSupabaseApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainInstallServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainInstallUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveSupabaseApplication ()
{
  local logSectionDesc="Removing Supabase Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  local svcInstalled=$?
  ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  local uiInstalled=$?
  if [[ "$svcInstalled" -eq 0 || "$uiInstalled" -eq 0 ]]; then
    read -p "Some dependent $gCOMPANY_NAME product modules appear to be currently installed. Removing this module may adversely affect the operation of these dependencies. Would you like continue removing this module? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with removal."
    fi
  fi

  supabaseContainerCleanup
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryRemoveServiceEntries
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to remoce Supabase registry services."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


function mainRemoveServiceApplication ()
{
  local logSectionDesc="Removing Service Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_mainCleanupServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveUIApplication ()
{
  local logSectionDesc="Removing UI Application"
  ndr_logSecStart "$logSectionDesc"

  local regName="$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME"
  ndr_isModuleInstalledReg "$regName"
  return_code=$?
  if [[ return_code -ne 0 ]]; then
    read -p "This product does not currently appear to be installed. Would you like to force remove any partial or existing installation? (Y|n)" -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Operation cancelled, returning to main menu."
      return 1
    else
      ndr_logInfo "Proceeding with forced removal."
    fi
  fi

  ndr_mainCleanupUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_RegistryAddKey "$regName" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to update registry entry [$regName -> $gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED]."
    #return 1
  fi

  ndr_RegistryUninstall
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainRemoveAllApplications ()
{
  local logSectionDesc="Removing all applications"
  ndr_logSecStart "$logSectionDesc"

  mainRemoveSupabaseApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainRemoveServiceApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi
  
  mainRemoveUIApplication
  return_code=$?
  if [ $return_code -ne 0 ]; then
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# command line parsing
# ====================================================
function mainParseCommandLineArgs ()
{
  local logSectionDesc="Parsing command line arguments"
  ndr_logSecStart "$logSectionDesc"

  # Default values
  
  ndr_parseCommandLineArgs "$@"

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --password|-p)
        if [[ -n "$2" && "$2" != --* ]]; then
          gSUPABASE_REMOTE_PASSWORD="$2"
          #ndr_logInfo "Supabase set to [$gSUPABASE_REMOTE_PASSWORD]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --remote-token|-rt)
        if [[ -n "$2" && "$2" != --* ]]; then
          supabaseRemotetoken="$2"
          ndr_logInfo "Supabase $1 set to [$gSUPABASE_REMOTE_TOKEN]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --dest|-d)
        if [[ -n "$2" && "$2" != --* ]]; then
          gNEXTDR_HOME_DIR="$2"
          ndr_logInfo "Home directory set to [$gNEXTDR_HOME_DIR]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --help|-h)
        ndr_logWarn "Usage: $0 --express <$EXPRESS_MENU_OPTIONS> --password <password> [--debug]."
        return 1
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Express install mode
# ====================================================
function mainExpressInstallMode ()
{
  local logSectionDesc="Executing express install mode"
  ndr_logSecStart "$logSectionDesc"

  # Check if express mode is enabled and an option is provided
  if [ "$gExpressMode" != 1 ]; then
    ndr_logError "Express mode is not enabled. Use --express to enable."
    return 1
  fi
  if [ -z "$gExpressOption" ]; then
    ndr_logError "No express option provided. Use --express <option>."
    return 1
  fi

  case "$gExpressOption" in
    installprerequisites)
      ndr_checkAndInstallPrerequisites
      ;;
    pullschema)
      mainPullSupabaseReferenceSchema
      ;;
    installsupabase)
      mainInstallSupabaseApplication
      ;;
    installservice)
      mainInstallServiceApplication
      ;;
    installui)
      mainInstallUIApplication
      ;;
    installall)
      mainInstallAllApplications
      ;;
    removesupabaseapp|removesupabase)
      mainRemoveSupabaseApplication
      ;;
    removeserviceapp|removeservice)
      mainRemoveServiceApplication
      ;;
    removeuiapp|removeui)
      mainRemoveUIApplication
      ;;
    removeall)
      mainRemoveAllApplications
      ;;
    test)
      customizeSupabaseDockerComposeFileAddBridgeNetwork
      #ndr_ParseComposeFileServices "/opt/nextdr/master/install/NDR-docker-compose.yml"
      #ndr_RegistryAddKeyServiceEntries
      #cat /var/lib/NextDR/registry.json
      #ndr_RegistryReadServiceEntries
      #ndr_RegistryRemoveServiceEntries
      #cat /var/lib/NextDR/registry.json
      #ndr_checkAndInstallDocker
      ;;
    *)
      # print usage and exit
      ndr_logWarn "Invalid option. Please use one of the following options: installprerequisites, pullschema, installcontainer, pullandinstall or cleancontainer."
      return 1
      ;;
  esac
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Interactive menu mode
# ====================================================
function mainInteractiveMenuMode ()
{
  local logSectionDesc="Executing interactive menu mode"
  ndr_logSecStart "$logSectionDesc"
  
  MENU_ITEM_1="Check and install software package prerequisites"
  MENU_ITEM_2="Pull Supabase reference schema file from repository"
  MENU_ITEM_3="Install the $gCOMPANY_NAME Supabase database in a local Docker container"
  MENU_ITEM_4="Install the $gCOMPANY_NAME Service application in a local Docker container"
  MENU_ITEM_5="Install the $gCOMPANY_NAME UI application in a local Docker container"
  MENU_ITEM_6="Install all modules"
  MENU_ITEM_7="Remove existing $gCOMPANY_NAME Supabase Docker application"
  MENU_ITEM_8="Remove existing $gCOMPANY_NAME Service Docker application"
  MENU_ITEM_9="Remove existing $gCOMPANY_NAME UI Docker application"
  MENU_ITEM_10="Remove all modules"

  while true; do
    local supabaseAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)
    local serviceAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)
    local uiAppInstalled=$(if ndr_isModuleInstalledReg "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME" >/dev/null; then echo "(installed)"; else echo "(not installed)"; fi)

    echo "===================================================================="
    echo "$gCOMPANY_NAME Software Installation and Package Management Menu"
    echo "===================================================================="
    echo "Please select an option:"
    echo ""
    echo "1. $MENU_ITEM_1"
    echo "2. $MENU_ITEM_2"
    echo "3. $MENU_ITEM_3" "$supabaseAppInstalled"
    echo "4. $MENU_ITEM_4" "$serviceAppInstalled"
    echo "5. $MENU_ITEM_5" "$uiAppInstalled"
    echo "6. $MENU_ITEM_6"
    echo "7. $MENU_ITEM_7" "$supabaseAppInstalled"
    echo "8. $MENU_ITEM_8" "$serviceAppInstalled"
    echo "9. $MENU_ITEM_9" "$uiAppInstalled"
    echo "10. $MENU_ITEM_10"
    echo "q. Exit"
    echo ""
    read -p "Enter your choice [1-n]: " choice

    case $choice in
      1)
        echo "$MENU_ITEM_1. This will check for NPM/NPX, GIT, Docker, etc and prompt for interactive install."
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        # default YES logic
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_checkAndInstallPrerequisites
        fi
        ;;
      2)
        echo "$MENU_ITEM_2"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainPullSupabaseReferenceSchema
        fi
        ;;
      3)
        echo "$MENU_ITEM_3. Warning, this will overwrite any existing instance and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallSupabaseApplication
        fi
        ;;
      4)
        echo "$MENU_ITEM_4. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallServiceApplication
        fi
        ;;
      5)
        echo "$MENU_ITEM_5. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallUIApplication
        fi
        ;;
      6)
        echo "$MENU_ITEM_6. Warning, this will overwrite any existing instances and install a clean copy. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (Y|n) " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainInstallAllApplications
        fi
        ;;
      7)
        echo "$MENU_ITEM_7. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveSupabaseApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
     8)
        echo "$MENU_ITEM_8. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveServiceApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      9)
        echo "$MENU_ITEM_9. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveUIApplication
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      10)
        echo "$MENU_ITEM_10. Warning, this will completely remove any existing instance. This action cannot be undone!"
        read -p "Are you sure you want to proceed? (y|N) " -n 1 -r
        echo    # Move to a new line
        # default NO logic
        if [[ $REPLY =~ ^[Yy]$ ]]; then
          mainRemoveAllApplications
        else
          ndr_logInfo "Operation cancelled, returning to main menu."
        fi
        ;;
      Q|q)
        echo "Exiting..."
        break
        ;;
      t)
        #test section
        # unit test - remove for production
        #ndr_removeDockerPackages
        #break
        gACTIVE_NDR_SQL_SCHEMA_FILE="$PWD/$gNDR_SQL_SCHEMA_FILENAME" 
        gSUPABASE_NEXTDR_HOME="/opt/nextdr/home"
        customizeSupabaseDockerComposeFile
        break
        ;;
      *)
        echo "Invalid option. Please try again."
        ;;
    esac

    echo ""
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function main ()
{
  echo "🕓 Started at $(date)"

  mainParseCommandLineArgs "$@"
  return_code=$?
  if [ $return_code != 0 ]; then
    return 1
  fi

  ndr_osTypeCheck

  if [[ "$gExpressMode" -eq 1  && -n "$gExpressOption" ]]; then
    ndr_logInfo "Express install option selected. Skipping all prompts and running with supplied values."
    mainExpressInstallMode
    echo "🕓 Finished at $(date)"
    return 0
  fi

  mainInteractiveMenuMode

  echo "🕓 Finished at $(date)"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
  exit 0
fi

# TC 1: fresh docker only install, skip pull
# TC 2: existing docker only install (remove and install), skip pull
# TC 3: fresh docker install with db pull
# TC 4: existing docker install (skip) with db pull
# tc 5: test express options

# issues
# change install log file to /var/log/ndrInstall.log
# move supabase cli install to main prereq function?
# move env file check before cleanup?
# pullschema would either eventually go away or be hidden. We shhould hide in the interactive menu, but still expose in express so we can administer discretely.
# user should not be able to perform this action and schema file should only be available from GITHUB eventually for production (schema file pull should eventually be done as part of supabase app install and not separate step).
# there may a GIT repo conflict between the current local repo and pulling the supabase files. Pulling in the install dir may have a tag conflict.
# clean up supabase subfolder under script dir after schema pull.
# if docker engine install continues to have issues, use alternalte convenience script: https://get.docker.com/

# notes
# docker desktop in linux requires KVM support. This is not available in VirtualBox when running a linux VM on windows bare metal (requires linux on linux host, then you cna enable the KVM option in virtual box in the processor tab).
# Docker does not provide support for running Docker Desktop for Linux in nested virtualization scenarios. 
# We recommend that you run Docker Desktop for Linux natively on supported distributions.
# docker core services are fine and will run all required modules for NextDR supabase.
# https://docs.docker.com/desktop/setup/install/linux/

# Main linux distro families. test with one of each for coverage of all distros.
# debian: debian, ubuntu, linux mint, pop!_os
# redhat: redhat, centos, fedora, rocky linux
# suse: suse, opensuse
# less common, not required for testing
# arch: arch linux, manjaro
# alpine: alpine linux
# osx: macos

# VM notes
# after copying initial file
# sudo chown -R vboxuser /path/to/directory
# sudo chmod -R 777 /path/to/directory
# dos2unix /path/to/directory/filename.sh