#!/bin/bash

source "$(dirname "${BASH_SOURCE[0]}")/ndrDocker.sh"

# --- CONFIGURATION ---

gLOG_FILE="/var/log/ndrDevops.log"

EXPRESS_MENU_OPTIONS="installprerequisites | generatesshkey | pullrepo | buildservicecontainer | builduiapp | uploadimages | cleanupserviceapp | cleanupuiapp | fullbuild"

# ----------------------

# --- FUNCTIONS ---

function mainGenerateGitHubPublicKey ()
{
  local logSectionDesc="Generating GitHub Public Key"
  ndr_logSecStart "$logSectionDesc"

  echo "Warning - This script will generate a NEW Github public SSH key file. This process will also override the existing Github public key configured in your account. This action cannot be undone! If you do not wish to generate a new key, please exit back to the main menu."
  read -p "Are you sure you want to proceed? [y/N] " -n 1 -r
  echo    # Move to a new line
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    ndr_logInfo "Proceeding to generate a new Github key..."
  else
    ndr_logError "Operation cancelled."
    return 1
  fi

  # 🔍 Precheck: Remove stale key files if they exist
  ndr_logInfo "Removing any stale SSH key files..."
  rm -f "$SSH_KEY_FILE" "$SSH_PUB_KEY_FILE"
  # also remove local key files
  LOCAL_KEY_FILE="$gSCRIPT_HOME_DIR/$SSH_KEY_FILE_NAME"
  LOCAL_PUB_KEY_FILE="$gSCRIPT_HOME_DIR/${SSH_PUB_KEY_FILE}"
  rm -f "$LOCAL_KEY_FILE" "$LOCAL_PUB_KEY_FILE"

  ssh-keygen -t ed25519 -C "$NDR_GIT_SSH_KEY_USER_EMAIL"  -f "$SSH_KEY_FILE" -N "$gSSH_Passphrase"
  #ssh-keygen -t ed25519 -C "$NDR_GIT_SSH_KEY_USER_EMAIL"  -f "$SSH_KEY_FILE" -N ""
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "generating key "
    return 1
  fi
  
  eval "$(ssh-agent -s)"
  cmd="ssh-add $SSH_KEY_FILE"
  $cmd
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "adding key file "
    return 1
  fi

  # Check if file exists before reading
  if [[ ! -f "$SSH_PUB_KEY_FILE" ]]; then
    ndr_logError "SSH public key file does not exist at: $SSH_PUB_KEY_FILE "
    return 1
  fi

  SSH_PUBLIC_KEY=$(cat "$SSH_PUB_KEY_FILE")
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "reading key file "
    return 1
  fi
  
  # Check if output is empty
  if [[ -z "$SSH_PUBLIC_KEY" ]]; then
    ndr_logError "Public key file is empty "
    return 1
  fi

  eval "$(ssh-agent -s)"

  # copy the keys to the local folder
  cp "$SSH_KEY_FILE" "$LOCAL_KEY_FILE"
  cp "$SSH_PUB_KEY_FILE" "$LOCAL_PUB_KEY_FILE"

  # add the public key to your GitHub account manually (https://github.com/settings/keys)
  #eg. ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPqTYtDVbwkzjuPQuukTMmaQqE+zYRUXVJuQMPHggP2X <EMAIL>
  ndr_logInfo "\n\nPlease add the following public key to your GitHub account [https://github.com/settings/keys]:"
  ndr_logInfo "$SSH_PUBLIC_KEY"
  ndr_logInfo "\n\nAfter adding the key, please upload the new key file [$SSH_KEY_FILE] to the $gCOMPANY_NAME Github repository."
  ndr_logInfo "\n\nPress any key to continue..."
  read -r -n 1 -s
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function authenticateGitSSHKey ()
{
  local logSectionDesc="🔐 SSH Key Authentication"
  ndr_logSecStart "$logSectionDesc"
  
  LOCAL_KEY_FILE="$gSCRIPT_HOME_DIR/$SSH_KEY_FILE_NAME"
  LOCAL_PUB_KEY_FILE="${LOCAL_KEY_FILE}.pub"

  if [[ $gSSH_KEY_AUTHENTICATED -eq 1 ]]; then
    ndr_logInfo "SSH key already authenticated."
    ndr_logSecEnd "$logSectionDesc"
    return 0
  fi

  # check git identities and populate if missing.
  gitUser=$(git config user.name)
  if [[ -z "$gitUser" ]]; then
    ndr_logWarn "$gCOMPANY_NAME devops git user name is not set for this local repo."
    read -p "Please specify a valid user name [$NDR_GIT_LOCAL_REPO_USER_NAME]: " -r gitUser
    echo    # Move to a new line
    gitUser="${gitUser:-NDR_GIT_LOCAL_REPO_USER_NAME}"
    ndr_logInfo "Configuring user name [$gitUser] for local git repo operations."

    git config --global user.name "$gitUser"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to configure user name [$gitUser] for local git repo operations."
      return 1
    fi
  fi
  
  gitEmail=$(git config user.email)
  if [[ -z "$gitEmail" ]]; then
    ndr_logWarn "$gCOMPANY_NAME devops git user email is not set for this local repo."
    read -p "Please specify a valid user email [$NDR_GIT_LOCAL_REPO_USER_EMAIL]: " -r gitEmail
    echo    # Move to a new line
    gitEmail="${gitEmail:-NDR_GIT_LOCAL_REPO_USER_EMAIL}"
    ndr_logInfo "Configuring user email [$gitEmail] for local git repo operations."

    git config --global user.email "$gitEmail"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to configure user email [$gitEmail] for local git repo operations."
      return 1
    fi
  fi

  # Check if file exists before proceeding
  if [[ ! -f $SSH_KEY_FILE ]]; then
    ndr_logWarn "SSH key file does not exist at: $SSH_KEY_FILE"

    # not present in users home .ssh folder, copy from install folder to home dir
    if [[ -f "$LOCAL_KEY_FILE" ]]; then
      ndr_logInfo "Found SSH key file in install folder. Copying to $SSH_KEY_DIR..."
      cp "$LOCAL_KEY_FILE" "$SSH_KEY_DIR/"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to copy SSH key file [$LOCAL_KEY_FILE] to $SSH_KEY_DIR. Please copy manually."
        return 1
      fi
    else
      ndr_logError "SSH key file [$LOCAL_KEY_FILE] not found in install folder: $gSCRIPT_HOME_DIR"
      return 1
    fi
  else
    ndr_logInfo "SSH key file found at: $SSH_KEY_FILE"
  fi
  
  # Check if file exists before proceeding
  if [[ ! -f $SSH_PUB_KEY_FILE ]]; then
    ndr_logWarn "SSH key file does not exist at: $SSH_PUB_KEY_FILE"

    # not present in users home .ssh folder, copy from install folder to home dir
    if [[ -f "$LOCAL_PUB_KEY_FILE" ]]; then
      ndr_logInfo "Found SSH key file in install folder. Copying to $SSH_KEY_DIR..."
      cp "$LOCAL_PUB_KEY_FILE" "$SSH_KEY_DIR/"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to copy SSH key file [$LOCAL_PUB_KEY_FILE] to $SSH_KEY_DIR. Please copy manually."
        return 1
      fi
    else
      ndr_logError "SSH key file [$LOCAL_PUB_KEY_FILE] not found in install folder: $gSCRIPT_HOME_DIR"
      return 1
    fi
  else
    ndr_logInfo "SSH key file found at: $SSH_PUB_KEY_FILE"
  fi

  # 🔒 Secure the key files
  sudo chmod 600 "$SSH_KEY_FILE"
  #sudo chmod 600 "$SSH_PUB_KEY_FILE"
  
  eval "$(ssh-agent -s)"

  # Ensure SSH access
  ssh -T ************** 2>&1
  return_code=$?

  # On success, ssh returns 1 for "successfully authenticated", so only check the exit code
  if [[ $return_code -ne 1 ]]; then
    ndr_logError "SSH auth failed. Set up your SSH keys. "
    return 1
  fi

  git config --global credential.helper store
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "configuring Git credential helper store "
    return 1
  fi

  git config --global credential.helper 'cache --timeout=3600'
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "configuring Git credential helper timeout "
    return 1
  fi

  gSSH_KEY_AUTHENTICATED=1
  
  ndr_logInfo "✅ SSH authentication OK"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainPullGitRepo ()
{
  local logSectionDesc="Git Repo Pull/Update"
  ndr_logSecStart "$logSectionDesc"
  
  authenticateGitSSHKey
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to authenticate with SSH key."
    return 1
  fi

  # if not otherwise specified, assume the repo is in the parent folder of the install folder where this script is located
  if [[ -z $NDR_GIT_LOCAL_REPO_DEST_DIR ]]; then
    cd ".." || { ndr_logError "Failed to cd into parent dir"; return 1; }
    NDR_GIT_LOCAL_REPO_DEST_DIR=$PWD
  fi

  ndr_logInfo "Pulling from [$NDR_GITHUB_REPO_URL] branch/tag [$NDR_GIT_REPO_BRANCH_OR_TAG] to folder [$NDR_GIT_LOCAL_REPO_DEST_DIR]."

  # Scenarios to handle
  # 1. completely clean install. 
  # This really cant happen because the shell scripts would have been checked out from a prior git pull (chicken and egg scenario) unless dest dir is a completely different location and not an ancestor of PWD.
  # 2. directory present, no .git repo detected. do a temp dir checkout, copy back to actual dest, then force update checkout.
  # 3. directory and .git repo present, update.
  GIT_UPDATE_MODE_UNKNOWN=0
  GIT_UPDATE_MODE_CLEAN_CHECKOUT=1
  GIT_UPDATE_MODE_EMPTY_REPO_DIR_EXISTS=2
  GIT_UPDATE_MODE_UPDATE_EXISTING_REPO=3
  GIT_UPDATE_MODE=$GIT_UPDATE_MODE_UNKNOWN

  if [[ ! -d "$NDR_GIT_LOCAL_REPO_DEST_DIR" ]]; then
    # dest dir does not exist, clean install
    ndr_logInfo "📁 No repository exists at $NDR_GIT_LOCAL_REPO_DEST_DIR, checking out new..."
    GIT_UPDATE_MODE=$GIT_UPDATE_MODE_CLEAN_CHECKOUT
  elif [[ -d "$NDR_GIT_LOCAL_REPO_DEST_DIR/.git" ]]; then
    # dest dir and .git file exist, simple repo update.
    ndr_logInfo "📁 Repository already exists at $NDR_GIT_LOCAL_REPO_DEST_DIR, updating..."
    GIT_UPDATE_MODE=$GIT_UPDATE_MODE_UPDATE_EXISTING_REPO
  elif [[ -d "$NDR_GIT_LOCAL_REPO_DEST_DIR" && ! -d "$NDR_GIT_LOCAL_REPO_DEST_DIR/.git" ]]; then
    # dest dir exists, but no .git file.
    ndr_logInfo "📁 Directory already exists at $NDR_GIT_LOCAL_REPO_DEST_DIR but no .git repo found, attempting to clone repo into it..."
    GIT_UPDATE_MODE=$GIT_UPDATE_MODE_EMPTY_REPO_DIR_EXISTS
  fi

  # If repo already exists, try simply updating
  if [[ $GIT_UPDATE_MODE -eq "$GIT_UPDATE_MODE_UPDATE_EXISTING_REPO" ]]; then
    
    # Check if branch/tag exists locally
    if git show-ref --verify --quiet "refs/heads/$NDR_GIT_REPO_BRANCH_OR_TAG" || git rev-parse --verify "$NDR_GIT_REPO_BRANCH_OR_TAG" >/dev/null 2>&1; then
      ndr_logInfo "🔄 Branch/tag '$NDR_GIT_REPO_BRANCH_OR_TAG' exists locally. Pulling latest changes..."

      #git checkout "$NDR_GIT_REPO_BRANCH_OR_TAG"
      #git pull origin "$NDR_GIT_REPO_BRANCH_OR_TAG"

      git fetch origin
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to fetch git origin."
        return 1
      fi
      ndr_logInfo "git fetch origin successful."

      git pull origin "$(git rev-parse --abbrev-ref HEAD)"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to perform git pull."
        return 1
      fi
      ndr_logInfo "git pull successful."

      git checkout -- .
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to perform git checkout."
        return 1
      fi
      ndr_logInfo "git checkout successful."

    else
      btOutput=$(git describe --all --exact-match HEAD 2>/dev/null || git rev-parse --short HEAD)
      localBranchTag=${btOutput#*/}
      ndr_logWarn "Warning, local branch/tag '$localBranchTag' does not match target '$NDR_GIT_REPO_BRANCH_OR_TAG' in [$NDR_GIT_LOCAL_REPO_DEST_DIR]. Cannot overwrite repo tag, aborting..."
      return 1

    fi

  elif [[ $GIT_UPDATE_MODE -eq "$GIT_UPDATE_MODE_EMPTY_REPO_DIR_EXISTS" ]]; then
    
    cd "$NDR_GIT_LOCAL_REPO_DEST_DIR" || { ndr_logError "Failed to cd into dest dir [$NDR_GIT_LOCAL_REPO_DEST_DIR]"; return 1; }
    
    # if the repo folder already exists (but empty), we need to do some additional commands to update in this folder.    
    if (( 0 )); then
      # Clone just the repository's .git folder (excluding files as they are already in
      # `existing-dir`) into an empty temporary directory
      DEST_DIR_TMP="$NDR_GIT_LOCAL_REPO_DEST_DIR/existing-dir.tmp"
      mkdir -p "$DEST_DIR_TMP"

      git clone --no-checkout --branch "$NDR_GIT_REPO_BRANCH_OR_TAG" "$NDR_GITHUB_REPO_URL" "$DEST_DIR_TMP" # might want --no-hardlinks for cloning local repo
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to clone git repo [$NDR_GITHUB_REPO_URL] into [$DEST_DIR_TMP]."
        return 1
      fi
      ndr_logInfo "Cloned git repo [$NDR_GITHUB_REPO_URL] into [$DEST_DIR_TMP]."

      # Move the .git folder to the directory with the files.
      # This makes `existing-dir` a git repo.
      mv "$DEST_DIR_TMP/.git" "$NDR_GIT_LOCAL_REPO_DEST_DIR/"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to move git repo from [$DEST_DIR_TMP/.git] to [$NDR_GIT_LOCAL_REPO_DEST_DIR]."
        return 1
      fi
      ndr_logInfo "Moved git repo from [$DEST_DIR_TMP/.git] to [$NDR_GIT_LOCAL_REPO_DEST_DIR]."

      # Delete the temporary directory
      rmdir "$DEST_DIR_TMP"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to remove temporary directory [$DEST_DIR_TMP]."
        #return 1
      fi
      ndr_logInfo "Removed temporary directory [$DEST_DIR_TMP]."
      
      # git thinks all files are deleted, this reverts the state of the repo to HEAD.
      # WARNING: any local changes to the files will be lost.
      git reset --hard HEAD
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "git reset failed."
        return 1
      fi
      ndr_logInfo "git reset successful."

      git checkout "$NDR_GIT_REPO_BRANCH_OR_TAG" -f
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to checkout '$NDR_GIT_REPO_BRANCH_OR_TAG'."
        return 1
      fi
      ndr_logInfo "Checkout success '$NDR_GIT_REPO_BRANCH_OR_TAG'."
    fi

    git init -b "$NDR_GIT_REPO_BRANCH_OR_TAG"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "git init failed for branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG]."
      return 1
    fi
    ndr_logInfo "git init successful for branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG]."

    git remote add origin "$NDR_GITHUB_REPO_URL"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed add git origin for repo [$NDR_GITHUB_REPO_URL]."
      return 1
    fi
    ndr_logInfo "Added git origin for repo [$NDR_GITHUB_REPO_URL]."

    git fetch
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "git fetch failed."
      return 1
    fi
    ndr_logInfo "git fetch success."

    #git reset origin/master
    git checkout -t origin/master -f
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "git checkout failed."
      return 1
    fi
    ndr_logInfo "git checkout success."

  elif  [[ $GIT_UPDATE_MODE -eq "$GIT_UPDATE_MODE_CLEAN_CHECKOUT" ]]; then
    mkdir -p "$NDR_GIT_LOCAL_REPO_DEST_DIR"
    cd "$NDR_GIT_LOCAL_REPO_DEST_DIR" || { ndr_logError "Failed to cd into dest dir [$NDR_GIT_LOCAL_REPO_DEST_DIR]"; return 1; }

    git init "$NDR_GIT_LOCAL_REPO_DEST_DIR"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "git init failed for dest dir [$NDR_GIT_LOCAL_REPO_DEST_DIR]."
      return 1
    fi
    ndr_logInfo "git init successful for dest dir [$NDR_GIT_LOCAL_REPO_DEST_DIR]."

    git remote add origin "$NDR_GITHUB_REPO_URL"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed add git origin for repo [$NDR_GITHUB_REPO_URL]."
      return 1
    fi
    ndr_logInfo "Added git origin for repo [$NDR_GITHUB_REPO_URL]."

    git fetch --depth 1 origin "$NDR_GIT_REPO_BRANCH_OR_TAG"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to perform git fetch on branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG]."
      return 1
    fi
    ndr_logInfo "git fetch on branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG] successful."
    
    git checkout -b "$NDR_GIT_REPO_BRANCH_OR_TAG" origin/"$NDR_GIT_REPO_BRANCH_OR_TAG"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to checkout git repo on branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG]."
      return 1
    fi
    ndr_logInfo "git repo checkout on branch or tag [$NDR_GIT_REPO_BRANCH_OR_TAG] successful."
    
  else
    ndr_logError "Unknown git update mode."
    return 1
  fi

  ndr_logInfo "Repo is up to date on [$NDR_GIT_REPO_BRANCH_OR_TAG]"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function mainBuildServiceApplication ()
{
  local logSectionDesc="Building Service Container Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$NDR_SERVICE_HOME_LOC"
  local dockerImageBaseName="$NDR_SERVICE_IMAGE_NAME"
  local dockerImageVersion="$NDR_SERVICE_IMAGE_VERSION"
  local dockerContainerName="$NDR_SERVICE_CONTAINER_NAME"
  local dockerContainerPort="$NDR_SERVICE_CONTAINER_PORT"

  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  buildDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker application [$dockerImageBaseName:$dockerImageVersion $dockerContainerName $dockerContainerPort]."
    ndr_logSecEnd "$logSectionDesc"
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function [build_mode_options]
function mainBuildUIApplication () 
{
  local logSectionDesc="Building UI Container Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$NDR_UI_HOME_LOC"
  local dockerImageBaseName="$NDR_UI_IMAGE_NAME"
  local dockerImageVersion="$NDR_UI_IMAGE_VERSION"
  local dockerContainerName="$NDR_UI_CONTAINER_NAME"
  local dockerContainerPort="$NDR_UI_CONTAINER_PORT"

  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  buildDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"  "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker application [$dockerImageBaseName:$dockerImageVersion $dockerContainerName $dockerContainerPort]."
    ndr_logSecEnd "$logSectionDesc"
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> [build_mode_options]
function buildDockerApplicationImage ()
{
  local logSectionDesc="Building Docker Application Image"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  
  local dockerAppManageOptions="${4:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"

  # Validate argument count
  if [[ $# -lt 3 ]]; then
    ndr_logError "Usage: buildDockerApplication <folder> <image_name> <image_version> [build_mode_options]"
    return 1
  fi

  # move from the install directory to the container/module directory
  cd "$gSCRIPT_HOME_DIR/../$containerFolder" || { ndr_logError "Failed to cd into $containerFolder"; return 1; }
  
  # check if the Dockerfile exists
  if [[ ! -f Dockerfile ]]; then
    ndr_logError "Dockerfile not found in $containerFolder directory."
    return 1
  fi

  dockerAppManageOptions=$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT ))
  # remove any existing Docker image with the same name
  ndr_cleanupDockerApplication "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerAppManageOptions"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing Docker application image."
    return 1
  fi

  # construct env file
  ndr_BuildModuleEnvFile "$containerFolder"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build module env file for [$containerFolder]."
    return 1
  fi

  # build the Docker image
  ndr_logInfo "Executing Docker build command for image [$dockerImageName]."
  docker build -t "$dockerImageName" .
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to build Docker image for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker build command succeeded for image [$dockerImageName]."

  # check if the newly built Docker image exists
  ndr_verifyDockerImageExists "$dockerImageName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image verification failed."
    return 1
  fi

  # clean up image env file
  ndr_cleanupModuleEnvFile "$containerFolder"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to clean up module env file for [$containerFolder]."
    #return 1
  fi

  ndr_logInfo "Docker image [$dockerImageName] successfully built."

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <folder> <image_name> <image_version> <container_name> <container_port> [build_mode_options]
function buildDockerApplication ()
{
  local logSectionDesc="Building Docker Application"
  ndr_logSecStart "$logSectionDesc"

  local containerFolder="$1"
  local dockerImageBaseName="$2"
  local dockerImageVersion="$3"
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  local dockerContainerName=$4
  local dockerContainerPort=$5
  #local dockerContainerURL="http://localhost:$dockerContainerPort"
  
  local dockerAppManageOptions="${6:-$NDR_DOCKER_APP_MANAGE_OPTIONS_DEFAULT}"
  local buildAndStartContainer=false
  
  ndr_logInfo "Docker application build mode options [$dockerAppManageOptions]"

  # Validate argument count (only 5 mandatory)
  if [[ $# -lt 5 ]]; then
    ndr_logError "Usage: buildDockerApplication <folder> <image_name> <image_version> <container_name> <container_port> [build_mode_options]"
    return 1
  fi

  while true; do

    if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE )); then
      buildDockerApplicationImage  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to build Docker image for [$dockerImageName]."
        return 1
      fi
      ndr_logInfo "Docker image [$dockerImageName] successfully built."
    else
      ndr_logInfo "Skipping Docker image creation for [$dockerImageName]."
    fi

    # there's only 2 options where container building is performed:
    # 1. explicit build with flag NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER
    # 2. optional build with flag NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL
    # check for explicit container build option that may be present.
    if (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER )); then
      buildAndStartContainer=true
    elif (( dockerAppManageOptions & NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER_OPTIONAL )); then
      # if options suggest container is optional, we should prompt.
      ndr_logInfo "Would you like to optionally build and start the container [$dockerContainerName]"
      read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
      echo    # Move to a new line
      if [[ $REPLY =~ ^[Nn]$ ]]; then
        ndr_logInfo "Skipping docker container build, returning to main menu."
        break
      fi
      buildAndStartContainer=true
    else
      ndr_logInfo "Container build not requested for [$dockerContainerName]."
      buildAndStartContainer=false
    fi

    if [[ "$buildAndStartContainer" == true ]]; then
      ndr_buildDockerApplicationContainer  "$containerFolder" "$dockerImageBaseName" "$dockerImageVersion" "$dockerContainerName" "$dockerContainerPort"
      return_code=$?
      if [ $return_code != 0 ]; then
        ndr_logError "Failed to build Docker container for [$dockerContainerName]."
        return 1
      fi
      ndr_logInfo "Docker container [$dockerContainerName] successfully built."
    fi

    break

  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# usage: function <image_name> <image_version> <repo_name> <repo_type>
function uploadDockerImage ()
{
  local logSectionDesc="Uploading Docker Image"
  ndr_logSecStart "$logSectionDesc"

  local dockerImageBaseName="$1"
  local dockerImageVersion="$2"
  local dockerImageName="${dockerImageBaseName}:${dockerImageVersion}"
  local imageRepoName="$3"
  local imageRepoType="$4"

  # Validate argument count
  if [[ $# -lt 4 ]]; then
    ndr_logError "Usage: uploadDockerImage <image_name> <image_version> <repo_name> <repo_type>"
    return 1
  fi

  if [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_GIT ]]; then
    
    ndr_logWarn "Mode not supported"
    return 1

    dockerImageDir="$gSCRIPT_HOME_DIR/Images"
    dockerImageTarFile="$dockerImageDir/${dockerImageBaseName}.tar"
    dockerImageZipFile="${dockerImageTarFile}.zst"
    
    # clean up any leftover local image files
    ndr_logInfo "Cleaning up any leftover image files [$gSCRIPT_HOME_DIR/${dockerImageBaseName}.*]"
    if [[ -f "$dockerImageTarFile" ]]; then
      rm -rf "$dockerImageTarFile"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to remove preexisting local Docker image file [$dockerImageTarFile]."
      fi
      ndr_logInfo "Preexisting Docker image file removed [$dockerImageTarFile]."
    fi
    if [[ -f "$dockerImageZipFile" ]]; then
      rm -rf "$dockerImageZipFile"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to remove preexisting local Docker image file [$dockerImageZipFile]."
      fi
      ndr_logInfo "Preexisting Docker image file removed [$dockerImageZipFile]."
    fi
    
    if [[ ! -d "$dockerImageDir" ]]; then
      mkdir -p "$dockerImageDir"
    fi

    # save to tar file
    ndr_logInfo "Extracting and saving Docker tar file [$dockerImageTarFile] for image [$dockerImageName], please wait..."
    docker save -o "$dockerImageTarFile" "$dockerImageName"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to save Docker tar file [$dockerImageTarFile] for image [$dockerImageName]."
      return 1
    fi
    tarFileSize=$(du -h "$$dockerImageTarFile" | cut -f1)
    tarFileSize="${tarFileSize:-unknown}"
    ndr_logInfo "Docker tar file saved [$dockerImageTarFile], size [$tarFileSize] for image [$dockerImageName]"

    # verify tar file
    tar -tvf "$dockerImageTarFile"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to verify Docker image tar file [$dockerImageTarFile]."
      return 1
    fi
    ndr_logInfo "Docker image tar file verified [$dockerImageTarFile]"

    # compress tar file
    ndr_logInfo "🗜️ Compressing Docker tar file [$dockerImageTarFile], please wait..."
    zstd -v -f --rm -19 "$dockerImageTarFile" -o "$dockerImageZipFile"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "🗜️ Failed to compress Docker image tar file [$dockerImageTarFile]."
      return 1
    fi
    zipFileSize=$(du -h "$dockerImageZipFile" | cut -f1)
    zipFileSize="${zipFileSize:-unknown}"
    ndr_logInfo "🗜️ Docker image tar file [$dockerImageTarFile] compressed from [$tarFileSize] to [$zipFileSize]."

    # git auth
    authenticateGitSSHKey
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to authenticate with SSH key."
      return 1
    fi

    # check local git repo
    gitCurrentBranch=$(git branch --show-current)
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to check local git branch."
      return 1
    fi

    if [[ "$gitCurrentBranch" -ne "$NDR_GIT_REPO_BRANCH_OR_TAG" ]]; then
      ndr_logError "Local git branch [$gitCurrentBranch] does not match production target [$NDR_GIT_REPO_BRANCH_OR_TAG]."
      return 1
    fi
  
    # git add
    ndr_logInfo "Adding docker image file [$dockerImageZipFile] to git repo branch [$gitCurrentBranch], please wait..."
    git add "$dockerImageZipFile"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to git add docker image file [$dockerImageZipFile] to repo branch [$gitCurrentBranch]."
      return 1
    fi
    ndr_logInfo "Added docker image file [$dockerImageZipFile] to git repo branch [$gitCurrentBranch]."

    # git commit
    gitComment="Devops commit of [$dockerImageZipFile] for image [$dockerImageName] on [$(date '+%Y-%m-%d %H:%M:%S')]"
    git commit "$dockerImageZipFile" -m "$gitComment"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to git commit docker image file [$dockerImageZipFile] to repo branch [$gitCurrentBranch]."
      return 1
    fi
    ndr_logInfo "Committed docker image file [$dockerImageZipFile] to git repo branch [$gitCurrentBranch]."

    # git push
    git push origin "$gitCurrentBranch"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to git push docker image file [$dockerImageZipFile] to repo branch [$gitCurrentBranch]."
      
      # if push fails, we should immediately uncommit and unstage the file.
      # Unstage the file from the last commit
      ndr_logInfo "Uncommitting docker image zip file [$dockerImageZipFile] from git repo branch [$gitCurrentBranch]."
      git reset HEAD^ -- "$dockerImageZipFile"
      # Remove from staging
      ndr_logInfo "Unstaging docker image zip file [$dockerImageZipFile] from git repo branch [$gitCurrentBranch]."
      git restore --staged "$dockerImageZipFile"

      return 1
    fi
    ndr_logInfo "Successfully pushed docker image  file [$dockerImageZipFile] to git repo branch [$gitCurrentBranch]."

    # delete tar/zip files
    ndr_logInfo "Cleaning up image files [$gSCRIPT_HOME_DIR/${dockerImageBaseName}.*]"
    if [[ -f "$dockerImageTarFile" ]]; then
      rm -rf "$dockerImageTarFile"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to remove local Docker image file [$dockerImageTarFile]."
      fi
      ndr_logInfo "Docker image file removed [$dockerImageTarFile]."
    fi
    if [[ -f "$dockerImageZipFile" ]]; then
      rm -rf "$dockerImageZipFile"
      return_code=$?
      if [ $return_code -ne 0 ]; then
        ndr_logError "Failed to remove local Docker image file [$dockerImageZipFile]."
      fi
      ndr_logInfo "Docker image file removed [$dockerImageZipFile]."
    fi

  elif  [[ $imageRepoType -eq $NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB ]]; then
  
    docker login -u "$NDR_DOCKER_REPO_ACCOUNT" -p "$NDR_DOCKER_REPO_ACCESS_TOKEN"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker login failed for [$NDR_DOCKER_REPO_ACCOUNT]."
      return 1
    fi
    ndr_logInfo "Docker login success for account [$NDR_DOCKER_REPO_ACCOUNT]"

    # form remote repo tag in a way that we can decode it on the download end and retag in the local format.
    # repo tag format: <account>/<repo_name>:<{image_base_name}_{image_version}>
    local repoImageTag="$NDR_DOCKER_REPO_ACCOUNT/$imageRepoName:${dockerImageBaseName}_${dockerImageVersion}"
    
    # apply additional remote tag reference to local image
    docker tag "$dockerImageName" "$repoImageTag"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker tag failed for [$dockerImageName -> $repoImageTag]."
      return 1
    fi
    ndr_logInfo "Docker tag success for [$dockerImageName -> $repoImageTag]."
    
    docker push "$repoImageTag"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker push failed for [$repoImageTag]."

      # cleanup remote repo tag reference to local image.
      docker rmi "$repoImageTag"
      if [ $return_code -ne 0 ]; then
        ndr_logError "Docker rmi failed for [$repoImageTag]."
      fi
  
      return 1
    fi
    ndr_logInfo "Docker push success for local [$dockerImageName] to remote [$repoImageTag]."

    # cleanup remote repo tag reference to local image.
    ndr_logInfo "Removing remote repo tag [$repoImageTag] from local image [$dockerImageName]"
    docker rmi "$repoImageTag"
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker rmi failed for [$repoImageTag]."
      #return 1
    fi

  else
    ndr_logError "Error, unknown Docker image repo mode."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainUploadDockerImages ()
{
  local logSectionDesc="Uploading Docker Images"
  ndr_logSecStart "$logSectionDesc"

  local dockerAppManageOptions="${1:-$NDR_DOCKER_APP_MANAGE_OPTIONS_NONE}"

  local uploadImage=true
  
  # check and upload service
  local dockerImageBaseName=$NDR_SERVICE_IMAGE_NAME
  local dockerImageVersion=$NDR_SERVICE_IMAGE_VERSION
  local dockerImageName="$dockerImageBaseName:$dockerImageVersion"  

  ndr_verifyDockerImageExists "$dockerImageName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image verification failed for [$dockerImageName]."
    return 1
  fi

  ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT"
  return_code=$?
  if [[ $return_code == 1 ]]; then
    ndr_logInfo "Uploading Docker image [$dockerImageName]. Warning, this will overwrite any existing image in the repository with the same name. This action cannot be undone!"
    read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Skipping Docker image upload for [$dockerImageName]."
      uploadImage=false
    fi
  fi

  if [[ "$uploadImage" == true ]]; then
    uploadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$NDR_DOCKER_SERVICE_REPO_NAME" "$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker image upload failed for [$dockerImageName]."
      return 1
    fi
    ndr_logInfo "Docker image upload succeeded for [$dockerImageName]."
  fi

  # check and upload ui
  uploadImage=true
  dockerImageBaseName=$NDR_UI_IMAGE_NAME
  dockerImageVersion=$NDR_UI_IMAGE_VERSION
  dockerImageName="$dockerImageBaseName:$dockerImageVersion"
  
  ndr_verifyDockerImageExists "$dockerImageName"
  return_code=$?
  #2-error, 1-not found, 0-found
  if [ $return_code -ne 0 ]; then
    ndr_logError "Docker image verification failed for [$dockerImageName]."
    return 1
  fi

  ndr_checkFlag "$dockerAppManageOptions" "$NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT"
  return_code=$?
  if [[ $return_code == 1 ]]; then
    ndr_logInfo "Uploading Docker image [$dockerImageName]. Warning, this will overwrite any existing image in the repository with the same name. This action cannot be undone!"
    read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
    echo    # Move to a new line
    if [[ $REPLY =~ ^[Nn]$ ]]; then
      ndr_logInfo "Skipping Docker image upload for [$dockerImageName]."
      uploadImage=false
    fi
  fi

  if [[ "$uploadImage" == true ]]; then
    uploadDockerImage "$dockerImageBaseName" "$dockerImageVersion" "$NDR_DOCKER_SERVICE_REPO_NAME" "$NDR_DOCKER_IMAGE_REPO_TYPE_DOCKERHUB"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Docker image upload failed for [$dockerImageName]."
      return 1
    fi
    ndr_logInfo "Docker image upload succeeded for [$dockerImageName]."
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function mainBuildAndUpload ()
{
  local logSectionDesc="Build and Upload Images"
  ndr_logSecStart "$logSectionDesc"

  DOCKER_CONTAINER_SKIP_START=true

  ndr_checkAndInstallPrerequisites
  mainBuildServiceApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE"
  mainBuildUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE"
  mainUploadDockerImages "$NDR_DOCKER_APP_MANAGE_OPTIONS_UPLOAD_NO_PROMPT"
  ndr_mainCleanupUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
  ndr_mainCleanupServiceApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function testFunction ()
{
  local logSectionDesc="Unit test function"
  ndr_logSecStart "$logSectionDesc"

  finalScript="final.sh"
  cat ndrCommon.sh ndrRegistry.sh ndrDocker.sh ndrInstall.sh > $finalScript
  shc -f "$finalScript" -o "${finalScript%.sh}.bin"
  chmod +x "${finalScript%.sh}.bin"
  
  return 0

  authenticateGitSSHKey
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to authenticate ssh."
    return 1
  fi

  dockerImageTarFile="$gSCRIPT_HOME_DIR/${NDR_UI_IMAGE_NAME}.tar"
  dockerImageName="${NDR_UI_IMAGE_NAME}:${NDR_UI_IMAGE_VERSION}"

  # test automated sequence of create image, save image, remove image, restore image, create container.
  mainBuildUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to build Docker application image [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application image successfully built for image [$dockerImageName]"

  if [[ -f "$dockerImageTarFile" ]]; then
    rm -rf "$dockerImageTarFile"
    return_code=$?
    if [ $return_code -ne 0 ]; then
      ndr_logError "Failed to remove preexisting local Docker tar file [$dockerImageTarFile]."
    fi
    ndr_logInfo "Preexisting Docker tar file removed [$dockerImageTarFile]."
  fi

  ndr_logInfo "Extracting and saving Docker tar file [$dockerImageTarFile] for image [$dockerImageName], please wait..."
  docker save -o "$dockerImageTarFile" "$dockerImageName"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to save Docker tar file [$dockerImageTarFile] for image [$dockerImageName]."
    return 1
  fi
  tarFileSize=$(du -h "$dockerImageTarFile" | cut -f1)
  tarFileSize="${tarFileSize:-unknown}"
  ndr_logInfo "Docker tar file saved [$dockerImageTarFile], size [$tarFileSize] for image [$dockerImageName]"

  tar -tvf "$dockerImageTarFile"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to verify Docker image tar file [$dockerImageTarFile]."
    return 1
  fi
  ndr_logInfo "Docker image tar file verified [$dockerImageTarFile]"

  # compress tar file
  ndr_logInfo "🗜️ Compressing Docker tar file [$dockerImageTarFile], please wait..."
  dockerImageZipFile="${dockerImageTarFile}.zst"
  zstd -f -v -19 "$dockerImageTarFile" -o "$dockerImageZipFile"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "🗜️ Failed to compress Docker image tar file [$dockerImageTarFile]."
    return 1
  fi
  zipFileSize=$(du -h "$dockerImageZipFile" | cut -f1)
  zipFileSize="${zipFileSize:-unknown}"
  ndr_logInfo "🗜️ Docker image tar file [$dockerImageTarFile] compressed from [$tarFileSize] to [$zipFileSize]."

  return 0

  ndr_mainCleanupServiceApplication "$(( NDR_DOCKER_APP_MANAGE_OPTIONS_IMAGE | NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_NO_PROMPT ))"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to clean up Docker application for [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application cleaned up [$dockerImageName]"

  ndr_logInfo "Loading Docker tar file [$dockerImageTarFile]"
  docker load -i "$dockerImageTarFile"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to load Docker image tar file [$dockerImageTarFile]."
    return 1
  fi
  ndr_logInfo "Docker image tar file loaded [$dockerImageTarFile]"

  mainBuildUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_CONTAINER"
  return_code=$?
  if [ $return_code -ne 0 ]; then
    ndr_logError "Failed to build Docker application container for image [$dockerImageName]."
    return 1
  fi
  ndr_logInfo "Docker application container successfully built for image [$dockerImageName]"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# command line parsing
# ====================================================
function mainParseCommandLineArgs ()
{
  local logSectionDesc="Parsing command line arguments"
  ndr_logSecStart "$logSectionDesc"

  ndr_parseCommandLineArgs "$@"

  # Parse arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --passphrase|-p)
        if [[ -n "$2" && "$2" != --* ]]; then
          gSSH_Passphrase="$2"
          #ndr_logInfo "Passphrase set to [$gSSH_Passphrase]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --branch|-b)
        if [[ -n "$2" && "$2" != --* ]]; then
          NDR_GIT_REPO_BRANCH_OR_TAG="$2"
          ndr_logInfo "GIT branch set to [$NDR_GIT_REPO_BRANCH_OR_TAG]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --dest|-d)
        if [[ -n "$2" && "$2" != --* ]]; then
          NDR_GIT_LOCAL_REPO_DEST_DIR="$2"
          ndr_logInfo "GIT destination dir set to [$NDR_GIT_LOCAL_REPO_DEST_DIR]"
          shift 2
        else
          ndr_logError "$1 requires a value."
          return 1
        fi
        ;;
      --help|-h)
        ndr_logWarn "Usage: $0 --express <$EXPRESS_MENU_OPTIONS> --passphrase <password> [--branch <branch_or_tag>] [--dest <dest folder>] [--debug]."
        return 1
        ;;
      *)
        #ndr_logError "Unknown option: $1"
        shift
        ;;
    esac
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# ====================================================
# Express install mode
# ====================================================

function mainExpressInstallMode ()
{
  local logSectionDesc="Executing express install mode"
  ndr_logSecStart "$logSectionDesc"

  # Check if express mode is enabled and an option is provided
  if [ "$gExpressMode" != 1 ]; then
    ndr_logError "Express mode is not enabled. Use --express to enable."
    return 1
  fi
  if [ -z "$gExpressOption" ]; then
    ndr_logError "No express option provided. Use --express <option>."
    return 1
  fi

  # Set default values for variables
  gExpressMode=1
  
  case "$gExpressOption" in
    installprerequisites)
      ndr_checkAndInstallPrerequisites
      ;;
    generatesshkey)
      mainGenerateGitHubPublicKey
      ;;
    pullrepo|pull)
      mainPullGitRepo
      ;;
    buildserviceapp|buildservice|service)
      mainBuildServiceApplication
      ;;
    builduiapp|buildui|ui)
      mainBuildUIApplication
      ;;
    uploadimages|upload)
      mainUploadDockerImages
      ;;
    fullbuild|full)
      mainBuildAndUpload
      ;;
    cleanupserviceapp|cleanupservice)
      ndr_mainCleanupServiceApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
      ;;
    cleanupuiapp|cleanupui)
      ndr_mainCleanupUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
      ;;
    cleanupall)
      ndr_mainCleanupServiceApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
      ndr_mainCleanupUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
      ;;
    cleanupdockerengine|cleanupdocker|removedocker)
      ndr_removeDockerPackages
      ;;
    scrubdocker)
      ndr_scrubAllDockerResources
      ;;
    test)
      testFunction
      ;;
    *)
      # print usage and exit
      ndr_logWarn "Invalid option. Please use one of the following options: $EXPRESS_MENU_OPTIONS"
      return 1
      ;;
  esac
  
  ndr_logSecEnd "$logSectionDesc"
  
  return 0
}

# ====================================================
# Interactive menu mode
# ====================================================
function mainInteractiveMenuMode ()
{
  local logSectionDesc="Executing interactive menu mode"
  ndr_logSecStart "$logSectionDesc"
  
  MENU_ITEM_1="Check and install software package prerequisites"
  MENU_ITEM_2="Generate new Github SSH key"
  MENU_ITEM_3="Pull/Update latest $gCOMPANY_NAME GIT repository"
  MENU_ITEM_4="Build/Start $gCOMPANY_NAME back end Service Docker application"
  MENU_ITEM_5="Build/Start $gCOMPANY_NAME front end UI Docker application"
  MENU_ITEM_6="Upload Prebuilt Docker images to $gCOMPANY_NAME Docker Image Repository"
  MENU_ITEM_7="Stop/Remove $gCOMPANY_NAME back end Service Docker application"
  MENU_ITEM_8="Stop/Remove $gCOMPANY_NAME front end UI Docker application"
  MENU_ITEM_9="Full Build - Perform all steps"
  MENU_ITEM_10="Stop/Remove Docker Engine."

  while true; do
    echo "========================================================"
    echo "$gCOMPANY_NAME DevOps Management Script"
    echo "========================================================"
    echo "Please select an option:"
    echo ""
    echo "1. $MENU_ITEM_1"
    echo "2. $MENU_ITEM_2"
    echo "3. $MENU_ITEM_3"
    echo "4. $MENU_ITEM_4"
    echo "5. $MENU_ITEM_5"
    echo "6. $MENU_ITEM_6"
    echo "7. $MENU_ITEM_7"
    echo "8. $MENU_ITEM_8"
    echo "9. $MENU_ITEM_9"
    echo "10. $MENU_ITEM_10"
    echo "q. Exit"
    echo ""
    read -p "Enter your choice [1-n]: " choice

    case $choice in
      1)
        echo "$MENU_ITEM_1. This will check for NPM/NPX, GIT, Docker, etc and prompt for interactive install."
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_checkAndInstallPrerequisites
        fi
        ;;
      2)
        echo "$MENU_ITEM_2"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainGenerateGitHubPublicKey
        fi
        ;;
      3)
        echo "$MENU_ITEM_3"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainPullGitRepo
        fi
        ;;
      4)
        echo "$MENU_ITEM_4"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainBuildServiceApplication
        fi
        ;;
      5)
        echo "$MENU_ITEM_5"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainBuildUIApplication
        fi
        ;;
      6)
        echo "$MENU_ITEM_6"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainUploadDockerImages
        fi
        ;;
      7)
        echo "$MENU_ITEM_7"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_mainCleanupServiceApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
        fi
        ;;
      8)
        echo "$MENU_ITEM_8"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_mainCleanupUIApplication "$NDR_DOCKER_APP_MANAGE_OPTIONS_REMOVE_ALL"
        fi
        ;;
      9)
        echo "$MENU_ITEM_9"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          mainBuildAndUpload
        fi
        ;;
      10)
        echo "$MENU_ITEM_10"
        read -p "Are you sure you want to proceed? [Y|n] " -n 1 -r
        echo    # Move to a new line
        if [[ $REPLY =~ ^[Nn]$ ]]; then
          ndr_logInfo "Operation cancelled, returning to main menu."
        else
          ndr_removeDockerPackages
        fi
        ;;
      Q|q)
        echo "Exiting..."
        break
        ;;
      t)
        #test section
        # unit test - remove for production
        echo "Running unit tests..."
        testFunction
        break
        ;;
      *)
        echo "Invalid option. Please try again."
        ;;
    esac

    echo ""
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function main ()
{
  echo "🕓 Started at $(date)"

  mainParseCommandLineArgs "$@"
  return_code=$?
  if [ $return_code != 0 ]; then
    return 1
  fi

  ndr_osTypeCheck

  if [[ "$gExpressMode" -eq 1 && -n "$gExpressOption" ]]; then
    ndr_logInfo "Express install option selected. Skipping all prompts and running with supplied values."
    mainExpressInstallMode
    echo "🕓 Finished at $(date)"
    return 0
  fi

  mainInteractiveMenuMode

  echo "🕓 Finished at $(date)"

  return 0
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
  exit 0
fi

# Issues
# add menu option to add ndr schema file to git repo
# git repo pull doesnt really work all that well when in a merge situation. maybe only support fresh pulls? but when would that be useful?

# docker images can be extracted from docker's internal storage system for archival
# docker save -o my-image.tar my-image:latest
# Then you can:
# Inspect the .tar with tar -tvf my-image.tar
# Move it to another system
# Restore it with docker load -i my-image.tar
# this could be useful for offline install or preserving image files in git repo?
# TODO - test automated sequence of create image, save image, remove image, restore image, create container.

#Pushing images
#You can push a new image to this repository using the CLI:
#docker tag local-image:tagname new-repo:tagname
#docker push new-repo:tagname
#Make sure to replace tagname with your desired image repository tag.
