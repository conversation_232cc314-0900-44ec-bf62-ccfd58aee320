import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

export class OSUpdateExecutor extends BaseStepExecutor {
	constructor() {
		super("Apply OS updates");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Starting OS updates for step: ${step.name}`);

		try {
			this.validate(step);
			await this.sleep(2500);

			return this.createSuccessResult("OS updates applied successfully");
		} catch (error: any) {
			return this.createErrorResult(`OS update failed: ${error.message}`, error.message);
		}
	}

	validate(step: any): boolean {
		super.validate(step);
		if (step.operation_type !== "Apply OS updates") {
			throw new Error(`Invalid operation type for OS update: ${step.operation_type}`);
		}
		return true;
	}
}
