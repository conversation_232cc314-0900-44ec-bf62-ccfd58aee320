import { Request, Response, NextFunction } from "express";
import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";
import { supabase } from "../services/supabaseService";

/**
 * Middleware to validate license token
 *
 * Checks for a valid license token in:
 * 1. Authorization header (Bearer token)
 * 2. license cookie
 *
 */
export const licenseCheck = async (req: Request, res: Response, next: NextFunction) => {
	try {
		let token: string | undefined;

		const authHeader = req.headers.authorization;
		if (authHeader && authHeader.startsWith("Bearer ")) {
			token = authHeader.substring(7);
		}

		if (!token && req.cookies && req.cookies.license) {
			token = req.cookies.license;
		}

		if (!token) {
			return res.status(401).json({ error: "No license token provided" });
		}

		const publicKeyPath = path.join(__dirname, "../../keys/public.key");

		if (!fs.existsSync(publicKeyPath)) {
			console.error("License public key not found");
			return res.status(500).json({ error: "License verification error" });
		}

		const publicKey = fs.readFileSync(publicKeyPath, "utf8");

		const decoded = jwt.verify(token, publicKey, { algorithms: ["RS256"] }) as any;

		if (decoded.exp < Math.floor(Date.now() / 1000)) {
			return res.status(401).json({ error: "License expired" });
		}

		const { data: licenseData, error: licenseError } = await supabase
			.from("licenses")
			.select("status, last_verified")
			.eq("jwt", token)
			.single();

		if (licenseError || !licenseData) {
			console.error("License not found in database:", licenseError);
			return res.status(401).json({ error: "Invalid license" });
		}

		if (licenseData.status !== "active") {
			return res.status(401).json({ error: "License is not active" });
		}

		await supabase
			.from("licenses")
			.update({ last_verified: new Date().toISOString() })
			.eq("jwt", token);

		req.license = {
			customerId: decoded.sub,
			expiresAt: new Date(decoded.exp * 1000),
			features: decoded.features || [],
		};

		next();
	} catch (error) {
		console.error("License check error:", error);

		if (error instanceof jwt.TokenExpiredError) {
			return res.status(401).json({ error: "License expired" });
		}

		if (error instanceof jwt.JsonWebTokenError) {
			return res.status(401).json({ error: "Invalid license token" });
		}

		return res.status(500).json({ error: "License verification error" });
	}
};

declare global {
	namespace Express {
		interface Request {
			license?: {
				customerId: string;
				expiresAt: Date;
				features: string[];
			};
		}
	}
}

export default licenseCheck;
