import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";

interface Group {
	id: string;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
	members: {
		id: string;
		email: string;
		role: string;
		status: string;
	}[];
}

export const useGroups = () => {
	const queryClient = useQueryClient();

	const {
		data: groups,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["groups"],
		queryFn: async () => {
			const { data: groups, error: groupsError } = await supabase.from("internal_groups").select(`
					*,
					members:group_members(
						user:user_profiles(
							id,
							email,
							role,
							status
						)
					)
				`);

			if (groupsError) throw groupsError;

			const transformedData = groups.map((group: any) => ({
				...group,
				members: group.members?.map((m: any) => m.user) || [],
			}));

			return transformedData as Group[];
		},
	});

	const createGroup = useMutation({
		mutationFn: async (group: Omit<Group, "id" | "created_at" | "updated_at">) => {
			const { error } = await supabase.from("groups").insert(group);
			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["groups"] });
		},
	});

	const updateGroup = useMutation({
		mutationFn: async ({ id, ...group }: Partial<Group> & { id: string }) => {
			const { error } = await supabase.from("groups").update(group).eq("id", id);
			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["groups"] });
		},
	});

	const deleteGroup = useMutation({
		mutationFn: async (id: string) => {
			const { error } = await supabase.from("groups").delete().eq("id", id);
			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["groups"] });
		},
	});

	return {
		groups,
		isLoading,
		error,
		createGroup,
		updateGroup,
		deleteGroup,
	};
};
