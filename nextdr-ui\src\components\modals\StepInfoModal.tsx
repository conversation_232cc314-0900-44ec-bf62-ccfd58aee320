import React from "react";
import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
	DialogDescription,
} from "@/components/ui/dialog";
import { useModalStore } from "@/lib/store/useStore";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
	Clock,
	AlertCircle,
	CheckCircle2,
	XCircle,
	Info,
	Server,
	Cloud,
	Database,
	Shield,
	Bell,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface StepInfoModalData {
	content: string;
	step: {
		title?: string;
		description?: string;
		content?: React.ReactNode;
		status?: string;
		operation_type?: string;
		details?: Record<string, string | number | boolean>;
		warnings?: string[];
		configuration?: Record<string, any>;
	};
}

const StepInfoModal = () => {
	const { isOpen, modalType, modalData, onClose } = useModalStore();
	const showModal = isOpen && modalType === "stepInfo";
	const data = modalData as StepInfoModalData | undefined;

	const getOperationIcon = (type?: string) => {
		switch (type) {
			case "Restore virtual machine":
				return <Server className="h-5 w-5 text-blue-500" />;
			case "IaC":
				return <Cloud className="h-5 w-5 text-purple-500" />;
			case "Database restore":
				return <Database className="h-5 w-5 text-green-500" />;
			case "Virus check":
				return <Shield className="h-5 w-5 text-red-500" />;
			case "Notification":
				return <Bell className="h-5 w-5 text-amber-500" />;
			default:
				return <Info className="h-5 w-5 text-gray-500" />;
		}
	};

	const renderVMRestoreDetails = (config: Record<string, any>) => {
		return (
			<Card>
				<CardContent className="p-4">
					<div className="flex items-center gap-2 mb-4">
						<Server className="h-5 w-5 text-blue-500" />
						<h3 className="text-sm font-medium">VM Restore Configuration</h3>
					</div>
					<div className="space-y-3">
						{config.vm_selections?.length > 0 && (
							<div>
								<p className="text-sm text-muted-foreground mb-2">Selected VMs</p>
								<div className="space-y-1">
									{config.vm_selections.map((vm: any, index: number) => (
										<div key={index} className="text-sm bg-secondary/50 p-2 rounded">
											{vm.vm_id}
											{vm.rename_to && (
												<p className="text-xs text-muted-foreground">Rename to: {vm.rename_to}</p>
											)}
										</div>
									))}
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		);
	};

	const renderIaCDetails = (config: Record<string, any>) => {
		return (
			<Card>
				<CardContent className="p-4">
					<div className="flex items-center gap-2 mb-4">
						<Cloud className="h-5 w-5 text-purple-500" />
						<h3 className="text-sm font-medium">Infrastructure Configuration</h3>
					</div>
					<div className="space-y-3">
						<div>
							<p className="text-sm text-muted-foreground">Project Naming</p>
							<p className="text-sm font-medium capitalize">
								{config.project_naming || "Not specified"}
							</p>
						</div>
						{config.custom_project_name && (
							<div>
								<p className="text-sm text-muted-foreground">Custom Project Name</p>
								<p className="text-sm font-medium">{config.custom_project_name}</p>
							</div>
						)}
						<div>
							<p className="text-sm text-muted-foreground">Snapshot Point</p>
							<p className="text-sm font-medium">{config.snapshot_point_id || "Not specified"}</p>
						</div>
						{config.selected_components?.length > 0 && (
							<div>
								<p className="text-sm text-muted-foreground mb-2">Components to Restore</p>
								<div className="flex flex-wrap gap-2">
									{config.selected_components.map((component: string, index: number) => (
										<Badge key={index} variant="secondary" className="capitalize">
											{component}
										</Badge>
									))}
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		);
	};

	const renderDefaultDetails = (details: Record<string, string | number | boolean>) => {
		return (
			<Card>
				<CardContent className="p-4">
					<h3 className="text-sm font-medium mb-3">Step Details</h3>
					<div className="space-y-2 text-sm">
						{Object.entries(details).map(([key, value]) => (
							<div key={key} className="flex justify-between">
								<span className="text-muted-foreground capitalize">{key.replace(/_/g, " ")}:</span>
								<span className="font-medium">{String(value)}</span>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	};

	return (
		<Dialog open={showModal} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						{getOperationIcon(data?.step?.operation_type)}
						{data?.step?.title || "Step Information"}
					</DialogTitle>
					{data?.step?.description && (
						<DialogDescription>{data.step.description}</DialogDescription>
					)}
				</DialogHeader>

				<ScrollArea className="h-[calc(80vh-200px)]">
					<div className="space-y-4 mt-4 pr-4">
						{data?.step?.configuration ? (
							<>
								{data.step.operation_type === "Restore virtual machine" &&
									renderVMRestoreDetails(data.step.configuration)}
								{data.step.operation_type === "IaC" && renderIaCDetails(data.step.configuration)}
								{!["Restore virtual machine", "IaC"].includes(data.step.operation_type || "") &&
									data.step.details &&
									renderDefaultDetails(data.step.details)}
							</>
						) : data?.content ? (
							<Card>
								<CardContent className="p-4">
									<div className="prose prose-sm max-w-none">{data.content}</div>
								</CardContent>
							</Card>
						) : (
							<></>
						)}

						{data?.step?.warnings && data.step.warnings.length > 0 && (
							<Card className="border-amber-500/50">
								<CardContent className="p-4">
									<div className="flex items-start gap-2">
										<AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
										<div>
											<h3 className="text-sm font-medium text-amber-500 mb-2">Warnings</h3>
											<ul className="space-y-1 text-sm">
												{data.step.warnings.map((warning, index) => (
													<li key={index} className="text-amber-500">
														{warning}
													</li>
												))}
											</ul>
										</div>
									</div>
								</CardContent>
							</Card>
						)}
					</div>
				</ScrollArea>

				<div className="flex justify-end mt-6">
					<Button onClick={onClose}>Close</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default StepInfoModal;
