import { Router } from "express";
import { getGcpInstance } from "../controllers/gcpController";
import {
	createBackup,
	getSnapshots,
	restoreFromSnapshot,
	listVMs,
	getNetworkInterfaces,
	getIAMAccountsRoles,
	getProjectResources,
} from "../controllers/backupController";
import {
	executeRecoveryPlan,
	executeNextSteps,
	updateRecoveryStepStatus,
} from "../controllers/recoveryController";
import { getStepByToken, handleApprovalDecision } from "../controllers/approvalController";
import { resumeRecoveryPlanExecution } from "../services/recoveryProgressService";
import {
	startRecoveryPlanExecution,
	resumeRecoveryPlanExecution as resumeExecution,
	getExecutionStatus,
	getExecutionProgress,
	cancelExecution,
} from "../controllers/executionController";
import { sendEmail, sendApprovalEmail, sendVerificationEmail } from "../services/emailService";
import { supabase } from "../services/supabaseService";
import {
	getIntegrationConfig,
	updateIntegrationConfig,
	triggerSync,
	getSyncStats,
} from "../controllers/integrationController";
import { isAdmin } from "../middleware/rbac";

const router = Router();

router.get("/get_gcp_instance", getGcpInstance);
router.post("/gcp/backup", createBackup);
router.get("/gcp/snapshots/:instanceName", getSnapshots);
router.post("/gcp/restore", restoreFromSnapshot);
router.get("/gcp/vms/", listVMs);
router.get("/gcp/project/get_network_interfaces", getNetworkInterfaces);
router.get("/gcp/project/get_iam_accounts_roles", getIAMAccountsRoles);
router.get("/gcp/project/resources", getProjectResources);
// Legacy execution route (keep for backward compatibility)
router.post("/gcp/recovery/execute/:planId/:datacenterId", executeRecoveryPlan);
router.post("/gcp/recovery/update_status/:stepId", updateRecoveryStepStatus);

// New modular execution routes
router.post("/gcp/recovery/v2/execute/:planId/:datacenterId", startRecoveryPlanExecution);
router.post("/gcp/recovery/v2/resume/:planId", resumeExecution);
router.get("/gcp/recovery/v2/status/:planId", getExecutionStatus);
router.get("/gcp/recovery/v2/progress/:planId", getExecutionProgress);
router.post("/gcp/recovery/v2/cancel/:planId", cancelExecution);

// Get approval details by token
router.get("/gcp/recovery/approval/:token", getStepByToken);

// Handle approval decisions
router.post("/gcp/recovery/approval/:token", handleApprovalDecision);

// Resume execution after approval - no auth required for this route
// as it's called from the approval service after email approval
router.post("/gcp/recovery/resume/:planId", async (req, res) => {
	try {
		const { planId } = req.params;
		const { checkpointId, datacenterId: requestDatacenterId } = req.body;

		console.log(`Received request to resume plan ${planId} after checkpoint ${checkpointId}`);
		console.log(`Request body:`, req.body);

		if (!planId) {
			res.status(400).json({ error: "Plan ID is required" });
			return;
		}

		if (!checkpointId) {
			res.status(400).json({ error: "Checkpoint ID is required" });
			return;
		}

		// Get the recovery plan to get the current execution ID and datacenter ID
		const { data: recoveryPlan, error: planError } = await supabase
			.from("recovery_plans_new")
			.select("*")
			.eq("id", planId)
			.single();

		if (planError || !recoveryPlan) {
			console.error("Error fetching recovery plan:", planError);
			res.status(404).json({ error: "Recovery plan not found" });
			return;
		}

		// Get the datacenter ID from the request, plan metadata, or use default
		const datacenterId =
			requestDatacenterId || recoveryPlan.execution_metadata?.datacenter_id || "default";

		console.log(`Using datacenter ID: ${datacenterId}`);

		// Resume execution
		console.log(
			`Calling resumeRecoveryPlanExecution for plan ${planId}, checkpoint ${checkpointId}`
		);
		const plan = await resumeRecoveryPlanExecution(planId, checkpointId);
		console.log(`Plan execution resumed successfully:`, plan);

		// Instead of calling executeRecoveryPlan through the API route (which requires authentication),
		// call the controller function directly to continue execution
		console.log(`Directly executing recovery plan to continue execution`);

		try {
			// First try to get the execution ID from the recovery plan
			let executionId = recoveryPlan.current_execution_id;

			// If not found, try to get the execution details from the executions table
			if (!executionId) {
				const { data: execution, error: executionError } = await supabase
					.from("recovery_plan_executions")
					.select("*")
					.eq("recovery_plan_id", planId)
					.eq("status", "in_progress")
					.order("created_at", { ascending: false })
					.limit(1)
					.single();

				if (executionError || !execution) {
					console.error("Error finding active execution:", executionError);
					throw new Error("No active execution found for this plan");
				}

				executionId = execution.id;
			}

			console.log(`Found active execution: ${executionId}`);

			// Import the executeNextSteps function from the recovery controller
			const { executeNextSteps } = require("../controllers/recoveryController");

			// Execute the next steps directly
			await executeNextSteps(planId, executionId, checkpointId, datacenterId);

			// Return success response
			res.status(200).json({
				message: "Recovery plan execution resumed successfully",
				planId,
				checkpointId,
				executionId,
				datacenterId,
			});
			return;
		} catch (error: any) {
			console.error("Error executing next steps:", error);
			res.status(500).json({ error: error.message });
			return;
		}
	} catch (error: any) {
		console.error(`Error resuming execution:`, error);
		res.status(500).json({ error: error.message });
	}
});

router.get("/admin/integrations/config", isAdmin, getIntegrationConfig);
router.put("/admin/integrations/config", isAdmin, updateIntegrationConfig);
router.post("/admin/integrations/sync", isAdmin, triggerSync);
router.get("/admin/integrations/stats", isAdmin, getSyncStats);

export default router;
