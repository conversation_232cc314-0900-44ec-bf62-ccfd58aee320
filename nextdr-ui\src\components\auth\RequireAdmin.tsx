import { Navigate } from "react-router-dom";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { Skeleton } from "@/components/ui/skeleton";

interface RequireAdminProps {
	children: React.ReactNode;
}

export function RequireAdmin({ children }: RequireAdminProps) {
	const { isAdmin, isLoading } = useIsAdmin();

	if (isLoading) {
		return (
			<div className="flex h-screen items-center justify-center">
				<div className="space-y-4">
					<Skeleton className="h-8 w-[200px]" />
					<Skeleton className="h-8 w-[150px]" />
				</div>
			</div>
		);
	}

	if (!isAdmin) {
		return <Navigate to="/" replace />;
	}

	return <>{children}</>;
}
