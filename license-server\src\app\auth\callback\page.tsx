"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { supabase } from "@/lib/supabase/client";

export default function AuthCallbackPage() {
	const router = useRouter();
	const searchParams = useSearchParams();

	useEffect(() => {
		const handleAuth = async () => {
			try {
				const redirectTo = searchParams.get("redirectTo") || "/admin/dashboard";

				// Handle the auth callback
				const { data, error } = await supabase.auth.getSession();

				if (error) {
					console.error("Auth callback error:", error);
					router.push(`/login?error=${encodeURIComponent(error.message)}`);
					return;
				}

				if (data.session) {
					// Successfully authenticated, redirect to the intended page
					router.push(redirectTo);
				} else {
					// No session found, redirect to login
					router.push("/login?error=Authentication failed");
				}
			} catch (error) {
				console.error("Unexpected error during authentication:", error);
				router.push("/login?error=Authentication failed");
			}
		};

		handleAuth();
	}, [router, searchParams]);

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="text-center">
				<h2 className="text-xl font-semibold text-gray-900">Completing sign in...</h2>
				<p className="mt-2 text-gray-600">Please wait while we complete your authentication.</p>
			</div>
		</div>
	);
}
