import nodemailer from "nodemailer";
import { config } from "../config";

// Create a transporter using Gmail SMTP
const transporter = nodemailer.createTransport({
	service: "gmail",
	auth: {
		user: config.GMAIL_USER,
		pass: config.GMAIL_APP_PASSWORD, // Use app password for Gmail
	},
});

/**
 * Send an email using the configured transporter
 * @param to Recipient email address
 * @param subject Email subject
 * @param html HTML content of the email
 * @returns Promise that resolves when email is sent
 */
export const sendEmail = async (to: string, subject: string, html: string): Promise<void> => {
	try {
		console.log(`Attempting to send email to ${to} with subject "${subject}"`);
		console.log(`Using Gmail user: ${config.GMAIL_USER}`);

		// Log email configuration (without password)
		console.log("Email configuration:", {
			service: "gmail",
			auth: {
				user: config.GMAIL_USER,
				pass: config.GMAIL_APP_PASSWORD ? "********" : "NOT_SET",
			},
		});

		const result = await transporter.sendMail({
			from: config.GMAIL_USER,
			to,
			subject,
			html,
		});

		console.log(`Email sent successfully to ${to}`);
		console.log(`Message ID: ${result.messageId}`);
		console.log(`Response: ${result.response}`);
	} catch (error: any) {
		console.error("Error sending email:", error);

		try {
			console.error("Error details:", JSON.stringify(error, null, 2));
		} catch (jsonError) {
			console.error("Error could not be stringified:", error);
		}

		// Check for common Gmail authentication issues
		if (error.message?.includes("Invalid login")) {
			console.error("Gmail authentication failed. Please check your username and app password.");
		} else if (error.message?.includes("5.7.0")) {
			console.error(
				"Gmail rejected the authentication. This might be due to security settings or the app password being revoked."
			);
		}

		throw error;
	}
};

/**
 * Send an approval request email
 * @param to Recipient email address
 * @param stepName Name of the step requiring approval
 * @param planName Name of the recovery plan
 * @param approvalToken Unique token for approval link
 * @returns Promise that resolves when email is sent
 */
export const sendApprovalEmail = async (
	to: string,
	stepName: string,
	planName: string,
	approvalToken: string
): Promise<void> => {
	const approvalLink = `${config.FRONTEND_URL}/approve/${approvalToken}`;

	const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Recovery Step Approval Required</h2>
      <p>Hello,</p>
      <p>Your approval is required for the following recovery step:</p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Plan:</strong> ${planName}</p>
        <p><strong>Step:</strong> ${stepName}</p>
      </div>
      <p>Please click the button below to review and approve or reject this step:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${approvalLink}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Review and Approve
        </a>
      </div>
      <p>Or copy and paste this link into your browser:</p>
      <p>${approvalLink}</p>
      <p>This link will expire in 24 hours.</p>
      <p>Thank you,<br>NextDR Recovery System</p>
    </div>
  `;

	return sendEmail(to, `Recovery Step Approval Required: ${stepName}`, html);
};

/**
 * Send a verification request email
 * @param to Recipient email address
 * @param stepName Name of the step requiring verification
 * @param planName Name of the recovery plan
 * @param approvalToken Unique token for verification link
 * @returns Promise that resolves when email is sent
 */
export const sendVerificationEmail = async (
	to: string,
	stepName: string,
	planName: string,
	approvalToken: string
): Promise<void> => {
	const approvalLink = `${config.FRONTEND_URL}/approve/${approvalToken}`;

	const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Recovery Step Verification Required</h2>
      <p>Hello,</p>
      <p>Your verification is required for the following recovery step:</p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Plan:</strong> ${planName}</p>
        <p><strong>Step:</strong> ${stepName}</p>
      </div>
      <p>Please click the button below to verify the current state before proceeding:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${approvalLink}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Review and Verify
        </a>
      </div>
      <p>Or copy and paste this link into your browser:</p>
      <p>${approvalLink}</p>
      <p>This link will expire in 24 hours.</p>
      <p>Thank you,<br>NextDR Recovery System</p>
    </div>
  `;

	return sendEmail(to, `Recovery Step Verification Required: ${stepName}`, html);
};

/**
 * Send a notification email
 * @param to Recipient email address
 * @param subject Email subject
 * @param message Notification message
 * @param planName Name of the recovery plan
 * @param stepName Name of the step sending the notification
 * @returns Promise that resolves when email is sent
 */
export const sendNotificationEmail = async (
	to: string,
	subject: string,
	message: string,
	planName: string,
	stepName: string
): Promise<void> => {
	const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Recovery Plan Notification</h2>
      <p>Hello,</p>
      <p>You have received a notification from the recovery process:</p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Plan:</strong> ${planName}</p>
        <p><strong>Step:</strong> ${stepName}</p>
        <p><strong>Message:</strong> ${message}</p>
      </div>
      <p>This is an automated notification. No action is required.</p>
      <p>Thank you,<br>NextDR Recovery System</p>
    </div>
  `;

	return sendEmail(to, subject, html);
};
