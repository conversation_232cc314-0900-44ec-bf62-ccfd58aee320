import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, Users } from "lucide-react";
import { useUsers } from "@/lib/api/hooks/users";
import { useGroups } from "@/lib/api/hooks/groups";

interface AssigneeBadgeProps {
	assigneeType?: "user" | "group";
	assigneeId?: string;
	assigneeName?: string;
}

const AssigneeBadge: React.FC<AssigneeBadgeProps> = ({
	assigneeType,
	assigneeId,
	assigneeName,
}) => {
	const [showMembers, setShowMembers] = useState(false);
	const { users } = useUsers();
	const { groups } = useGroups();

	if (!assigneeType || !assigneeId) {
		return <Badge variant="outline">Unassigned</Badge>;
	}

	if (assigneeType === "user") {
		const user = users?.find((u) => u.id === assigneeId);
		return (
			<Badge variant="secondary" className="flex items-center gap-1">
				<Users className="h-3 w-3" />
				{user?.email || assigneeName || "Unknown User"}
			</Badge>
		);
	}

	const group = groups?.find((g) => g.id === assigneeId);
	const groupMembers = group?.members || [];

	return (
		<div className="flex flex-col gap-1">
			<div className="flex items-center gap-2">
				<Badge variant="secondary" className="flex items-center gap-1">
					<Users className="h-3 w-3" />
					{group?.name || assigneeName || "Unknown Group"}
				</Badge>
				{groupMembers.length > 0 && (
					<Button
						variant="ghost"
						size="sm"
						className="h-6 px-2"
						onClick={() => setShowMembers(!showMembers)}
					>
						{showMembers ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
					</Button>
				)}
			</div>
			{showMembers && groupMembers.length > 0 && (
				<div className="pl-4 mt-1 space-y-1">
					{groupMembers.map((member) => (
						<div key={member.id} className="text-sm text-muted-foreground">
							{member.email}
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default AssigneeBadge;
