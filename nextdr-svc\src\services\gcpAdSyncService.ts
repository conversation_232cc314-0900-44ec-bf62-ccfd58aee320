import { google } from "googleapis";
import { supabase } from "./supabaseService";

interface IntegrationConfig {
	id: string;
	source: string;
	enabled: boolean;
	config: {
		service_account_key: string;
		domain: string;
		sync_interval_minutes: number;
	};
}

interface SyncResult {
	total_users: number;
	total_groups: number;
	synced_users: number;
	synced_groups: number;
	conflicts: number;
	errors: string[];
}

class GcpAdSyncService {
	async performSync(config: IntegrationConfig): Promise<SyncResult> {
		const syncResult: SyncResult = {
			total_users: 0,
			total_groups: 0,
			synced_users: 0,
			synced_groups: 0,
			conflicts: 0,
			errors: [],
		};

		try {
			let serviceAccountCredentials;
			try {
				serviceAccountCredentials = JSON.parse(config.config.service_account_key);
			} catch (error) {
				throw new Error("Invalid service account key format");
			}

			const auth = new google.auth.GoogleAuth({
				credentials: serviceAccountCredentials,
				scopes: [
					"https://www.googleapis.com/auth/admin.directory.user.readonly",
					"https://www.googleapis.com/auth/admin.directory.group.readonly",
				],
			});

			const admin = google.admin({ version: "directory_v1", auth });

			const { data: gcpUsers } = await admin.users.list({
				domain: config.config.domain,
				maxResults: 500,
			});

			syncResult.total_users = gcpUsers?.users?.length || 0;

			for (const gcpUser of gcpUsers?.users || []) {
				try {
					await this.syncUser(gcpUser, syncResult);
				} catch (error: any) {
					syncResult.errors.push(`User ${gcpUser.primaryEmail}: ${error.message}`);
					syncResult.conflicts++;
				}
			}

			const { data: gcpGroups } = await admin.groups.list({
				domain: config.config.domain,
				maxResults: 500,
			});

			syncResult.total_groups = gcpGroups?.groups?.length || 0;

			for (const gcpGroup of gcpGroups?.groups || []) {
				try {
					await this.syncGroup(gcpGroup, syncResult);
				} catch (error: any) {
					syncResult.errors.push(`Group ${gcpGroup.name}: ${error.message}`);
					syncResult.conflicts++;
				}
			}

			await this.updateSyncStats(syncResult);

			return syncResult;
		} catch (error: any) {
			console.error("GCP AD sync error:", error);
			throw new Error(`Failed to sync with GCP AD: ${error.message}`);
		}
	}

	private async syncUser(gcpUser: any, syncResult: SyncResult): Promise<void> {
		const email = gcpUser.primaryEmail;
		const externalId = gcpUser.id;

		const { data: existingUser, error: fetchError } = await supabase
			.from("user_profiles")
			.select("*")
			.eq("email", email)
			.single();

		if (fetchError && fetchError.code !== "PGRST116") {
			throw fetchError;
		}

		const syncMetadata = {
			external_id: externalId,
			last_synced_at: new Date().toISOString(),
			sync_status: "synced",
		};

		if (existingUser) {
			const updateData: any = {
				source: "gcp_ad",
				sync_metadata: syncMetadata,
				updated_at: new Date().toISOString(),
			};

			if (existingUser.role !== "admin") {
				updateData.role = existingUser.role || "viewer";
			}

			const { error: updateError } = await supabase
				.from("user_profiles")
				.update(updateData)
				.eq("id", existingUser.id);

			if (updateError) {
				throw updateError;
			}
		} else {
			const { data: authUser, error: authError } = await supabase.auth.admin.inviteUserByEmail(
				email,
				{
					data: {
						source: "gcp_ad",
						external_id: externalId,
					},
				}
			);

			if (authError) {
				throw authError;
			}

			if (authUser.user) {
				const { error: profileError } = await supabase.from("user_profiles").insert({
					id: authUser.user.id,
					email: email,
					role: "viewer",
					status: "invited",
					source: "gcp_ad",
					sync_metadata: syncMetadata,
				});

				if (profileError) {
					throw profileError;
				}
			}
		}

		syncResult.synced_users++;
	}

	private async syncGroup(gcpGroup: any, syncResult: SyncResult): Promise<void> {
		const groupName = gcpGroup.name;
		const externalId = gcpGroup.id;

		const { data: existingGroup, error: fetchError } = await supabase
			.from("internal_groups")
			.select("*")
			.eq("name", groupName)
			.single();

		if (fetchError && fetchError.code !== "PGRST116") {
			throw fetchError;
		}

		const syncMetadata = {
			external_id: externalId,
			last_synced_at: new Date().toISOString(),
			sync_status: "synced",
		};

		if (existingGroup) {
			const { error: updateError } = await supabase
				.from("internal_groups")
				.update({
					metadata: {
						...existingGroup.metadata,
						source: "gcp_ad",
						sync_metadata: syncMetadata,
					},
					updated_at: new Date().toISOString(),
				})
				.eq("id", existingGroup.id);

			if (updateError) {
				throw updateError;
			}
		} else {
			const { error: createError } = await supabase.from("internal_groups").insert({
				name: groupName,
				description: `Synced from GCP AD: ${gcpGroup.description || groupName}`,
				type: "internal",
				metadata: {
					source: "gcp_ad",
					sync_metadata: syncMetadata,
				},
			});

			if (createError) {
				throw createError;
			}
		}

		syncResult.synced_groups++;
	}

	private async updateSyncStats(syncResult: SyncResult): Promise<void> {
		await supabase.from("sync_stats").upsert({
			source: "gcp_ad",
			total_users: syncResult.total_users,
			total_groups: syncResult.total_groups,
			synced_users: syncResult.synced_users,
			synced_groups: syncResult.synced_groups,
			conflicts: syncResult.conflicts,
			last_sync_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		});
	}
}

export const gcpAdSyncService = new GcpAdSyncService();
