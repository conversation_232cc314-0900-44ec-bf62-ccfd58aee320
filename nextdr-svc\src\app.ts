import express from "express";
import vmRoutes from "./routes/vmRoutes";
import gcpRoutes from "./routes/gcpRoutes";
import licenseRoutes from "./routes/license";
import approvalRoutes from "./routes/approvalRoutes";
import cors from "cors";
import cookieParser from "cookie-parser";
import eventsRouter from "./routes/sseManager/eventsService";
import authMiddleware from "./middleware/authMiddleware";

const app = express();

app.use(
	cors({
		origin: ["http://localhost:8080", "http://localhost:3000", "http://localhost:5173"],
		credentials: true,
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
		allowedHeaders: ["Content-Type", "Authorization", "Accept"],
		exposedHeaders: ["Set-Cookie", "Content-Type"],
	})
);
app.use(cookieParser());
app.use(express.json());

// Apply authentication middleware
app.use(authMiddleware);

// API Routes
app.use("/api/vm", vmRoutes);
app.use("/api", gcpRoutes);
app.use("/api", licenseRoutes);
app.use("/api", eventsRouter);
app.use("/api/approval", approvalRoutes);

export default app;
