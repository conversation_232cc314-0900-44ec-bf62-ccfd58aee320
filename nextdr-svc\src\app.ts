import express from "express";
import vmRoutes from "./routes/vmRoutes";
import gcpRoutes from "./routes/gcpRoutes";
import licenseRoutes from "./routes/license";
import approvalRoutes from "./routes/approvalRoutes";
import cors from "cors";
import cookieParser from "cookie-parser";
import eventsRouter from "./routes/sseManager/eventsService";
import authMiddleware from "./middleware/authMiddleware";
import { licenseCheck } from "./middleware/licenseCheck";

const app = express();

app.use(
	cors({
		origin: ["http://localhost:8080", "http://localhost:3000", "http://localhost:5173"],
		credentials: true,
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
		allowedHeaders: ["Content-Type", "Authorization", "Accept"],
		exposedHeaders: ["Set-Cookie", "Content-Type"],
	})
);
app.use(cookieParser());
app.use(express.json());

// Apply authentication middleware
app.use(authMiddleware);

// Apply license check middleware to all routes except license and approval routes
app.use((req, res, next) => {
	// Skip license check for license-related routes and approval routes
	const publicRoutes = [
		"/api/license/activate",
		"/api/license/verify",
		"/api/license/generate",
		"/api/license/status",
		"/api/approve",
		"/api/gcp/recovery/approval", // Allow unauthenticated access to approval routes
		"/api/gcp/recovery/resume", // Allow unauthenticated access to resume routes (called from email)
	];

	// Check if the current path starts with any of the public routes
	const isPublicRoute = publicRoutes.some((route) => req.path.startsWith(route));

	if (isPublicRoute) {
		console.log(`Skipping license check for public route: ${req.path}`);
		next();
		return;
	}

	// Apply license check for all other routes
	console.log(`Applying license check for protected route: ${req.path}`);
	licenseCheck(req, res, next);
});

// API Routes
app.use("/api/vm", vmRoutes);
app.use("/api", gcpRoutes);
app.use("/api", licenseRoutes);
app.use("/api", eventsRouter);
app.use("/api/approval", approvalRoutes);

export default app;
