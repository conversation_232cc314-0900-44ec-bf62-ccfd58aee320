import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";

/**
 * Executor for approval steps
 * This executor doesn't actually execute anything - it just validates that approval is required
 */
export class ApprovalExecutor extends BaseStepExecutor {
	constructor() {
		super("Approval");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Processing approval step: ${step.name}`);

		try {
			this.validate(step);
			const config = this.parseConfiguration(step);

			// Approval steps are handled by the execution engine
			// This executor just confirms the step is properly configured

			const approverInfo = step.approval_metadata || {};
			const { approver_id, approver_role, approval_message } = approverInfo;

			if (!approver_id && !approver_role) {
				throw new Error("Approval step must specify either approver_id or approver_role");
			}

			this.log(
				`Approval step configured for: ${
					approver_id ? `user ${approver_id}` : `role ${approver_role}`
				}`
			);

			return this.createSuccessResult("Approval step configured successfully", {
				approver_id,
				approver_role,
				approval_message,
				requires_approval: true,
			});
		} catch (error: any) {
			this.log(`Approval step validation failed: ${error.message}`, "error");
			return this.createErrorResult(
				`Approval step validation failed: ${error.message}`,
				error.message
			);
		}
	}

	validate(step: any): boolean {
		super.validate(step);

		if (step.operation_type !== "Approval") {
			throw new Error(`Invalid operation type for approval: ${step.operation_type}`);
		}

		if (!step.requires_approval) {
			throw new Error("Approval step must have requires_approval set to true");
		}

		return true;
	}
}
