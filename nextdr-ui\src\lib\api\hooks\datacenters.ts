import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../../supabase-client";
import { toast } from "@/components/ui/sonner";
import { Datacenter } from "@/lib/types";

export const useDatacenters = () => {
  return useQuery({
    queryKey: ["datacenters"],
    queryFn: async () => {
      const { data, error } = await supabase.from("datacenters2").select("*");

      if (error) {
        throw new Error(error.message);
      }

      return data as Datacenter[];
    },
  });
};

export const useAddDatacenter = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (datacenter: Omit<Datacenter, "id" | "created_at">) => {
      const { data, error } = await supabase
        .from("datacenters2")
        .insert(datacenter)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data as Datacenter;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datacenters"] });
      toast.success("Datacenter added successfully");
    },
    onError: (error) => {
      toast.error(`Failed to add datacenter: ${error.message}`);
    },
  });
};

export const useDeleteDatacenter = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (datacenterId: string) => {
      const { error } = await supabase.from("datacenters2").delete().eq("id", datacenterId);

      if (error) {
        throw new Error(error.message);
      }

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["datacenters"] });
      toast.success("Datacenter deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete datacenter: ${error.message}`);
    },
  });
};
