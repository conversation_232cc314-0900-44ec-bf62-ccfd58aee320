# NextDR Licensing System

This document describes the on-prem licensing system for NextDR.

## Overview

The licensing system uses asymmetric JWT signing (RS256) to create and validate license tokens. Licenses are created in the backend, validated on each API call, and activated from the frontend.

## Setup

1. Generate RSA keys:

```bash
cd nextdr-svc
node scripts/generate-keys.js
```

This will:
- Generate a private key in `nextdr-svc/keys/private.key`
- Generate a public key in `nextdr-svc/keys/public.key`
- Copy the public key to `lib/license/public.key` for frontend use
- Add the private key to `.gitignore`

## License Generation (Admin)

To generate a license for a customer:

```bash
curl -X POST http://localhost:8081/api/license/generate \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": "acme-inc",
    "exp": **********
  }'
```

This will return a JWT token that can be provided to the customer.

## License Activation (User)

Users can activate their license by:

1. Navigating to `/license` in the frontend
2. Pasting their license token
3. Clicking "Activate License"

The license will be stored as an HttpOnly cookie and used for all subsequent API calls.

## License Validation

The backend validates the license on each API call using the `licenseCheck` middleware. If the license is invalid or expired, the API will return a 401 Unauthorized response.

## Database Schema

Licenses are stored in the `licenses` table:

```sql
create table if not exists public.licenses (
  id uuid primary key default gen_random_uuid(),
  customer_id text not null,
  jwt text not null,
  issued_at timestamptz default now()
);
```

## Security Considerations

- The private key (`private.key`) should be kept secure and not committed to version control
- The public key (`public.key`) can be distributed and is used for verification
- Licenses are stored as HttpOnly cookies to prevent JavaScript access
- The backend validates the license on each API call

## License Format

Licenses are JWT tokens with the following payload:

```json
{
  "sub": "customer-id",
  "exp": **********
}
```

- `sub`: Customer identifier
- `exp`: Expiration timestamp (Unix timestamp)

## API Endpoints

- `POST /api/license/generate`: Generate a license token (admin only)
- `POST /api/license/activate`: Activate a license token
- `POST /api/license/verify`: Verify a license token

## Frontend Components

- `LicenseForm`: Component for activating a license
- `lib/license/licenseCheck`: Utility functions for checking license status
- `/license`: Page for license activation
