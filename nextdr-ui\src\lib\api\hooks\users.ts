import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";
import { User } from "@supabase/supabase-js";
import { UserRole } from "@/types/rbac";

interface UserWithRole extends User {
	role: UserRole;
}

export const useUsers = () => {
	const queryClient = useQueryClient();

	const {
		data: users,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["users"],
		queryFn: async () => {
			const { data, error } = await supabase.from("user_profiles").select("*");

			console.log(data);

			if (error) throw error;
			return data as UserWithRole[];
		},
	});

	const updateUserRole = useMutation({
		mutationFn: async ({ userId, role }: { userId: string; role: UserRole }) => {
			const { error } = await supabase.rpc("update_user_role", {
				target_user_id: userId,
				new_role: role,
			});
			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["users"] });
		},
	});

	return {
		users,
		isLoading,
		error,
		updateUserRole,
	};
};
