/**
 * <PERSON><PERSON><PERSON> to generate RSA keys for license signing
 * 
 * This script generates a private key and a public key for license signing.
 * The private key is used to sign licenses and should be kept secure.
 * The public key is used to verify licenses and can be distributed.
 * 
 * Usage:
 * node scripts/generate-keys.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create keys directory if it doesn't exist
const keysDir = path.join(__dirname, '../keys');
if (!fs.existsSync(keysDir)) {
  fs.mkdirSync(keysDir, { recursive: true });
}

// Create frontend keys directory if it doesn't exist
const frontendKeysDir = path.join(__dirname, '../../lib/license');
if (!fs.existsSync(frontendKeysDir)) {
  fs.mkdirSync(frontendKeysDir, { recursive: true });
}

// Generate private key
console.log('Generating private key...');
execSync('openssl genrsa -out keys/private.key 2048', {
  cwd: path.join(__dirname, '..'),
  stdio: 'inherit'
});

// Generate public key
console.log('Generating public key...');
execSync('openssl rsa -in keys/private.key -pubout -out keys/public.key', {
  cwd: path.join(__dirname, '..'),
  stdio: 'inherit'
});

// Copy public key to frontend
console.log('Copying public key to frontend...');
fs.copyFileSync(
  path.join(keysDir, 'public.key'),
  path.join(frontendKeysDir, 'public.key')
);

console.log('Keys generated successfully!');
console.log('Private key: ' + path.join(keysDir, 'private.key'));
console.log('Public key: ' + path.join(keysDir, 'public.key'));
console.log('Frontend public key: ' + path.join(frontendKeysDir, 'public.key'));
console.log('\nIMPORTANT: Keep the private key secure and do not commit it to version control!');

// Add private key to .gitignore if not already there
const gitignorePath = path.join(__dirname, '../../.gitignore');
if (fs.existsSync(gitignorePath)) {
  let gitignore = fs.readFileSync(gitignorePath, 'utf8');

  if (!gitignore.includes('nextdr-svc/keys/private.key')) {
    gitignore += '\n# License keys\nnextdr-svc/keys/private.key\n';
    fs.writeFileSync(gitignorePath, gitignore);
    console.log('Added private key to .gitignore');
  }
}
