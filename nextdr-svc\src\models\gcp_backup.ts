export interface GCPBackup {
	id: string; // Unique backup ID
	schedule_id: string; // Reference to snapshot schedule
	project_config: string; // JSON string of project config
	vm_config: string; // JSON string of VM config
	created_at: string; // ISO timestamp
	name: string; // Friendly name for the backup
	gcp_snapshot_ids: string[]; // List of GCP snapshot IDs created
	status: "pending" | "completed" | "failed";
}

// Helper types for the config objects
export interface ProjectConfig {
	projectId: string;
	labels?: Record<string, string>;

	iamPolicies: {
		role: string;
		members: string[];
	}[];

	enabledApis: string[];

	networks: {
		name: string;
		autoCreateSubnetworks: boolean;
		routingMode: string;
		subnets: {
			name: string;
			region: string;
			ipCidrRange: string;
			privateIpGoogleAccess: boolean;
		}[];
		firewalls: {
			name: string;
			direction: string;
			priority: number;
			sourceRanges: string[];
			allowed: {
				IPProtocol: string;
				ports?: string[];
			}[];
			disabled?: boolean;
		}[];
		routes: {
			name: string;
			destRange: string;
			nextHopGateway: string;
			priority: number;
		}[];
	}[];

	serviceAccounts: {
		email: string;
		displayName: string;
		roles: string[];
	}[];
}

export interface VmProperties {
	name: string;
	id: string;
	zone: string;
	status: string;
	machineType: string;
	tags: string[];
	disks: Array<{
		deviceName: string;
		type: string;
		boot: boolean;
		autoDelete: boolean;
		source: string;
		interface: string;
		mode: string;
	}>;
	networkInterfaces: Array<{
		network: string;
		subnetwork: string;
		networkIP: string;
		accessConfigs?: Array<{
			type: string;
			name: string;
			natIP?: string;
		}>;
	}>;
	serviceAccounts: Array<{
		email: string;
		scopes: string[];
	}>;
	metadata: {
		items: Array<{
			key: string;
			value: string;
		}>;
	};
	scheduling: {
		preemptible: boolean;
		onHostMaintenance: string;
		automaticRestart: boolean;
	};
	labels: Record<string, string>;
	canIpForward: boolean;
	deletionProtection: boolean;
}

export interface BackupExecution {
	executionId: string; // Unique ID per execution
	scheduleId: string; // Backup schedule ID
	triggeredAt: string; // Timestamp of execution
	status: "PENDING" | "SUCCESS" | "FAILED";
	projectConfigSnapshot: ProjectConfig; // Project-level IAM, APIs, etc.
	vmSnapshots: VMSnapshotRecord[]; // One or more VM snapshot records
}
export interface VMSnapshotRecord {
	vmConfig: VmProperties; // Full VM metadata/config at time of backup
	snapshots: DiskSnapshotRecord[]; // List of disk snapshots
}

export interface DiskSnapshotRecord {
	deviceName: string; // e.g., "boot", "data-disk-1"
	diskName: string; // Original disk name
	snapshotId: string; // Created snapshot name/ID
	snapshotLink?: string; // (Optional) full GCP resource link
}

export interface RecoveryPlan {
	id: string;
	name: string;
	created_at: string;
	status: "pending" | "completed" | "failed";
	app_group: string;
	description: string;
}

export interface RecoverySteps {
	id: string;
	recovery_plan_id: string;
	name: string;
	description?: string;
	step_order: number;
	operation_type: string;
	status: string;
	config?: string;
	configuration?: any;
	approval_metadata?: {
		approver_type: "user" | "group";
		approver_id: string;
		approver_name: string;
		approver_role?: string;
		approval_status: "approved" | "pending" | "rejected";
		approval_comment?: string;
		approved_by?: string;
		approved_at?: string;
		approval_ip?: string;
	};
	created_at: string;
	updated_at: string;
}

export interface RecoveryStepConfig {
	vm_name: string;
	vm_zone: string;
	vm_project_id: string;
}
