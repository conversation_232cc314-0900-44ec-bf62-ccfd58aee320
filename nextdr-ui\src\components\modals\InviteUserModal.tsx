import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { UserRole } from "@/types/rbac";

const inviteSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
	role: z.enum(["admin", "operator", "approver", "viewer"] as const),
});

type InviteFormValues = z.infer<typeof inviteSchema>;

interface InviteUserModalProps {
	isOpen: boolean;
	onClose: () => void;
	onInvite: (email: string, role: UserRole) => Promise<void>;
}

const InviteUserModal = ({ isOpen, onClose, onInvite }: InviteUserModalProps) => {
	const { toast } = useToast();
	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		reset,
	} = useForm<InviteFormValues>({
		resolver: zodResolver(inviteSchema),
		defaultValues: {
			email: "",
			role: "viewer",
		},
	});

	const onSubmit = async (data: InviteFormValues) => {
		try {
			await onInvite(data.email, data.role);
			reset();
		} catch (error: any) {
			toast({
				title: "Failed to invite user",
				description: error.message || "Failed to invite user",
			});
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Invite New User</DialogTitle>
				</DialogHeader>
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="email">Email Address</Label>
						<Input id="email" type="email" placeholder="<EMAIL>" {...register("email")} />
						{errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
					</div>

					<div className="space-y-2">
						<Label htmlFor="role">Role</Label>
						<Select
							defaultValue="viewer"
							onValueChange={(value) => register("role").onChange({ target: { value } })}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select a role" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="admin">Admin</SelectItem>
								<SelectItem value="operator">Operator</SelectItem>
								<SelectItem value="approver">Approver</SelectItem>
								<SelectItem value="viewer">Viewer</SelectItem>
							</SelectContent>
						</Select>
						{errors.role && <p className="text-sm text-red-500">{errors.role.message}</p>}
					</div>

					<div className="flex justify-end gap-3 pt-4">
						<Button type="button" variant="outline" onClick={onClose}>
							Cancel
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? "Inviting..." : "Send Invite"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default InviteUserModal;
