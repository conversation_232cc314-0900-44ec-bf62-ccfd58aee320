-- Add source and sync_metadata columns to user_profiles if they don't exist
ALTER TABLE user_profiles
ADD COLUMN IF NOT EXISTS source TEXT NOT NULL DEFAULT 'native' CHECK (source IN ('native', 'gcp_ad')),
ADD COLUMN IF NOT EXISTS sync_metadata JSONB DEFAULT '{}'::jsonb;

-- Create integration_configs table
CREATE TABLE IF NOT EXISTS integration_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source TEXT NOT NULL CHECK (source IN ('native', 'gcp_ad')),
    enabled BOOLEAN NOT NULL DEFAULT false,
    last_sync_at TIMESTAMPTZ,
    sync_status TEXT CHECK (sync_status IN ('idle', 'syncing', 'success', 'error', 'in_progress')),
    error_message TEXT,
    config JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create sync_stats table
CREATE TABLE IF NOT EXISTS sync_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source TEXT NOT NULL CHECK (source IN ('native', 'gcp_ad')),
    total_users INTEGER NOT NULL DEFAULT 0,
    total_groups INTEGER NOT NULL DEFAULT 0,
    synced_users INTEGER NOT NULL DEFAULT 0,
    synced_groups INTEGER NOT NULL DEFAULT 0,
    conflicts INTEGER NOT NULL DEFAULT 0,
    last_sync_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_user_profiles_source ON user_profiles(source);
CREATE INDEX IF NOT EXISTS idx_integration_configs_source ON integration_configs(source);
CREATE INDEX IF NOT EXISTS idx_sync_stats_source ON sync_stats(source);

-- Add RLS policies
ALTER TABLE integration_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage integration configs"
    ON integration_configs
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can view sync stats"
    ON sync_stats
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_integration_configs_updated_at
    BEFORE UPDATE ON integration_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sync_stats_updated_at
    BEFORE UPDATE ON sync_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();