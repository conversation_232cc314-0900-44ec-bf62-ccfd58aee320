import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";

/**
 * Verify a license token using the public key
 *
 * @param token License token to verify
 * @returns Decoded token payload or null if invalid
 */
export const verifyLicenseToken = (token: string): any | null => {
	try {
		// Basic validation of token format
		if (!token || typeof token !== "string" || token.split(".").length !== 3) {
			console.error("Invalid token format");
			return null;
		}

		// Read public key
		const publicKeyPath = path.join(__dirname, "../../keys/public.key");

		if (!fs.existsSync(publicKeyPath)) {
			console.error("License public key not found at path:", publicKeyPath);
			return null;
		}

		const publicKey = fs.readFileSync(publicKeyPath, "utf8");

		if (!publicKey || publicKey.trim() === "") {
			console.error("Public key is empty or invalid");
			return null;
		}

		// Verify token
		const decoded = jwt.verify(token, publicKey, { algorithms: ["RS256"] });
		return decoded;
	} catch (error) {
		console.error("License verification error:", error);
		if (error instanceof Error) {
			console.error("Error name:", error.name);
			console.error("Error message:", error.message);
		}
		return null;
	}
};

/**
 * Get license information from a token
 *
 * @param token License token
 * @returns License information or null if invalid
 */
export const getLicenseInfo = (token: string): { customerId: string; expiresAt: Date } | null => {
	const decoded = verifyLicenseToken(token);

	if (!decoded) {
		return null;
	}

	return {
		customerId: decoded.sub,
		expiresAt: new Date(decoded.exp * 1000),
	};
};

/**
 * Check if a license token is valid
 *
 * @param token License token
 * @returns True if valid, false otherwise
 */
export const isLicenseValid = (token: string): boolean => {
	return verifyLicenseToken(token) !== null;
};
