import { <PERSON><PERSON><PERSON><PERSON> } from "cron";
import { createBackupForVM } from "../controllers/backupController";
import { supabase } from "./supabaseService";
import { Request, Response } from "express";

interface SnapshotSchedule {
	id: string;
	created_at: string;
	group_id: string;
	vm_ids: string[];
	frequency: string;
	retention: number;
	start_time: string;
	day_of_week: number;
	day_of_month: number;
	status: string;
	next_run: string;
	last_run: string;
	datacenter_id: string;
}

class SnapshotScheduler {
	private jobs: Map<string, CronJob<any, any>> = new Map();

	async initializeSchedules() {
		try {
			const { data: schedules, error } = await supabase.from("snapshot_schedules").select("*");

			if (error) throw error;
			schedules?.forEach((schedule: SnapshotSchedule) =>
				this.createBackupJob(schedule, schedule.datacenter_id)
			);
		} catch (error) {
			console.error("Error initializing backup schedules:", error);
		}
	}

	/**
	 * Updates an existing backup job or creates a new one if it doesn't exist
	 * @param schedule The updated schedule configuration
	 */
	async updateSchedule(schedule: SnapshotSchedule) {
		try {
			// Stop any existing jobs for this schedule's VMs
			for (const vm_id of schedule.vm_ids) {
				if (!vm_id) continue;

				// Find and stop any existing jobs for this VM
				const jobPrefix = `${vm_id}-`;
				for (const [key, _] of this.jobs) {
					if (key.startsWith(jobPrefix)) {
						this.stopJob(key);
					}
				}
			}

			// Create new jobs with the updated schedule
			this.createBackupJob(schedule, schedule.datacenter_id);
			return true;
		} catch (error) {
			console.error("Error updating backup schedule:", error);
			return false;
		}
	}

	private mapFrequencyToCron(
		frequency: string,
		startTime?: string,
		dayOfWeek?: number,
		dayOfMonth?: number
	): string {
		const validFrequencies = ["hourly", "daily", "weekly", "monthly"];
		if (!validFrequencies.includes(frequency)) {
			console.warn(`Invalid frequency: ${frequency}, defaulting to daily`);
			frequency = "daily";
		}
		let hour = 0;
		let minute = 0;

		if (startTime) {
			const [hourStr, minuteStr] = startTime.split(":");
			hour = parseInt(hourStr, 10);
			minute = parseInt(minuteStr, 10);
			if (isNaN(hour) || hour < 0 || hour > 23) {
				console.warn(`Invalid hour in start time: ${startTime}, using default 0`);
				hour = 0;
			}

			if (isNaN(minute) || minute < 0 || minute > 59) {
				console.warn(`Invalid minute in start time: ${startTime}, using default 0`);
				minute = 0;
			}
		}
		const dow = dayOfWeek !== undefined && dayOfWeek >= 0 && dayOfWeek <= 6 ? dayOfWeek : 0;
		const dom = dayOfMonth !== undefined && dayOfMonth >= 1 && dayOfMonth <= 31 ? dayOfMonth : 1;

		switch (frequency) {
			case "hourly":
				return `0 ${minute} * * * *`;
			case "daily":
				return `0 ${minute} ${hour} * * *`;
			case "weekly":
				return `0 ${minute} ${hour} * * ${dow}`;
			case "monthly":
				return `0 ${minute} ${hour} ${dom} * *`;
			default:
				throw new Error("Invalid frequency");
		}
	}

	private createBackupJob(schedule: SnapshotSchedule, datacenterId: string) {
		for (const vm_id of schedule.vm_ids) {
			if (!vm_id) {
				// console.warn(`Skipping backup job creation for undefined VM ID in schedule ${schedule.id}`);
				continue;
			}
			const jobKey = `${vm_id}-${schedule.frequency}-${schedule.start_time || "default"}`;
			this.stopJob(jobKey);

			const cronExpression = this.mapFrequencyToCron(
				schedule.frequency,
				schedule.start_time,
				schedule.day_of_week,
				schedule.day_of_month
			);

			// console.log(`Creating backup job for VM ${vm_id} with cron: ${cronExpression}`);

			const job = new CronJob(
				cronExpression,
				async () => {
					try {
						// console.log(`Executing backup for VM ${vm_id} at ${new Date().toISOString()}`);
						await createBackupForVM(vm_id, schedule.frequency, schedule.retention, datacenterId);
					} catch (error) {
						console.error(`Backup failed for ${vm_id}:`, error);
					}
				},
				() => {
					try {
						let lastRun = new Date().toISOString();
						let nextRun = job.nextDate().toJSDate().toISOString();
						supabase
							.from("snapshot_schedules")
							.update({ last_run: lastRun, next_run: nextRun })
							.eq("id", schedule.id);
						// console.log(
						// 	`Updated schedule ${schedule.id} with last_run: ${lastRun} and next_run: ${nextRun}`
						// );
					} catch (error) {
						console.error(`Error updating schedule ${schedule.id}:`, error);
					}
				},
				true
			);
			this.jobs.set(jobKey, job);
		}
	}

	stopJob(jobKey: string) {
		const existingJob = this.jobs.get(jobKey);
		if (existingJob) {
			existingJob.stop();
			this.jobs.delete(jobKey);
		}
	}

	/**
	 * Get information about all active backup jobs
	 * @returns Array of job information objects
	 */
	getActiveJobs() {
		const activeJobs = [];
		for (const [key, job] of this.jobs) {
			const [vmId, frequency, startTime] = key.split("-");
			activeJobs.push({
				vmId,
				frequency,
				startTime: startTime === "default" ? null : startTime,
				nextRun: job.nextDate().toJSDate(),
				isActive: job.isActive,
			});
		}
		return activeJobs;
	}

	// createTestBackupJob() {
	// 	const now = new Date();
	// 	now.setSeconds(now.getSeconds() + 20);

	// 	const hours = now.getHours().toString().padStart(2, "0");
	// 	const minutes = now.getMinutes().toString().padStart(2, "0");
	// 	const startTime = `${hours}:${minutes}`;

	// 	const vmId = "instance-20250311-043524";
	// 	console.log(`Setting up test backup job for VM ID: ${vmId} at ${startTime}`);

	// 	const testSchedule: SnapshotSchedule = {
	// 		id: `test-${Date.now()}`,
	// 		created_at: new Date().toISOString(),
	// 		group_id: "test-group",
	// 		vm_ids: [vmId],
	// 		frequency: "daily",
	// 		retention: 1,
	// 		start_time: startTime,
	// 		day_of_week: 0,
	// 		day_of_month: 1,
	// 		status: "active",
	// 		next_run: "",
	// 		last_run: "",
	// 	};

	// 	console.log(`Creating test backup job to run at ${startTime}`);
	// 	this.createBackupJob(testSchedule);

	// 	const activeJobs = this.getActiveJobs();
	// 	console.log(`Active jobs after test job creation: ${JSON.stringify(activeJobs)}`);

	// 	console.log(`Executing immediate backup for VM ${vmId}...`);
	// 	setTimeout(async () => {
	// 		try {
	// 			console.log(`Executing backup for VM ${vmId} at ${new Date().toISOString()}`);
	// 			await createBackupForVM(vmId, testSchedule.frequency, testSchedule.retention);
	// 		} catch (error) {
	// 			console.error(`Backup failed for ${vmId}:`, error);
	// 		}
	// 	}, 1000);

	// 	return testSchedule;
	// }
}

export const snapshotScheduler = new SnapshotScheduler();
