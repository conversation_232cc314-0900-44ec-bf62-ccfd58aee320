import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";
import { useVMsInZone } from "@/lib/api/hooks/vms";
import { useSnapshots } from "@/lib/api/hooks/snapshots";
import { useUpdateRecoveryStep } from "@/lib/api/hooks/recoveryPlans";
import { Card, CardContent } from "@/components/ui/card";
import { Pencil, ChevronDown, Server, Clock, Calendar } from "lucide-react";

interface VM {
	id: string;
	name: string;
	status?: string;
	zone?: string;
	machineType?: string;
	backuptimes: {
		name: string;
		state: string;
		backupType: string;
		description: string;
		createTime: string;
	}[];
}

interface VMRestoreSelection {
	vm_id: string;
	restore_point_id: string;
	rename_to?: string;
}

interface VMRestoreStepProps {
	step: RecoveryStep;
	onEdit: () => void;
}

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	vm_ids: z.array(z.string()).min(1, "At least one VM must be selected"),
	snapshot_time: z.string().min(1, "Snapshot time is required"),
	rename_pattern: z.enum(["keep_same", "unique", "custom"]),
	custom_name: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const VMRestoreStep: React.FC<VMRestoreStepProps> = ({ step, onEdit }) => {
	const config = step.configuration as {
		vm_ids: string[];
		vm_selections?: VMRestoreSelection[];
		snapshot_time: string;
		rename_pattern: string;
		zone: string;
		project_id: string;
		datacenter_id: string;
	};

	const hasDetailedSelections = config.vm_selections && config.vm_selections.length > 0;

	return (
		<Card className="relative">
			<CardContent className="p-4">
				<div className="flex justify-between items-start mb-4">
					<div>
						<h3 className="font-medium">{step.name}</h3>
						<p className="text-sm text-muted-foreground">VM Restore Operation</p>
					</div>
					<Button variant="outline" size="sm" onClick={onEdit} className="absolute top-4 right-4">
						<Pencil className="h-4 w-4 mr-2" />
						Edit
					</Button>
				</div>

				<div className="space-y-2">
					<div className="grid grid-cols-2 gap-4">
						<div>
							<p className="text-sm font-medium">Snapshot Time</p>
							<p className="text-sm text-muted-foreground">{config.snapshot_time || "Latest"}</p>
						</div>
					</div>

					<div>
						<p className="text-sm font-medium">VMs to Restore</p>
						{hasDetailedSelections ? (
							<div className="mt-2 space-y-2">
								{config.vm_selections!.map((selection) => (
									<div key={selection.vm_id} className="bg-gray-800 p-2 rounded-md">
										<div className="flex items-center justify-between">
											<div className="flex items-center">
												<Server className="h-4 w-4 mr-2 text-blue-500" />
												<span>{selection.vm_id}</span>
											</div>
											{selection.rename_to && (
												<div className="text-xs text-gray-400">
													Rename to: {selection.rename_to}
												</div>
											)}
										</div>
										{selection.restore_point_id && (
											<div className="mt-1 text-xs flex items-center text-gray-400">
												<Clock className="h-3 w-3 mr-1" />
												<span>Restore point: {selection.restore_point_id}</span>
											</div>
										)}
									</div>
								))}
							</div>
						) : (
							<p className="text-sm text-muted-foreground">{config.vm_ids.length} VMs selected</p>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

export default VMRestoreStep;
