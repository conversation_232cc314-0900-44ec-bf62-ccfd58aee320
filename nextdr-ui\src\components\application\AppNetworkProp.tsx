import React, { useState, useEffect } from "react";
import { Network, RefreshCw, Globe, Server } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { getNetworkInterfaces } from "@/lib/api/api-client";
import { NetworkInterface } from "@/lib/types";
interface AppNetworkPropProps {
	discoveredResources?: any;
	projectId?: string;
	datacenterId: string;
}

export default function AppNetworkProp({
	discoveredResources,
	projectId,
	datacenterId,
}: AppNetworkPropProps) {
	const [networkResources, setNetworkResources] = useState<any>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { toast } = useToast();

	const fetchNetworkInterfaces = async () => {
		if (!projectId) return;

		setLoading(true);
		setError(null);
		try {
			const data: NetworkInterface[] = await getNetworkInterfaces(projectId, datacenterId);

			if (data) {
				setNetworkResources(data);
			}
		} catch (err: any) {
			setError(err.message || "Failed to fetch network interfaces");
			toast({
				title: "Error",
				description: "Failed to fetch network interfaces",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (projectId) {
			fetchNetworkInterfaces();
		}
	}, [projectId, datacenterId]);

	const networkInterfaces = networkResources || [];

	return (
		<section className="space-y-6">
			<div className="flex justify-between items-center">
				<div className="flex items-center gap-3">
					<Network className="h-6 w-6 text-blue-500" />
					<h2 className="text-xl font-semibold">Network Interfaces</h2>
					{networkInterfaces.length > 0 && (
						<span className="px-2 py-1 bg-secondary rounded-full text-xs text-muted-foreground">
							{networkInterfaces.length}{" "}
							{networkInterfaces.length === 1 ? "interface" : "interfaces"}
						</span>
					)}
				</div>
				<Button
					onClick={() => fetchNetworkInterfaces()}
					variant="outline"
					size="sm"
					disabled={loading}
				>
					{loading ? (
						<div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					) : (
						<RefreshCw className="h-4 w-4 mr-2" />
					)}
					Refresh
				</Button>
			</div>

			{error && (
				<div className="bg-red-900/20 border border-red-800/30 text-red-400 p-4 rounded-md">
					{error}
				</div>
			)}

			{loading ? (
				<div className="flex justify-center items-center py-8">
					<div className="animate-spin h-6 w-6 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					<span>Loading network interfaces...</span>
				</div>
			) : networkInterfaces.length > 0 ? (
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					{networkInterfaces.map((networkInterface: any, index: number) => (
						<Card key={index} className="overflow-hidden">
							<CardContent className="p-4">
								<div className="flex items-center gap-2 mb-3">
									<Globe className="h-5 w-5 text-blue-500" />
									<h3 className="font-medium">
										{networkInterface.name || `Interface ${index + 1}`}
									</h3>
								</div>

								<div className="space-y-3">
									<div className="flex flex-col gap-1">
										<span className="text-xs text-muted-foreground">Network</span>
										<span className="text-sm font-mono bg-secondary px-2 py-1 rounded">
											{networkInterface.network?.split("/").pop() || "N/A"}
										</span>
									</div>

									<div className="flex flex-col gap-1">
										<span className="text-xs text-muted-foreground">Subnet</span>
										<span className="text-sm font-mono bg-secondary px-2 py-1 rounded">
											{networkInterface.subnetwork?.split("/").pop() || "N/A"}
										</span>
									</div>

									<div className="flex flex-col gap-1">
										<span className="text-xs text-muted-foreground">Internal IP</span>
										<span className="text-sm font-mono bg-secondary px-2 py-1 rounded">
											{networkInterface.networkIP || "N/A"}
										</span>
									</div>

									{networkInterface.accessConfigs && networkInterface.accessConfigs.length > 0 && (
										<div>
											<span className="text-xs text-muted-foreground block mb-1">
												External Access
											</span>
											{networkInterface.accessConfigs.map((config: any, configIndex: number) => (
												<div key={configIndex} className="bg-secondary p-2 rounded mb-2">
													<div className="flex items-center justify-between mb-1">
														<span className="text-xs text-muted-foreground">External IP:</span>
														<span className="text-xs font-mono">{config.natIP || "None"}</span>
													</div>
													<div className="flex items-center justify-between">
														<span className="text-xs text-muted-foreground">Type:</span>
														<span className="text-xs">{config.type || "Unknown"}</span>
													</div>
												</div>
											))}
										</div>
									)}
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			) : (
				<div className="text-center p-8 bg-secondary rounded-lg">
					<Network className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h4 className="text-lg font-medium mb-2">No network interfaces found</h4>
					<p className="text-muted-foreground">This project has no network interfaces configured</p>
				</div>
			)}
		</section>
	);
}
