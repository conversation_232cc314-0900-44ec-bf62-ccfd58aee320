import express from "express";
import { Request, Response, NextFunction } from "express";
import { GCPClientSet, GetGcpClients } from "../lib/gcpClients";

export const getGcpInstance = async (req: Request, res: Response): Promise<void> => {
	try {
		const dataCenterId = req.query.datacenterId;
		const zone = (req.query.zone as string) || "us-central1-c";
		const instanceName = req.query.instanceName as string;

		const clients: GCPClientSet = await GetGcpClients(dataCenterId as string);

		const [instance] = await clients.instancesClient.get({
			project: clients.projectId,
			zone: zone,
			instance: instanceName,
		});

		const [routes] = await clients.routesClient.list({
			project: clients.projectId,
		});

		const disks = instance.disks?.map((disk) => ({
			deviceName: disk.deviceName,
			type: disk.type,
			interface: disk.interface,
			mode: disk.mode,
			boot: disk.boot,
			sizeGb: disk.diskSizeGb,
			source: disk.source,
		}));

		const filteredRoutes = instanceName
			? routes.filter((route) => route.nextHopInstance?.includes(instanceName))
			: routes;

		const formattedRoutes = filteredRoutes.map((route) => ({
			name: route.name,
			destRange: route.destRange,
			nextHop: route.nextHopInstance || route.nextHopGateway || route.nextHopIp,
			priority: route.priority,
			tags: route.tags,
		}));

		const serviceAccounts = instance.serviceAccounts?.map((sa) => ({
			email: sa.email,
			scopes: sa.scopes,
		}));

		// const serviceAccountEmail = instance.serviceAccounts?.[0]?.email;
		// if (!serviceAccountEmail) {
		//   return res.status(404).json({ error: "No service account found for this instance" });
		// }

		// const resourceName = `projects/${PROJECT_ID}/serviceAccounts/${serviceAccountEmail}`;

		// const iamClient = new IAMClient();
		// const [policy] = await iamClient.getIamPolicy({ resource: resourceName });

		res.json({
			name: instance.name,
			status: instance.status,
			machineType: instance.machineType,
			networkInterfaces: instance.networkInterfaces,
			zone: instance.zone,
			labels: instance.labels,
			disks: disks,
			routes: formattedRoutes,
			serviceAccounts: serviceAccounts,
		});
	} catch (error) {
		console.error("Error fetching instance:", error);
		res.status(500).json({ error: "Failed to fetch GCP instance info" });
	}
};
