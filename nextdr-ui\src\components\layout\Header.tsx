import { motion } from "framer-motion";
import { Bell, Search, Shield, User } from "lucide-react";
import { useSidebarStore } from "@/lib/store/useStore";

const Header = ({ title, children }: { title: string; children?: React.ReactNode }) => {
	const { collapsed } = useSidebarStore();

	return (
		<motion.header
			initial={{ marginLeft: collapsed ? 80 : 250 }}
			animate={{ marginLeft: collapsed ? 80 : 250 }}
			transition={{ duration: 0.3, ease: "easeInOut" }}
			className="fixed top-0 right-0 left-0 z-30 glass-effect border-b border-[rgba(29,185,84,0.1)] h-16"
			style={{
				background: "rgba(18, 18, 18, 0.5)",
				boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
			}}
		>
			<div className="absolute inset-0 bg-gradient-to-r from-[rgba(29,185,84,0.03)] to-transparent pointer-events-none"></div>
			<div className="flex items-center justify-between h-full px-6 relative z-10">
				<h1 className="text-xl font-semibold text-[#1DB954]/80 drop-shadow-sm">{title}</h1>

				<div className="flex items-center space-x-4">{children}</div>
			</div>
		</motion.header>
	);
};

export default Header;
