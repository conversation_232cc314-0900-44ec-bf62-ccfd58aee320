import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Navigate, useNavigate } from "react-router-dom";
import { Shield, Eye, EyeOff, Mail, Lock, AlertCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { useAuth } from "@/lib/context/AuthContext";

const authSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
	password: z.string().min(6, "Password must be at least 6 characters"),
});

const magicLinkSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
});

type AuthFormValues = z.infer<typeof authSchema>;
type MagicLinkFormValues = z.infer<typeof magicLinkSchema>;

const Login = () => {
	const navigate = useNavigate();
	const { user, signIn, signUp, signInWithMagicLink, isLoading } = useAuth();
	const [authError, setAuthError] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [magicLinkSent, setMagicLinkSent] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [rememberMe, setRememberMe] = useState(false);

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<AuthFormValues>({
		resolver: zodResolver(authSchema),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	const {
		register: registerMagicLink,
		handleSubmit: handleSubmitMagicLink,
		formState: { errors: magicLinkErrors },
	} = useForm<MagicLinkFormValues>({
		resolver: zodResolver(magicLinkSchema),
		defaultValues: {
			email: "",
		},
	});

	const onSignIn = async (data: AuthFormValues) => {
		setIsSubmitting(true);
		setAuthError(null);

		// In a real implementation, you would store the rememberMe value
		// and handle persistent sessions accordingly
		const { error } = await signIn(data.email, data.password);

		if (error) {
			setAuthError(error);
		} else {
			navigate("/");
		}

		setIsSubmitting(false);
	};

	const onSignUp = async (data: AuthFormValues) => {
		setIsSubmitting(true);
		setAuthError(null);

		const { error } = await signUp(data.email, data.password);

		if (error) {
			setAuthError(error);
		}

		setIsSubmitting(false);
	};

	const onMagicLink = async (data: MagicLinkFormValues) => {
		setIsSubmitting(true);
		setAuthError(null);

		const { error } = await signInWithMagicLink(data.email);

		if (error) {
			setAuthError(error);
		} else {
			setMagicLinkSent(true);
		}

		setIsSubmitting(false);
	};

	if (user && !isLoading) {
		return <Navigate to="/" />;
	}

	if (isLoading) {
		return (
			<div className="flex h-screen items-center justify-center bg-dr-dark">
				<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center">
					<Shield className="mx-auto h-12 w-12 text-dr-purple mb-4 animate-pulse" />
					<motion.div
						initial={{ width: 0 }}
						animate={{ width: 120 }}
						transition={{
							duration: 1.5,
							repeat: Infinity,
							repeatType: "reverse",
						}}
						className="h-1 bg-dr-purple rounded-full mx-auto mt-4"
					/>
					<p className="text-lg mt-4 text-foreground">Loading...</p>
				</motion.div>
			</div>
		);
	}

	return (
		<div className="flex min-h-screen items-center justify-center p-4 bg-dr-dark relative overflow-hidden">
			{/* Background pattern */}
			<div className="absolute inset-0 opacity-5">
				<div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_center,rgba(29,185,84,0.1)_0,rgba(18,18,18,0)_70%)]" />
				<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200%] h-[200%] bg-[radial-gradient(ellipse_at_center,rgba(29,185,84,0.05)_0,rgba(18,18,18,0)_70%)]" />
			</div>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="w-full max-w-md z-10"
			>
				<motion.div
					className="text-center mb-8"
					initial={{ opacity: 0, scale: 0.9 }}
					animate={{ opacity: 1, scale: 1 }}
					transition={{ delay: 0.2, duration: 0.5 }}
				>
					<Shield className="h-14 w-14 text-dr-purple mx-auto mb-3" />
					<h1 className="text-3xl font-bold text-foreground">orKrestrate.AI</h1>
					<p className="text-muted-foreground mt-2">Disaster Recovery Orchestration</p>
				</motion.div>

				<Card className="border-muted/20 shadow-lg shadow-dr-purple/5 backdrop-blur-sm bg-card/95">
					<CardHeader className="pb-4">
						<CardTitle className="text-xl">Welcome Back</CardTitle>
						<CardDescription>Sign in to access your dashboard</CardDescription>
					</CardHeader>

					<Tabs defaultValue="signin" className="w-full">
						<TabsList className="grid grid-cols-3 w-full bg-muted/50">
							<TabsTrigger
								value="signin"
								className="data-[state=active]:bg-dr-purple data-[state=active]:text-black"
							>
								Sign In
							</TabsTrigger>
							<TabsTrigger
								value="signup"
								className="data-[state=active]:bg-dr-purple data-[state=active]:text-black"
							>
								Sign Up
							</TabsTrigger>
							<TabsTrigger
								value="magic"
								className="data-[state=active]:bg-dr-purple data-[state=active]:text-black"
							>
								Magic Link
							</TabsTrigger>
						</TabsList>

						<CardContent className="pt-6">
							<AnimatePresence mode="wait">
								<TabsContent value="signin" asChild>
									<motion.div
										initial={{ opacity: 0, x: -10 }}
										animate={{ opacity: 1, x: 0 }}
										exit={{ opacity: 0, x: 10 }}
										transition={{ duration: 0.2 }}
									>
										<form onSubmit={handleSubmit(onSignIn)} className="space-y-4">
											<div className="space-y-2">
												<Label htmlFor="email" className="text-sm font-medium">
													Email Address
												</Label>
												<div className="relative">
													<Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<Input
														id="email"
														type="email"
														{...register("email")}
														placeholder="<EMAIL>"
														className="pl-10"
													/>
												</div>
												{errors.email && (
													<p className="text-destructive text-sm flex items-center gap-1 mt-1">
														<AlertCircle className="h-3 w-3" />
														{errors.email.message}
													</p>
												)}
											</div>

											<div className="space-y-2">
												<div className="flex justify-between items-center">
													<Label htmlFor="password" className="text-sm font-medium">
														Password
													</Label>
													<button
														type="button"
														onClick={() => alert("Password reset functionality would go here")}
														className="text-xs text-dr-purple hover:text-dr-purple-light transition-colors"
													>
														Forgot password?
													</button>
												</div>
												<div className="relative">
													<Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<Input
														id="password"
														type={showPassword ? "text" : "password"}
														{...register("password")}
														placeholder="••••••••"
														className="pl-10 pr-10"
													/>
													<button
														type="button"
														onClick={() => setShowPassword(!showPassword)}
														className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
													>
														{showPassword ? (
															<EyeOff className="h-4 w-4" />
														) : (
															<Eye className="h-4 w-4" />
														)}
													</button>
												</div>
												{errors.password && (
													<p className="text-destructive text-sm flex items-center gap-1 mt-1">
														<AlertCircle className="h-3 w-3" />
														{errors.password.message}
													</p>
												)}
											</div>

											<div className="flex items-center space-x-2">
												<Checkbox
													id="remember"
													checked={rememberMe}
													onCheckedChange={(checked) => setRememberMe(checked as boolean)}
												/>
												<label
													htmlFor="remember"
													className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
												>
													Remember me
												</label>
											</div>

											{authError && (
												<motion.div
													initial={{ opacity: 0, y: -10 }}
													animate={{ opacity: 1, y: 0 }}
													className="rounded-md bg-destructive/15 p-3 flex items-start gap-2"
												>
													<AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
													<p className="text-sm text-destructive">{authError}</p>
												</motion.div>
											)}

											<Button
												type="submit"
												className="w-full bg-dr-purple hover:bg-dr-purple-dark text-black font-medium"
												disabled={isSubmitting}
											>
												{isSubmitting ? (
													<>
														<span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
														Signing in...
													</>
												) : (
													"Sign In"
												)}
											</Button>
										</form>
									</motion.div>
								</TabsContent>

								<TabsContent value="signup" asChild>
									<motion.div
										initial={{ opacity: 0, x: -10 }}
										animate={{ opacity: 1, x: 0 }}
										exit={{ opacity: 0, x: 10 }}
										transition={{ duration: 0.2 }}
									>
										<form onSubmit={handleSubmit(onSignUp)} className="space-y-4">
											<div className="space-y-2">
												<Label htmlFor="signup-email" className="text-sm font-medium">
													Email Address
												</Label>
												<div className="relative">
													<Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<Input
														id="signup-email"
														type="email"
														{...register("email")}
														placeholder="<EMAIL>"
														className="pl-10"
													/>
												</div>
												{errors.email && (
													<p className="text-destructive text-sm flex items-center gap-1 mt-1">
														<AlertCircle className="h-3 w-3" />
														{errors.email.message}
													</p>
												)}
											</div>

											<div className="space-y-2">
												<Label htmlFor="signup-password" className="text-sm font-medium">
													Password
												</Label>
												<div className="relative">
													<Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
													<Input
														id="signup-password"
														type={showPassword ? "text" : "password"}
														{...register("password")}
														placeholder="••••••••"
														className="pl-10 pr-10"
													/>
													<button
														type="button"
														onClick={() => setShowPassword(!showPassword)}
														className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
													>
														{showPassword ? (
															<EyeOff className="h-4 w-4" />
														) : (
															<Eye className="h-4 w-4" />
														)}
													</button>
												</div>
												{errors.password && (
													<p className="text-destructive text-sm flex items-center gap-1 mt-1">
														<AlertCircle className="h-3 w-3" />
														{errors.password.message}
													</p>
												)}
											</div>

											{authError && (
												<motion.div
													initial={{ opacity: 0, y: -10 }}
													animate={{ opacity: 1, y: 0 }}
													className="rounded-md bg-destructive/15 p-3 flex items-start gap-2"
												>
													<AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
													<p className="text-sm text-destructive">{authError}</p>
												</motion.div>
											)}

											<Button
												type="submit"
												className="w-full bg-dr-purple hover:bg-dr-purple-dark text-black font-medium"
												disabled={isSubmitting}
											>
												{isSubmitting ? (
													<>
														<span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
														Creating account...
													</>
												) : (
													"Create Account"
												)}
											</Button>
										</form>
									</motion.div>
								</TabsContent>

								<TabsContent value="magic" asChild>
									<motion.div
										initial={{ opacity: 0, x: -10 }}
										animate={{ opacity: 1, x: 0 }}
										exit={{ opacity: 0, x: 10 }}
										transition={{ duration: 0.2 }}
									>
										{magicLinkSent ? (
											<div className="space-y-4 text-center py-4">
												<motion.div
													initial={{ scale: 0.9, opacity: 0 }}
													animate={{ scale: 1, opacity: 1 }}
													className="rounded-md bg-green-900/20 p-4"
												>
													<Mail className="h-8 w-8 text-green-500 mx-auto mb-2" />
													<p className="text-sm text-green-500">
														Magic link sent! Please check your email inbox and click the link to
														sign in.
													</p>
												</motion.div>
												<Button
													type="button"
													variant="outline"
													className="w-full mt-4"
													onClick={() => setMagicLinkSent(false)}
												>
													Send another link
												</Button>
											</div>
										) : (
											<form onSubmit={handleSubmitMagicLink(onMagicLink)} className="space-y-4">
												<div className="space-y-2">
													<Label htmlFor="magic-email" className="text-sm font-medium">
														Email Address
													</Label>
													<div className="relative">
														<Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
														<Input
															id="magic-email"
															type="email"
															{...registerMagicLink("email")}
															placeholder="<EMAIL>"
															className="pl-10"
														/>
													</div>
													{magicLinkErrors.email && (
														<p className="text-destructive text-sm flex items-center gap-1 mt-1">
															<AlertCircle className="h-3 w-3" />
															{magicLinkErrors.email.message}
														</p>
													)}
												</div>

												{authError && (
													<motion.div
														initial={{ opacity: 0, y: -10 }}
														animate={{ opacity: 1, y: 0 }}
														className="rounded-md bg-destructive/15 p-3 flex items-start gap-2"
													>
														<AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
														<p className="text-sm text-destructive">{authError}</p>
													</motion.div>
												)}

												<Button
													type="submit"
													className="w-full bg-dr-purple hover:bg-dr-purple-dark text-black font-medium"
													disabled={isSubmitting}
												>
													{isSubmitting ? (
														<>
															<span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
															Sending link...
														</>
													) : (
														"Send Magic Link"
													)}
												</Button>

												<p className="text-xs text-center text-muted-foreground">
													We'll email you a magic link that will sign you in instantly.
												</p>
											</form>
										)}
									</motion.div>
								</TabsContent>
							</AnimatePresence>
						</CardContent>

						<CardFooter className="flex flex-col pt-0">
							<p className="text-xs text-center text-muted-foreground mt-4">
								By signing in, you agree to our Terms of Service and Privacy Policy.
							</p>
						</CardFooter>
					</Tabs>
				</Card>
			</motion.div>
		</div>
	);
};

export default Login;
