-- Create approval tokens table
CREATE TABLE approval_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    step_id UUID NOT NULL REFERENCES recovery_steps_new(id),
    approver_id UUID NOT NULL REFERENCES user_profiles(id),
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_step FOREIGN KEY (step_id) REFERENCES recovery_steps_new(id) ON DELETE CASCADE,
    CONSTRAINT fk_approver FOREIGN KEY (approver_id) REFERENCES user_profiles(id) ON DELETE CASCADE
);

-- Add indexes for faster lookups
CREATE INDEX idx_approval_tokens_token ON approval_tokens(token);
CREATE INDEX idx_approval_tokens_step ON approval_tokens(step_id);
CREATE INDEX idx_approval_tokens_approver ON approval_tokens(approver_id);

-- Add RLS policies for approval tokens
ALTER TABLE approval_tokens ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own approval tokens"
    ON approval_tokens FOR SELECT
    USING (approver_id = auth.uid());

CREATE POLICY "Users can create approval tokens"
    ON approval_tokens FOR INSERT
    WITH CHECK (approver_id = auth.uid());

CREATE POLICY "Users can delete their own approval tokens"
    ON approval_tokens FOR DELETE
    USING (approver_id = auth.uid());

-- Add approval-related columns to recovery steps
ALTER TABLE recovery_steps_new
ADD COLUMN requires_approval BOOLEAN DEFAULT false,
ADD COLUMN approval_metadata JSONB DEFAULT '{}'::jsonb;

-- Create function to check if step requires approval
CREATE OR REPLACE FUNCTION check_step_approval(step_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM recovery_steps_new
        WHERE id = step_id
        AND requires_approval = true
        AND (
            approval_metadata->>'approval_status' IS NULL
            OR approval_metadata->>'approval_status' = 'pending'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 