import { createClient } from '@supabase/supabase-js'

//const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!)

const supabaseUrl = 'https://utcestwwfefexcnmjcjr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0Y2VzdHd3ZmVmZXhjbm1qY2pyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNjY4MTk1NiwiZXhwIjoyMDQyMjU3OTU2fQ.GhSBwLZ7qY-zt1T5nHXfMPy3QdNJiVQ8KG9PMAPeCLA';
const supabase = createClient(supabaseUrl, supabaseKey);
export async function getVMConfig(instanceName: string) {
  const { data, error } = await supabase
    .from('vm_configs')
    .select('*')
    .eq('instance_name', instanceName)
    .single()
    
  if (error) throw error
  return data
} 