import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useModalStore } from "@/lib/store/useStore";

interface ConfirmDialogProps {
  // No props needed as we'll use the modal store
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = () => {
  const { isOpen, modalType, onClose, modalData } = useModalStore();
  const showModal = isOpen && modalType === "confirmDialog";

  // Extract data from modalData
  const title = modalData?.title || "Confirm Action";
  const message = modalData?.message || "Are you sure you want to proceed?";
  const confirmLabel = modalData?.confirmLabel || "Confirm";
  const cancelLabel = modalData?.cancelLabel || "Cancel";
  const variant = modalData?.variant || "default";
  const onConfirm = modalData?.onConfirm || (() => {});

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <AlertDialog open={showModal} onOpenChange={(open) => !open && onClose()}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{message}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{cancelLabel}</AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleConfirm} 
            className={variant === "destructive" ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
          >
            {confirmLabel}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmDialog;
