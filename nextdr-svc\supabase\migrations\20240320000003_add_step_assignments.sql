-- Add assignee fields to recovery steps
ALTER TABLE recovery_steps_new
ADD COLUMN assignee_type TEXT CHECK (assignee_type IN ('user', 'group')),
ADD COLUMN assignee_id UUID,
ADD COLUMN assignee_name TEXT,
ADD COLUMN assignee_details JSONB DEFAULT '{}'::jsonb;

-- Create index for faster lookups
CREATE INDEX idx_recovery_steps_assignee ON recovery_steps_new(assignee_type, assignee_id);

-- Add RLS policies for assignee access
CREATE POLICY "Users can view steps they are assigned to"
    ON recovery_steps_new FOR SELECT
    USING (
        -- Allow if user is directly assigned
        (assignee_type = 'user' AND assignee_id = auth.uid())
        OR
        -- Allow if user is in the assigned group
        (assignee_type = 'group' AND EXISTS (
            SELECT 1 FROM group_members
            WHERE group_id = assignee_id AND user_id = auth.uid()
        ))
        OR
        -- Allow admins to view all steps
        EXISTS (
            SELECT 1 FROM user_profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Create function to resolve step assignees
CREATE OR REPLACE FUNCTION public.resolve_step_assignees(step_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT,
    role TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH step_info AS (
        SELECT assignee_type, assignee_id
        FROM recovery_steps_new
        WHERE id = step_id
    )
    SELECT 
        u.id as user_id,
        u.email,
        p.role,
        p.status
    FROM step_info s
    LEFT JOIN LATERAL (
        SELECT id, email, role, status
        FROM user_profiles
        WHERE s.assignee_type = 'user' AND id = s.assignee_id
        UNION ALL
        SELECT up.id, up.email, up.role, up.status
        FROM group_members gm
        JOIN user_profiles up ON up.id = gm.user_id
        WHERE s.assignee_type = 'group' AND gm.group_id = s.assignee_id
    ) u ON true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 