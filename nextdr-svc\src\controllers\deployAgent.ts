import { NodeSSH } from "node-ssh";

  export const copyAndRunAgent = async (apiURL: string, stepId: string, ipAddress: string, 
      username: string, privateKeyPath: string, localBinaryPath: string, remotePath: string) => {
      const ssh = new NodeSSH();
      console.log("Copying agent to VM", ipAddress, username, privateKeyPath, localBinaryPath, remotePath);
      const fs = require('fs');
      try {
        await ssh.connect({
          host: ipAddress,
          username,
          privateKey: fs.readFileSync(privateKeyPath, 'utf8'),
        });
    
        console.log('SSH connection established.');
    
        try {
          await ssh.putFile(localBinaryPath, remotePath);
          console.log('Agent copied to VM:', remotePath);
        } catch (err) {
          console.error('Failed to copy agent to VM:', err);
        }
    
        try {
          await ssh.execCommand(`chmod +x ${remotePath}`);
          console.log('Agent marked as executable.');
        } catch (err) {
          console.error('Failed to mark agent as executable:', err);
        }
    
        try {
          const { stdout, stderr } = await ssh.execCommand(
            `${remotePath} -api-url ${apiURL} -step-id ${stepId}`
          );
        
        if (stdout) console.log('Agent Output:', stdout);
        if (stderr) console.error('Agent Error:', stderr);
        } catch (err) {
          console.error('Failed to execute agent:', err);
        }
      } catch (err) {
        console.error('Failed to deploy agent:', err);
      } finally {
        ssh.dispose();
      }
    }