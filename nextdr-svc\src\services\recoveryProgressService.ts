import { supabase } from "../services/supabaseService";
import { v4 as uuidv4 } from "uuid";

/**
 * Get all checkpoints for a recovery plan
 * @param planId The recovery plan ID
 * @returns Array of checkpoint records
 */
export const getRecoveryPlanCheckpoints = async (planId: string) => {
	const { data, error } = await supabase
		.from("recovery_plan_checkpoints")
		.select("*")
		.eq("recovery_plan_id", planId);

	if (error) {
		console.error("Error fetching recovery plan checkpoints:", error);
		throw new Error(`Failed to fetch recovery plan checkpoints: ${error.message}`);
	}

	return data || [];
};

/**
 * Create a new progress record for a recovery step
 * @param planId The recovery plan ID
 * @param stepId The recovery step ID
 * @param executionId The execution ID
 * @returns The created progress record
 */
export const createRecoveryPlanProgress = async (
	planId: string,
	stepId: string,
	executionId: string
): Promise<any> => {
	// First, check if a progress record already exists for this step
	const { data: existingProgress } = await supabase
		.from("recovery_plan_progress")
		.select("*")
		.eq("recovery_plan_id", planId)
		.eq("step_id", stepId)
		.single();

	// If a record already exists, update it instead of creating a new one
	if (existingProgress) {
		console.log(`Progress record already exists for step ${stepId}, updating instead of creating`);

		// Update the existing record with a complete reset
		const { data, error } = await supabase
			.from("recovery_plan_progress")
			.update({
				status: "pending",
				started_at: null,
				completed_at: null,
				execution_metadata: {
					execution_id: executionId,
					reset_at: new Date().toISOString(),
					previous_status: existingProgress.status,
				},
			})
			.eq("recovery_plan_id", planId)
			.eq("step_id", stepId)
			.select()
			.single();

		if (error) {
			console.error("Error resetting recovery plan progress:", error);
			throw new Error(`Failed to reset recovery plan progress: ${error.message}`);
		}

		return data;
	}

	// If no record exists, create a new one
	const { data, error } = await supabase
		.from("recovery_plan_progress")
		.insert({
			id: uuidv4(),
			recovery_plan_id: planId,
			step_id: stepId,
			status: "pending",
			execution_metadata: {
				execution_id: executionId,
				created_at: new Date().toISOString(),
			},
		})
		.select()
		.single();

	if (error) {
		console.error("Error creating recovery plan progress:", error);

		// If the error is a duplicate key error, try updating instead
		if (error.code === "23505") {
			// PostgreSQL unique violation code
			console.log(`Duplicate key error for step ${stepId}, trying to update instead`);

			// Get the existing record first
			const { data: existingRecord, error: fetchError } = await supabase
				.from("recovery_plan_progress")
				.select("*")
				.eq("recovery_plan_id", planId)
				.eq("step_id", stepId)
				.single();

			if (fetchError) {
				console.error("Error fetching existing progress record:", fetchError);
				throw new Error(`Failed to fetch existing progress record: ${fetchError.message}`);
			}

			// Update the existing record with a complete reset
			const { data: updatedData, error: updateError } = await supabase
				.from("recovery_plan_progress")
				.update({
					status: "pending",
					started_at: null,
					completed_at: null,
					execution_metadata: {
						execution_id: executionId,
						reset_at: new Date().toISOString(),
						previous_status: existingRecord?.status,
					},
				})
				.eq("recovery_plan_id", planId)
				.eq("step_id", stepId)
				.select()
				.single();

			if (updateError) {
				console.error("Error resetting recovery plan progress:", updateError);
				throw new Error(`Failed to reset recovery plan progress: ${updateError.message}`);
			}

			return updatedData;
		}

		throw new Error(`Failed to create recovery plan progress: ${error.message}`);
	}

	return data;
};

/**
 * Update the status of a recovery step progress
 * @param planId The recovery plan ID
 * @param stepId The recovery step ID
 * @param executionId The execution ID
 * @param status The new status
 * @param metadata Optional additional metadata
 * @returns The updated progress record
 */
export const updateRecoveryPlanProgress = async (
	planId: string,
	stepId: string,
	executionId: string,
	status: string,
	metadata: Record<string, any> = {}
) => {
	// First, check if a progress record exists for this step
	const { data: existingProgress, error: checkError } = await supabase
		.from("recovery_plan_progress")
		.select("*")
		.eq("recovery_plan_id", planId)
		.eq("step_id", stepId);

	if (checkError) {
		console.error("Error checking for existing recovery plan progress:", checkError);
		throw new Error(`Failed to check for existing recovery plan progress: ${checkError.message}`);
	}

	// If no record exists, create a new one instead of updating
	if (!existingProgress || existingProgress.length === 0) {
		console.log(`No progress record found for step ${stepId}, creating a new one`);
		return createRecoveryPlanProgress(planId, stepId, executionId);
	}

	// Update the existing record
	const { data, error } = await supabase
		.from("recovery_plan_progress")
		.update({
			status,
			execution_metadata: {
				execution_id: executionId,
				updated_at: new Date().toISOString(),
				...metadata,
			},
		})
		.eq("recovery_plan_id", planId)
		.eq("step_id", stepId)
		.select();

	if (error) {
		console.error("Error updating recovery plan progress:", error);
		throw new Error(`Failed to update recovery plan progress: ${error.message}`);
	}

	// Return the first record if multiple were updated (should only be one due to unique constraint)
	return data && data.length > 0 ? data[0] : null;
};

/**
 * Get all progress records for a recovery plan execution
 * @param planId The recovery plan ID
 * @param executionId The execution ID (optional, not currently used)
 * @returns Array of progress records
 */
export const getRecoveryPlanProgress = async (planId: string, executionId?: string) => {
	const { data, error } = await supabase
		.from("recovery_plan_progress")
		.select("*")
		.eq("recovery_plan_id", planId)
		.order("created_at", { ascending: true });

	if (error) {
		console.error("Error fetching recovery plan progress:", error);
		throw new Error(`Failed to fetch recovery plan progress: ${error.message}`);
	}

	return data || [];
};

/**
 * Check if a checkpoint is approved
 * @param checkpointId The checkpoint ID
 * @returns Boolean indicating if the checkpoint is approved
 */
export const isCheckpointApproved = async (checkpointId: string) => {
	const { data, error } = await supabase
		.from("recovery_plan_checkpoints")
		.select("approval_status")
		.eq("id", checkpointId)
		.single();

	if (error) {
		console.error("Error checking checkpoint approval status:", error);
		throw new Error(`Failed to check checkpoint approval status: ${error.message}`);
	}

	return data?.approval_status === "approved";
};

/**
 * Create a checkpoint for a recovery plan
 * @param planId The recovery plan ID
 * @param stepId The recovery step ID
 * @param approverId The approver user ID (optional)
 * @param approverRole The approver role (optional)
 * @param metadata Additional metadata to store with the checkpoint (optional)
 * @returns The created checkpoint record
 */
export const createRecoveryPlanCheckpoint = async (
	planId: string,
	stepId: string,
	approverId?: string,
	approverRole?: string,
	metadata?: Record<string, any>
) => {
	// If neither approverId nor approverRole is provided, we'll let the approval service
	// handle finding a suitable approver. We don't throw an error here anymore.
	if (!approverId && !approverRole) {
		console.log(`No approver specified for checkpoint (planId: ${planId}, stepId: ${stepId})`);
	}

	// First, check if a checkpoint already exists for this plan and step
	console.log(`Checking for existing checkpoint for planId=${planId}, stepId=${stepId}`);
	const { data: existingCheckpoint, error: checkError } = await supabase
		.from("recovery_plan_checkpoints")
		.select("*")
		.eq("recovery_plan_id", planId)
		.eq("step_id", stepId)
		.maybeSingle();

	if (checkError) {
		console.error("Error checking for existing checkpoint:", checkError);
		throw new Error(`Failed to check for existing checkpoint: ${checkError.message}`);
	}

	// If a checkpoint already exists, return it
	if (existingCheckpoint) {
		console.log(
			`Found existing checkpoint for planId=${planId}, stepId=${stepId}:`,
			existingCheckpoint
		);

		// If the existing checkpoint is not in pending status, we might want to reset it
		if (existingCheckpoint.approval_status !== "pending") {
			console.log(
				`Existing checkpoint has status ${existingCheckpoint.approval_status}, updating to pending`
			);

			// Update the existing checkpoint to pending status
			const { data: updatedCheckpoint, error: updateError } = await supabase
				.from("recovery_plan_checkpoints")
				.update({
					approval_status: "pending",
					approver_id: approverId || existingCheckpoint.approver_id,
					approver_role: approverRole || existingCheckpoint.approver_role,
					updated_at: new Date().toISOString(),
					// Reset approval fields
					approved_at: null,
					approved_by: null,
					approval_metadata: {
						...existingCheckpoint.approval_metadata,
						comment: null,
						updated_at: new Date().toISOString(),
					},
				})
				.eq("id", existingCheckpoint.id)
				.select()
				.single();

			if (updateError) {
				console.error("Error updating existing checkpoint:", updateError);
				throw new Error(`Failed to update existing checkpoint: ${updateError.message}`);
			}

			return updatedCheckpoint;
		}

		return existingCheckpoint;
	}

	// If no checkpoint exists, create a new one
	console.log(`Creating new checkpoint for planId=${planId}, stepId=${stepId}`);
	try {
		// Include metadata in the approval_metadata field if provided
		const approval_metadata = metadata ? { ...metadata, created_at: new Date().toISOString() } : {};

		const { data, error } = await supabase
			.from("recovery_plan_checkpoints")
			.insert({
				id: uuidv4(),
				recovery_plan_id: planId,
				step_id: stepId,
				approver_id: approverId,
				approver_role: approverRole,
				approval_required: true,
				approval_status: "pending",
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				approval_metadata: approval_metadata,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating recovery plan checkpoint:", error);

			// If it's a duplicate key error, try to fetch the existing checkpoint
			if (error.code === "23505") {
				// PostgreSQL unique constraint violation code
				console.log(`Duplicate key error, fetching existing checkpoint`);
				const { data: existingData, error: fetchError } = await supabase
					.from("recovery_plan_checkpoints")
					.select("*")
					.eq("recovery_plan_id", planId)
					.eq("step_id", stepId)
					.single();

				if (fetchError) {
					console.error(
						"Error fetching existing checkpoint after duplicate key error:",
						fetchError
					);
					throw new Error(`Failed to fetch existing checkpoint: ${fetchError.message}`);
				}

				return existingData;
			}

			throw new Error(`Failed to create recovery plan checkpoint: ${error.message}`);
		}

		return data;
	} catch (error) {
		console.error("Exception creating recovery plan checkpoint:", error);
		throw error;
	}
};

/**
 * Update a checkpoint's approval status
 * @param checkpointId The checkpoint ID
 * @param status The new approval status
 * @param userId The user ID who approved/rejected
 * @param comment Optional comment
 * @param metadata Optional additional metadata
 * @returns The updated checkpoint record
 */
export const updateCheckpointApprovalStatus = async (
	checkpointId: string,
	status: "approved" | "rejected",
	userId: string,
	comment?: string,
	metadata: Record<string, any> = {}
) => {
	const { data, error } = await supabase
		.from("recovery_plan_checkpoints")
		.update({
			approval_status: status,
			approved_at: new Date().toISOString(),
			approved_by: userId,
			approval_metadata: {
				comment,
				ip_address: metadata.ip_address,
				user_agent: metadata.user_agent,
				updated_at: new Date().toISOString(),
				...metadata,
			},
		})
		.eq("id", checkpointId)
		.select()
		.single();

	if (error) {
		console.error("Error updating checkpoint approval status:", error);
		throw new Error(`Failed to update checkpoint approval status: ${error.message}`);
	}

	return data;
};

/**
 * Resume execution of a recovery plan after approval
 * @param planId The recovery plan ID
 * @param checkpointId The checkpoint ID that was approved
 * @param datacenterId Optional datacenter ID to use for execution
 * @returns The updated recovery plan
 */
export const resumeRecoveryPlanExecution = async (
	planId: string,
	checkpointId: string,
	datacenterId?: string
) => {
	console.log(`Resuming execution for plan ${planId} after checkpoint ${checkpointId}`);

	const { data: plan, error: planError } = await supabase
		.from("recovery_plans_new")
		.select("*")
		.eq("id", planId)
		.single();

	if (planError || !plan) {
		console.error("Error fetching recovery plan:", planError);
		throw new Error(`Failed to fetch recovery plan: ${planError?.message}`);
	}

	// Get the checkpoint details
	const { data: checkpoint, error: checkpointError } = await supabase
		.from("recovery_plan_checkpoints")
		.select("*")
		.eq("id", checkpointId)
		.single();

	console.log(`Retrieved checkpoint details:`, checkpoint);

	if (checkpointError || !checkpoint) {
		console.error("Error fetching checkpoint:", checkpointError);
		throw new Error(`Failed to fetch checkpoint: ${checkpointError?.message}`);
	}

	// Verify checkpoint is approved
	if (checkpoint.approval_status !== "approved") {
		throw new Error("Cannot resume execution: checkpoint is not approved");
	}

	// Update the plan status to in_progress
	const { data, error } = await supabase
		.from("recovery_plans_new")
		.update({
			execution_status: "in_progress",
			execution_metadata: {
				...plan.execution_metadata,
				resumed_at: new Date().toISOString(),
				resumed_from_checkpoint: checkpointId,
				resumed_by: checkpoint.approved_by,
			},
		})
		.eq("id", planId)
		.select()
		.single();

	if (error) {
		console.error("Error resuming recovery plan execution:", error);
		throw new Error(`Failed to resume recovery plan execution: ${error.message}`);
	}

	// Log the resume action
	await supabase.from("audit_logs").insert({
		user_id: checkpoint.approved_by,
		action: "resume_recovery_plan_execution",
		entity_type: "recovery_plan",
		entity_id: planId,
		details: {
			checkpoint_id: checkpointId,
			execution_id: plan.current_execution_id,
			resumed_at: new Date().toISOString(),
		},
	});

	// Trigger the execution to continue by directly calling executeNextSteps
	try {
		// Use the provided datacenter ID or get it from the plan metadata
		const effectiveDatacenterId =
			datacenterId || plan.execution_metadata?.datacenter_id || "default";

		// Send an SSE update to notify clients that execution is resuming
		const { sendPlanUpdate } = await import("../routes/sseManager/sseManager");
		sendPlanUpdate(planId, "RESUMING", {
			message: "Execution is resuming after approval",
			checkpoint_id: checkpointId,
			datacenter_id: effectiveDatacenterId,
			timestamp: new Date().toISOString(),
		});

		console.log(`Resuming execution for plan ${planId} from checkpoint ${checkpointId}`);

		// Import and directly call executeNextSteps instead of making HTTP requests
		try {
			console.log(`Directly calling executeNextSteps function`);

			// Import the executeNextSteps function from the recovery controller
			const { executeNextSteps } = await import("../controllers/recoveryController");

			// Call executeNextSteps directly
			await executeNextSteps(
				planId,
				plan.current_execution_id,
				checkpointId,
				effectiveDatacenterId
			);

			console.log(`Successfully resumed execution for plan ${planId}`);
			return data;
		} catch (executeError) {
			console.error("Error executing next steps:", executeError);
			throw executeError;
		}
	} catch (error) {
		console.error("Error resuming execution:", error);
		// We don't throw here because we don't want to fail the approval process
		// The user can always manually resume if needed

		// Send an SSE update to notify clients that resuming failed
		try {
			const { sendPlanUpdate } = await import("../routes/sseManager/sseManager");
			sendPlanUpdate(planId, "RESUME_FAILED", {
				message: `Failed to resume execution: ${
					error instanceof Error ? error.message : "Unknown error"
				}`,
				checkpoint_id: checkpointId,
				timestamp: new Date().toISOString(),
			});
		} catch (sseError) {
			console.error("Error sending SSE update:", sseError);
		}
	}

	return data;
};
