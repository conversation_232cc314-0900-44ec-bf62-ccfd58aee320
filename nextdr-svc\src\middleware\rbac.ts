import { Request, Response, NextFunction } from "express";
import { supabase } from "../services/supabaseService";

export enum UserRole {
	ADMIN = "admin",
	OPERATOR = "operator",
	APPROVER = "approver",
	VIEWER = "viewer",
}

export const ROLE_PERMISSIONS = {
	[UserRole.ADMIN]: {
		canManageUsers: true,
		canManageGroups: true,
		canExecuteRecoveryPlans: true,
		canApproveActions: true,
		canViewAuditLogs: true,
		canExportAuditLogs: true,
	},
	[UserRole.OPERATOR]: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: true,
		canApproveActions: false,
		canViewAuditLogs: true,
		canExportAuditLogs: true,
	},
	[UserRole.APPROVER]: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: false,
		canApproveActions: true,
		canViewAuditLogs: true,
		canExportAuditLogs: false,
	},
	[UserRole.VIEWER]: {
		canManageUsers: false,
		canManageGroups: false,
		canExecuteRecoveryPlans: false,
		canApproveActions: false,
		canViewAuditLogs: false,
		canExportAuditLogs: false,
	},
};

interface RBACConfig {
	requiredRole?: UserRole;
	requiredPermission?: keyof (typeof ROLE_PERMISSIONS)[UserRole.ADMIN];
}

/**
 * RBAC middleware to enforce role-based access control
 * @param config Configuration for the RBAC check
 * @returns Express middleware function
 */
export const rbacMiddleware = (config: RBACConfig) => {
	return async (req: Request, res: Response, next: NextFunction) => {
		try {
			const user = req.user;
			if (!user) {
				res.status(401).json({ error: "Authentication required" });
				return;
			}

			const userRole = user.role as UserRole;
			if (!userRole) {
				res.status(403).json({ error: "User role not defined" });
				return;
			}

			if (config.requiredRole && userRole !== config.requiredRole) {
				if (userRole !== UserRole.ADMIN) {
					res.status(403).json({
						error: `Insufficient permissions. Required role: ${config.requiredRole}`,
					});
					return;
				}
			}

			if (config.requiredPermission) {
				const hasPermission = ROLE_PERMISSIONS[userRole][config.requiredPermission];

				if (!hasPermission && userRole !== UserRole.ADMIN) {
					res.status(403).json({
						error: `Insufficient permissions. Required permission: ${config.requiredPermission}`,
					});
					return;
				}
			}

			req.userRole = userRole;
			req.userPermissions = ROLE_PERMISSIONS[userRole];

			next();
		} catch (error) {
			console.error("RBAC middleware error:", error);
			res.status(500).json({ error: "Authorization error" });
		}
	};
};

export const canExecuteRecoveryPlans = rbacMiddleware({
	requiredPermission: "canExecuteRecoveryPlans",
});

export const canApproveActions = rbacMiddleware({
	requiredPermission: "canApproveActions",
});

export const isAdmin = rbacMiddleware({
	requiredRole: UserRole.ADMIN,
});

declare global {
	namespace Express {
		interface Request {
			userRole?: UserRole;
			userPermissions?: (typeof ROLE_PERMISSIONS)[UserRole];
			user?: {
				id: string;
				email?: string;
				role?: string;
				[key: string]: any;
			};
		}
	}
}
