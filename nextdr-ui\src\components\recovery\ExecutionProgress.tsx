import React from 'react';
import { motion } from 'framer-motion';
import { 
    CheckCircle, 
    Clock, 
    AlertCircle, 
    RefreshCw, 
    Play, 
    Pause, 
    Square,
    ArrowRight,
    User,
    Shield
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useExecutionProgress } from '@/hooks/useExecutionProgress';
import { useModalStore } from '@/lib/store/useStore';

interface ExecutionProgressProps {
    planId: string;
    datacenterId: string;
    onExecutionComplete?: () => void;
}

const ExecutionProgress: React.FC<ExecutionProgressProps> = ({
    planId,
    datacenterId,
    onExecutionComplete
}) => {
    const { onOpen } = useModalStore();
    const {
        progress,
        isLoading,
        error,
        isExecuting,
        lastEvent,
        startExecution,
        resumeExecution,
        cancelExecution,
        refreshProgress
    } = useExecutionProgress(planId);

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
            case 'approved':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'in_progress':
                return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
            case 'failed':
            case 'rejected':
                return <AlertCircle className="h-4 w-4 text-red-500" />;
            case 'awaiting_approval':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            default:
                return <Clock className="h-4 w-4 text-gray-400" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
            case 'approved':
                return 'bg-green-500/10 text-green-600 border-green-500/20';
            case 'in_progress':
                return 'bg-blue-500/10 text-blue-600 border-blue-500/20';
            case 'failed':
            case 'rejected':
                return 'bg-red-500/10 text-red-600 border-red-500/20';
            case 'awaiting_approval':
                return 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20';
            default:
                return 'bg-gray-500/10 text-gray-600 border-gray-500/20';
        }
    };

    const handleStartExecution = () => {
        if (progress?.status === 'paused') {
            // Show resume options
            const currentCheckpoint = progress.checkpoints?.find(cp => 
                cp.approval_status === 'approved' || cp.approval_status === 'pending'
            );
            
            onOpen('executionOptions', {
                title: 'Resume Execution',
                message: 'Do you want to resume from where you left off or restart from the beginning?',
                canResume: true,
                currentCheckpointId: currentCheckpoint?.id,
                onRestart: () => startExecution(datacenterId),
                onResume: (checkpointId: string) => resumeExecution(checkpointId, datacenterId)
            });
        } else {
            startExecution(datacenterId);
        }
    };

    const canResume = progress?.status === 'paused' && progress.checkpoints?.some(cp => 
        cp.approval_status === 'approved'
    );

    if (isLoading && !progress) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                        <span>Loading execution status...</span>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error && !progress) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center text-red-500">
                        <AlertCircle className="h-6 w-6 mr-2" />
                        <span>Error: {error}</span>
                        <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={refreshProgress}
                            className="ml-4"
                        >
                            Retry
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Execution Controls */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                            <Play className="h-5 w-5" />
                            Recovery Plan Execution
                        </CardTitle>
                        <div className="flex items-center gap-2">
                            {progress && (
                                <Badge variant="outline" className={getStatusColor(progress.status)}>
                                    {progress.status.replace('_', ' ').toUpperCase()}
                                </Badge>
                            )}
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            {!progress || progress.status === 'not_started' ? (
                                <Button 
                                    onClick={handleStartExecution}
                                    disabled={isExecuting}
                                    className="bg-dr-purple hover:bg-dr-purple-dark"
                                >
                                    <Play className="h-4 w-4 mr-2" />
                                    Start Execution
                                </Button>
                            ) : progress.status === 'paused' ? (
                                <Button 
                                    onClick={handleStartExecution}
                                    disabled={isExecuting}
                                    variant="outline"
                                >
                                    <ArrowRight className="h-4 w-4 mr-2" />
                                    Resume Execution
                                </Button>
                            ) : isExecuting ? (
                                <Button 
                                    onClick={cancelExecution}
                                    variant="destructive"
                                    size="sm"
                                >
                                    <Square className="h-4 w-4 mr-2" />
                                    Cancel
                                </Button>
                            ) : null}

                            <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={refreshProgress}
                                disabled={isLoading}
                            >
                                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                        </div>

                        {progress && (
                            <div className="text-sm text-muted-foreground">
                                {progress.completedSteps} of {progress.totalSteps} steps completed
                            </div>
                        )}
                    </div>

                    {progress && (
                        <div className="mt-4">
                            <div className="flex justify-between items-center text-sm mb-2">
                                <span>Overall Progress</span>
                                <span>{progress.progressPercentage}%</span>
                            </div>
                            <Progress 
                                value={progress.progressPercentage} 
                                className="h-2"
                            />
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Step Progress */}
            {progress && progress.steps && progress.steps.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Step Progress</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {progress.steps.map((step, index) => {
                                const stepProgress = step.progress?.[0];
                                const status = stepProgress?.status || 'pending';
                                const checkpoint = progress.checkpoints?.find(cp => cp.step_id === step.id);
                                const isAwaitingApproval = status === 'awaiting_approval';

                                return (
                                    <motion.div
                                        key={step.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.1 }}
                                        className={`p-4 rounded-lg border ${
                                            status === 'in_progress' 
                                                ? 'border-blue-200 bg-blue-50/50' 
                                                : 'border-gray-200'
                                        }`}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                {getStatusIcon(status)}
                                                <div>
                                                    <div className="font-medium">{step.name}</div>
                                                    <div className="text-sm text-muted-foreground">
                                                        {step.operation_type}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex items-center gap-2">
                                                <Badge variant="outline" className={getStatusColor(status)}>
                                                    {status.replace('_', ' ')}
                                                </Badge>

                                                {isAwaitingApproval && checkpoint && (
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => onOpen('checkpointInfo', {
                                                            checkpoint,
                                                            step,
                                                            onResume: () => resumeExecution(checkpoint.id, datacenterId)
                                                        })}
                                                    >
                                                        <Clock className="h-4 w-4 mr-1" />
                                                        View Approval
                                                    </Button>
                                                )}
                                            </div>
                                        </div>

                                        {isAwaitingApproval && checkpoint && (
                                            <div className="mt-3 p-3 bg-yellow-50 rounded-md border border-yellow-200">
                                                <div className="flex items-center text-yellow-800 text-sm">
                                                    <Clock className="h-4 w-4 mr-2" />
                                                    Waiting for approval from{' '}
                                                    {checkpoint.approver_id ? (
                                                        <span className="flex items-center ml-1">
                                                            <User className="h-3 w-3 mr-1" />
                                                            specific user
                                                        </span>
                                                    ) : (
                                                        <span className="flex items-center ml-1">
                                                            <Shield className="h-3 w-3 mr-1" />
                                                            {checkpoint.approver_role} role
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </motion.div>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Real-time Events */}
            {lastEvent && (
                <Card>
                    <CardHeader>
                        <CardTitle className="text-sm">Latest Event</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-sm">
                            <div className="flex items-center justify-between">
                                <span className="font-medium">{lastEvent.type.replace('_', ' ')}</span>
                                <span className="text-muted-foreground">
                                    {new Date(lastEvent.timestamp).toLocaleTimeString()}
                                </span>
                            </div>
                            {lastEvent.message && (
                                <div className="mt-1 text-muted-foreground">
                                    {lastEvent.message}
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
};

export default ExecutionProgress;
