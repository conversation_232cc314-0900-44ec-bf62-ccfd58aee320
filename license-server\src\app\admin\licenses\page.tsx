"use client";

import { useEffect, useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";

// Temporary types until we have proper type definitions
interface License {
	id: string;
	license_key: string;
	customer_id: string;
	status: string;
	expires_at: string;
	usage_count: number;
}

interface CustomerProfile {
	id: string;
	company_name: string;
}

export default function LicensesPage() {
	const [licenses, setLicenses] = useState<License[]>([]);
	const [customers, setCustomers] = useState<CustomerProfile[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState("");
	const [showGenerateModal, setShowGenerateModal] = useState(false);
	const [newLicense, setNewLicense] = useState({
		customerId: "",
		features: {},
		expiresAt: "",
	});

	useEffect(() => {
		// Remove auth check since AdminLayout handles it
		fetchLicenses();
		fetchCustomers();
	}, []);

	const fetchLicenses = async () => {
		try {
			const response = await fetch("/api/admin/licenses");
			if (!response.ok) throw new Error("Failed to fetch licenses");
			const data = await response.json();
			setLicenses(data);
		} catch (error: any) {
			setError(error.message);
		} finally {
			setLoading(false);
		}
	};

	const fetchCustomers = async () => {
		try {
			const response = await fetch("/api/admin/customers");
			if (!response.ok) throw new Error("Failed to fetch customers");
			const data = await response.json();
			setCustomers(data);
		} catch (error: any) {
			setError(error.message);
		}
	};

	const handleDeactivate = async (licenseId: string) => {
		try {
			const response = await fetch(`/api/admin/licenses?licenseId=${licenseId}`, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to deactivate license");
			fetchLicenses();
		} catch (error: any) {
			setError(error.message);
		}
	};

	const handleGenerateLicense = async (e: React.FormEvent) => {
		e.preventDefault();
		try {
			const response = await fetch("/api/admin/licenses", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(newLicense),
			});

			if (!response.ok) throw new Error("Failed to generate license");

			setShowGenerateModal(false);
			setNewLicense({
				customerId: "",
				features: {},
				expiresAt: "",
			});
			fetchLicenses();
		} catch (error: any) {
			setError(error.message);
		}
	};

	return (
		<AdminLayout title="License Management">
			<div className="space-y-6">
				{/* Header with Generate License button */}
				<div className="flex justify-between items-center">
					<div>
						<p className="text-sm text-gray-500">Manage and monitor all software licenses</p>
					</div>
					<button
						onClick={() => setShowGenerateModal(true)}
						className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
					>
						Generate License
					</button>
				</div>

				{loading ? (
					<div className="text-center py-12">
						<div className="text-xl">Loading licenses...</div>
					</div>
				) : (
					<div>
						{error && (
							<div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
								{error}
							</div>
						)}
						<div className="mt-8 flex flex-col">
							<div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
								<div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
									<div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
										<table className="min-w-full divide-y divide-gray-300">
											<thead className="bg-gray-50">
												<tr>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														License Key
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Customer
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Status
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Expires At
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Usage Count
													</th>
													<th className="relative py-3.5 pl-3 pr-4 sm:pr-6">
														<span className="sr-only">Actions</span>
													</th>
												</tr>
											</thead>
											<tbody className="divide-y divide-gray-200 bg-white">
												{licenses.map((license) => (
													<tr key={license.id}>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{license.license_key}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{customers.find((c) => c.id === license.customer_id)?.company_name ||
																"Unknown"}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															<span
																className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
																	license.status === "active"
																		? "bg-green-100 text-green-800"
																		: license.status === "expired"
																		? "bg-red-100 text-red-800"
																		: "bg-gray-100 text-gray-800"
																}`}
															>
																{license.status}
															</span>
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{new Date(license.expires_at).toLocaleDateString()}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{license.usage_count}
														</td>
														<td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
															{license.status === "active" && (
																<button
																	onClick={() => handleDeactivate(license.id)}
																	className="text-red-600 hover:text-red-900"
																>
																	Deactivate
																</button>
															)}
														</td>
													</tr>
												))}
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Generate License Modal */}
			{showGenerateModal && (
				<div className="fixed z-10 inset-0 overflow-y-auto">
					<div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
						<div className="fixed inset-0 transition-opacity" aria-hidden="true">
							<div className="absolute inset-0 bg-gray-500 opacity-75"></div>
						</div>

						<div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
							<form onSubmit={handleGenerateLicense}>
								<div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
									<div className="space-y-4">
										<div>
											<label
												htmlFor="customerId"
												className="block text-sm font-medium text-gray-700"
											>
												Customer
											</label>
											<select
												id="customerId"
												name="customerId"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newLicense.customerId}
												onChange={(e) =>
													setNewLicense({
														...newLicense,
														customerId: e.target.value,
													})
												}
											>
												<option value="">Select a customer</option>
												{customers.map((customer) => (
													<option key={customer.id} value={customer.id}>
														{customer.company_name}
													</option>
												))}
											</select>
										</div>
										<div>
											<label
												htmlFor="expiresAt"
												className="block text-sm font-medium text-gray-700"
											>
												Expiration Date
											</label>
											<input
												type="date"
												name="expiresAt"
												id="expiresAt"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newLicense.expiresAt}
												onChange={(e) =>
													setNewLicense({
														...newLicense,
														expiresAt: e.target.value,
													})
												}
											/>
										</div>
										<div>
											<label htmlFor="features" className="block text-sm font-medium text-gray-700">
												Features (JSON)
											</label>
											<textarea
												id="features"
												name="features"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												rows={4}
												value={JSON.stringify(newLicense.features, null, 2)}
												onChange={(e) => {
													try {
														const features = JSON.parse(e.target.value);
														setNewLicense({
															...newLicense,
															features,
														});
													} catch (error) {
														// Invalid JSON, ignore
													}
												}}
											/>
										</div>
									</div>
								</div>
								<div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
									<button
										type="submit"
										className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
									>
										Generate
									</button>
									<button
										type="button"
										className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
										onClick={() => setShowGenerateModal(false)}
									>
										Cancel
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			)}
		</AdminLayout>
	);
}
