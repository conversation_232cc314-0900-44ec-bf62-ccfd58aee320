import { createClient } from '@supabase/supabase-js';
import { VMConfig } from '../models/vmConfig';

// const supabaseUrl = process.env.SUPABASE_URL || '';
// const supabaseKey = process.env.SUPABASE_KEY || '';
const supabaseUrl = 'https://utcestwwfefexcnmjcjr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0Y2VzdHd3ZmVmZXhjbm1qY2pyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNjY4MTk1NiwiZXhwIjoyMDQyMjU3OTU2fQ.GhSBwLZ7qY-zt1T5nHXfMPy3QdNJiVQ8KG9PMAPeCLA';

export const supabase = createClient(supabaseUrl, supabaseKey);

export const saveVMConfig = async (vmConfig: VMConfig) => {
  try {
    const { data, error } = await supabase
      .from('vmconfig')
      .insert([vmConfig])
      .select();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving VM config:', error);
    throw error;
  }
}; 