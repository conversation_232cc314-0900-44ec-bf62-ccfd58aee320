import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, Clock, AlertCircle, Refresh<PERSON><PERSON>, Loader } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";

interface StepExecutionProps {
	step: RecoveryStep;
	index: number;
	onComplete: () => void;
	autoStart?: boolean;
	showExecuteButton?: boolean;
	datacenterId: string;
}

const StepExecution = ({
	step,
	index,
	onComplete,
	autoStart = false,
	showExecuteButton = true,
	datacenterId,
}: StepExecutionProps) => {
	const [status, setStatus] = useState<"pending" | "running" | "completed" | "failed">("pending");
	const [progress, setProgress] = useState(0);
	const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
	const [startTime, setStartTime] = useState<number | null>(null);
	const [currentAction, setCurrentAction] = useState<string>("");

	const duration = 2000;

	const actions = [
		"Initializing...",
		"Preparing resources...",
		"Executing operation...",
		"Verifying results...",
		"Finalizing...",
	];

	const startExecution = () => {
		setStatus("running");
		setStartTime(Date.now());
		setProgress(0);
		setCurrentAction(actions[0]);

		// In a real implementation, this would connect to the backend via SSE or WebSockets
		// to get real-time updates on the step execution progress

		let actionIndex = 0;
		const interval = setInterval(() => {
			setProgress((prev) => {
				if (prev >= 100) {
					clearInterval(interval);
					setStatus("completed");
					onComplete();
					toast.success(`Step ${index + 1}: ${step.name} completed successfully`);
					return 100;
				}

				// Calculate time remaining
				const elapsed = Date.now() - (startTime || Date.now());
				const remaining = Math.max(0, duration - elapsed);
				setTimeRemaining(remaining);

				// Update current action based on progress
				const newProgress = Math.min(100, (elapsed / duration) * 100);
				const newActionIndex = Math.floor((newProgress / 100) * (actions.length - 1));
				if (newActionIndex !== actionIndex) {
					actionIndex = newActionIndex;
					setCurrentAction(actions[actionIndex]);
				}

				// Add some randomness to make it look more realistic in the simulation
				const randomFactor = 1 + (Math.random() - 0.5) * 0.1;
				return Math.min(100, newProgress * randomFactor);
			});
		}, 100);

		return () => clearInterval(interval);
	};

	useEffect(() => {
		if (autoStart) {
			startExecution();
		}
	}, [autoStart]);

	// Helper function to format time if needed in the UI
	// Currently not used but kept for future enhancements
	// const formatTimeRemaining = (ms: number): string => {
	// 	const seconds = Math.ceil(ms / 1000);
	// 	const minutes = Math.floor(seconds / 60);
	// 	const remainingSeconds = seconds % 60;
	// 	return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
	// };

	const getStatusIcon = () => {
		switch (status) {
			case "running":
				return <RefreshCw className="h-5 w-5 animate-spin" />;
			case "completed":
				return <CheckCircle className="h-5 w-5 text-green-500" />;
			case "failed":
				return <AlertCircle className="h-5 w-5 text-red-500" />;
			default:
				return <Clock className="h-5 w-5 text-muted-foreground" />;
		}
	};

	return (
		<div className="bg-secondary p-4 rounded-md border border-border relative overflow-hidden">
			{/* Circular progress bar */}
			{status === "running" && (
				<div
					className="absolute inset-0 rounded-md"
					style={{
						background: `conic-gradient(#4f46e5 ${progress * 3.6}deg, transparent 0deg)`,
						opacity: 0.15,
					}}
				/>
			)}
			{/* Border progress */}
			{status === "running" && (
				<div
					className="absolute inset-0 rounded-md"
					style={{
						background: `conic-gradient(#4f46e5 ${progress * 3.6}deg, transparent 0deg)`,
						mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
						maskComposite: "exclude",
						padding: "2px",
						opacity: 1,
					}}
				/>
			)}

			<div className="flex items-start relative z-10">
				<div className="flex-shrink-0 mr-4 mt-1 p-2 rounded-full bg-muted">
					<span className="flex items-center justify-center h-4 w-4 text-xs font-medium">
						{index + 1}
					</span>
				</div>

				<div className="flex-1">
					<div className="flex justify-between">
						<h4 className="font-medium">{step.name}</h4>
						<div className="flex items-center">{getStatusIcon()}</div>
					</div>

					<div className="flex items-center text-xs text-muted-foreground mt-1">
						<span className="capitalize">{step.operation_type.replace("_", " ")}</span>
					</div>

					{status === "running" && (
						<motion.div
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							className="mt-3"
						>
							<div className="flex justify-between items-center text-xs mb-1">
								<span>{currentAction}</span>
								<span>{Math.round(progress)}%</span>
							</div>
							<Progress value={progress} className="h-2" />
						</motion.div>
					)}

					{status === "pending" && showExecuteButton && (
						<Button variant="outline" size="sm" className="mt-3" onClick={startExecution}>
							Execute Step
						</Button>
					)}

					{status === "failed" && showExecuteButton && (
						<Button
							variant="outline"
							size="sm"
							className="mt-3 text-red-500 border-red-500"
							onClick={startExecution}
						>
							<RefreshCw className="mr-1 h-4 w-4" />
							Retry
						</Button>
					)}
				</div>
			</div>
		</div>
	);
};

export default StepExecution;
