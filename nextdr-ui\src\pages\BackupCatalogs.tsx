import React from "react";
import { motion } from "framer-motion";
import { Archive, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";

const BackupCatalogsPage = () => {
	return (
		<PageLayout title="Backup Catalogs">
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-2xl font-bold">Backup Catalogs</h1>
					<p className="text-muted-foreground">Manage your backup snapshots across providers</p>
				</div>
				<Button className="bg-dr-purple hover:bg-dr-purple-dark">
					<Plus className="mr-2 h-4 w-4" />
					New Backup
				</Button>
			</div>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.4 }}
				className="flex flex-col items-center justify-center py-16"
			>
				<Archive className="h-16 w-16 text-muted-foreground mb-4" />
				<h3 className="text-xl font-medium mb-2">No backups found</h3>
				<p className="text-muted-foreground mb-6 max-w-md text-center">
					Configure backup schedules for your application groups to populate this catalog.
				</p>
				<Button className="bg-dr-purple hover:bg-dr-purple-dark">
					<Plus className="mr-2 h-4 w-4" />
					Create Backup Schedule
				</Button>
			</motion.div>
		</PageLayout>
	);
};

export default BackupCatalogsPage;
