import { createClient } from "@supabase/supabase-js";

// Use environment variables or fallback to hardcoded values for development
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || "https://utcestwwfefexcnmjcjr.supabase.co";
const supabaseKey =
	import.meta.env.VITE_SUPABASE_ANON_KEY ||
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0Y2VzdHd3ZmVmZXhjbm1qY2pyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNjY4MTk1NiwiZXhwIjoyMDQyMjU3OTU2fQ.GhSBwLZ7qY-zt1T5nHXfMPy3QdNJiVQ8KG9PMAPeCLA";

if (!supabaseUrl || !supabaseKey) {
	console.error(
		"Missing Supabase credentials. Please add VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY to your environment variables."
	);
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
	auth: {
		persistSession: true,
		autoRefreshToken: true,
	},
});
