import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";
import { Database } from "@/types/database";
import { format } from "date-fns";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRight, Download, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { addDays } from "date-fns";
import { useRBAC } from "@/hooks/useRBAC";
import { DateRange } from "react-day-picker";
import { toast } from "@/components/ui/sonner";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON>alogHeader,
	Dialog<PERSON><PERSON>le,
	DialogTrigger,
} from "@/components/ui/dialog";

type AuditLog = Database["public"]["Tables"]["audit_logs"]["Row"] & {
	userEmail: string;
};

const ITEMS_PER_PAGE = 10;

function formatDetails(log: AuditLog) {
	if (log.action === "role_update") {
		const details = log.details as { new_role: string; old_role: string; reason: string };
		return (
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<Badge variant="secondary">{details.old_role}</Badge>
					<ArrowRight className="h-4 w-4 text-muted-foreground" />
					<Badge variant="default">{details.new_role}</Badge>
				</div>
				{details.reason && (
					<span className="text-sm text-muted-foreground">Reason: {details.reason}</span>
				)}
			</div>
		);
	}

	if (log.action === "status_update") {
		const details = log.details as { new_status: string; old_status: string; reason: string };
		return (
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<Badge variant="secondary">{details.old_status}</Badge>
					<ArrowRight className="h-4 w-4 text-muted-foreground" />
					<Badge variant="default">{details.new_status}</Badge>
				</div>
				{details.reason && (
					<span className="text-sm text-muted-foreground">Reason: {details.reason}</span>
				)}
			</div>
		);
	}

	if (log.action === "resume_recovery_plan_execution") {
		const details = log.details as {
			resumed_at: string;
			execution_id: string;
			checkpoint_id: string;
		};
		return (
			<div className="flex flex-col gap-1">
				<div className="text-sm">
					<span className="text-muted-foreground">Execution ID: </span>
					<span className="font-mono text-xs">{details.execution_id}</span>
				</div>
				<div className="text-sm">
					<span className="text-muted-foreground">Checkpoint ID: </span>
					<span className="font-mono text-xs">{details.checkpoint_id}</span>
				</div>
				<div className="text-sm">
					<span className="text-muted-foreground">Resumed at: </span>
					<span>{format(new Date(details.resumed_at), "MMM d, yyyy HH:mm:ss")}</span>
				</div>
			</div>
		);
	}

	if (log.action === "send_approval_request") {
		const details = log.details as {
			token: string;
			step_id: string;
			expires_at: string;
			approver_id: string;
			recovery_plan_id: string;
		};
		return (
			<div className="flex flex-col gap-1">
				<div className="text-sm">
					<span className="text-muted-foreground">Recovery Plan ID: </span>
					<span className="font-mono text-xs">{details.recovery_plan_id}</span>
				</div>
				<div className="text-sm">
					<span className="text-muted-foreground">Step ID: </span>
					<span className="font-mono text-xs">{details.step_id}</span>
				</div>
				<div className="text-sm">
					<span className="text-muted-foreground">Expires at: </span>
					<span>{format(new Date(details.expires_at), "MMM d, yyyy HH:mm:ss")}</span>
				</div>
			</div>
		);
	}

	// For other action types, show a formatted view of the JSON
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button variant="outline" size="sm">
					View Details
				</Button>
			</DialogTrigger>
			<DialogContent className="max-w-3xl">
				<DialogHeader>
					<DialogTitle>
						{log.action.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
					</DialogTitle>
				</DialogHeader>
				<pre className="p-4 bg-muted rounded-lg overflow-auto max-h-[60vh]">
					{JSON.stringify(log.details, null, 2)}
				</pre>
			</DialogContent>
		</Dialog>
	);
}

export function AuditLogs() {
	const { canExportAuditLogs } = useRBAC();
	const [dateRange, setDateRange] = useState<DateRange>({
		from: addDays(new Date(), -30),
		to: new Date(),
	});
	const [actionFilter, setActionFilter] = useState<string>("");
	const [entityTypeFilter, setEntityTypeFilter] = useState<string>("");
	const [currentPage, setCurrentPage] = useState(1);

	const { data, isLoading } = useQuery({
		queryKey: ["auditLogs", dateRange, actionFilter, entityTypeFilter, currentPage],
		queryFn: async () => {
			let query = supabase
				.from("audit_logs")
				.select("*", { count: "exact" })
				.order("created_at", { ascending: false })
				.range((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE - 1);

			// Add date range filters if available
			if (dateRange?.from) {
				query = query.gte("created_at", dateRange.from.toISOString());
			}
			if (dateRange?.to) {
				query = query.lte("created_at", dateRange.to.toISOString());
			}

			// Add action filter if selected
			if (actionFilter && actionFilter !== "all_actions") {
				query = query.eq("action", actionFilter);
			}

			// Add entity type filter if selected
			if (entityTypeFilter && entityTypeFilter !== "all_entities") {
				query = query.eq("entity_type", entityTypeFilter);
			}

			const { data, error, count } = await query;

			if (error) throw error;

			// If we have user_ids, fetch user information
			const userIds = data.map((log) => log.user_id).filter((id): id is string => id !== null);

			if (userIds.length > 0) {
				const { data: users, error: usersError } = await supabase
					.from("user_profiles")
					.select("id, email")
					.in("id", userIds);

				if (usersError) throw usersError;

				// Create a map of user IDs to emails
				const userMap = new Map(users?.map((user) => [user.id, user.email]) || []);

				// Add user emails to the logs
				return {
					logs: data.map((log) => ({
						...log,
						userEmail: log.user_id ? userMap.get(log.user_id) || "Unknown" : "System",
					})),
					totalCount: count || 0,
				};
			}

			return {
				logs: data.map((log) => ({
					...log,
					userEmail: "System",
				})),
				totalCount: count || 0,
			};
		},
	});

	const totalPages = data ? Math.ceil(data.totalCount / ITEMS_PER_PAGE) : 0;

	const handleExport = async () => {
		if (!canExportAuditLogs()) {
			toast.error("You don't have permission to export audit logs");
			return;
		}

		try {
			const { data, error } = await supabase.rpc("export_audit_logs", {
				start_date: dateRange?.from?.toISOString() || null,
				end_date: dateRange?.to?.toISOString() || null,
				action_filter: actionFilter || null,
				entity_type_filter: entityTypeFilter || null,
			});

			if (error) throw error;

			// Convert to CSV
			const headers = [
				"ID",
				"Timestamp",
				"Actor",
				"Action",
				"Entity Type",
				"Entity ID",
				"Details",
				"IP Address",
				"User Agent",
			];
			const csvContent = [
				headers.join(","),
				...data.map((row: any) =>
					[
						row.id,
						row.timestamp,
						row.actor_email,
						row.action,
						row.entity_type,
						row.entity_id,
						JSON.stringify(row.details),
						row.ip_address,
						row.user_agent,
					].join(",")
				),
			].join("\n");

			// Download file
			const blob = new Blob([csvContent], { type: "text/csv" });
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `audit-logs-${format(new Date(), "yyyy-MM-dd")}.csv`;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
			window.URL.revokeObjectURL(url);
		} catch (error) {
			console.error("Error exporting audit logs:", error);
			toast.error("Failed to export audit logs");
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				<Skeleton className="h-8 w-full" />
				<Skeleton className="h-8 w-full" />
				<Skeleton className="h-8 w-full" />
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center gap-4">
				<DateRangePicker value={dateRange} onChange={setDateRange} />
				<Select value={actionFilter} onValueChange={setActionFilter}>
					<SelectTrigger className="w-[180px]">
						<SelectValue placeholder="Filter by action" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all_actions">All actions</SelectItem>
						<SelectItem value="role_update">Role updates</SelectItem>
						<SelectItem value="status_update">Status updates</SelectItem>
						<SelectItem value="user_deleted">User deletions</SelectItem>
						<SelectItem value="resume_recovery_plan_execution">Recovery Plan Execution</SelectItem>
						<SelectItem value="send_approval_request">Approval Requests</SelectItem>
						<SelectItem value="other">Other Actions</SelectItem>
					</SelectContent>
				</Select>
				<Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
					<SelectTrigger className="w-[180px]">
						<SelectValue placeholder="Filter by entity" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all_entities">All entities</SelectItem>
						<SelectItem value="user">Users</SelectItem>
						<SelectItem value="group">Groups</SelectItem>
					</SelectContent>
				</Select>
				{canExportAuditLogs() && (
					<Button onClick={handleExport} className="ml-auto">
						<Download className="h-4 w-4 mr-2" />
						Export
					</Button>
				)}
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Time</TableHead>
							<TableHead>User</TableHead>
							<TableHead>Action</TableHead>
							<TableHead>Details</TableHead>
							<TableHead>IP Address</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{data?.logs.map((log) => (
							<TableRow key={log.id}>
								<TableCell>{format(new Date(log.created_at), "MMM d, yyyy HH:mm:ss")}</TableCell>
								<TableCell>{log.userEmail}</TableCell>
								<TableCell>
									<Badge variant="outline">{log.action}</Badge>
								</TableCell>
								<TableCell>{formatDetails(log)}</TableCell>
								<TableCell>{log.ip_address || "-"}</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			<div className="flex items-center justify-between">
				<div className="text-sm text-muted-foreground">
					Showing {(currentPage - 1) * ITEMS_PER_PAGE + 1} to{" "}
					{Math.min(currentPage * ITEMS_PER_PAGE, data?.totalCount || 0)} of {data?.totalCount || 0}{" "}
					entries
				</div>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
						disabled={currentPage === 1}
					>
						<ChevronLeft className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
						disabled={currentPage === totalPages}
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
}
