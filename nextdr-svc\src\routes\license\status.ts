import { Router, Request, Response } from "express";
import { supabase } from "../../services/supabaseService";
import { verifyLicenseToken } from "../../utils/licenseUtils";

const router = Router();

const getLicenseStatus = async (req: Request, res: Response): Promise<any> => {
	try {
		const token = req.cookies.license;
		if (!token) {
			return res.status(401).json({
				valid: false,
				error: "No license token found",
			});
		}

		const decoded = verifyLicenseToken(token);
		if (!decoded) {
			return res.status(401).json({
				valid: false,
				error: "Invalid license token",
			});
		}

		if (decoded.exp < Math.floor(Date.now() / 1000)) {
			return res.status(401).json({
				valid: false,
				error: "License expired",
			});
		}

		const { data: licenseData, error: licenseError } = await supabase
			.from("licenses")
			.select("status, features")
			.eq("jwt", token)
			.single();

		if (licenseError || !licenseData) {
			return res.status(401).json({
				valid: false,
				error: "License not found in database",
			});
		}

		if (licenseData.status !== "active") {
			return res.status(401).json({
				valid: false,
				error: "License is not active",
			});
		}

		await supabase
			.from("licenses")
			.update({ last_verified: new Date().toISOString() })
			.eq("jwt", token);

		return res.status(200).json({
			valid: true,
			customerId: decoded.sub,
			expiresAt: new Date(decoded.exp * 1000).toISOString(),
			features: licenseData.features || [],
		});
	} catch (error) {
		console.error("License status check error:", error);
		return res.status(500).json({
			valid: false,
			error: "Failed to verify license status",
		});
	}
};

router.get("/", getLicenseStatus);

export default router;
