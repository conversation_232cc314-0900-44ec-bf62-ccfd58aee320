// sseManager.ts
import { Response } from "express";

/**
 * Client information including response object and authentication details
 */
interface SSEClient {
	res: Response;
	auth: {
		isAuthenticated: boolean;
		user?: {
			id: string;
			email?: string;
			role?: string;
		};
	};
}

/**
 * Map of plan IDs to arrays of client objects
 */
const clients = new Map<string, SSEClient[]>();

/**
 * Register a new SSE client for a specific recovery plan
 * @param planId The recovery plan ID
 * @param res The Express response object
 * @param auth Authentication information
 */
export const registerClient = (
	planId: string,
	res: Response,
	auth: { isAuthenticated: boolean; user?: any } = { isAuthenticated: false }
) => {
	res.setHeader("Content-Type", "text/event-stream");
	res.setHeader("Cache-Control", "no-cache");
	res.setHeader("Connection", "keep-alive");
	res.setHeader("X-Accel-Buffering", "no"); // Disable Nginx buffering

	// Create client object with response and auth info
	const client: SSEClient = {
		res,
		auth,
	};

	// Handle client disconnect
	res.on("close", () => {
		console.log(`Client disconnected from plan ${planId}`);
		const clientArray = clients.get(planId) || [];
		const index = clientArray.findIndex((c) => c.res === res);
		if (index !== -1) {
			clientArray.splice(index, 1);
			clients.set(planId, clientArray);
		}
	});

	const clientArray = clients.get(planId) || [];
	clients.set(planId, [...clientArray, client]);

	// Send initial connection confirmation with auth status
	res.write(
		`data: ${JSON.stringify({
			type: "connection",
			status: "connected",
			planId,
			isAuthenticated: auth.isAuthenticated,
			userId: auth.user?.id,
		})}\n\n`
	);

	console.log(
		`Client connected to plan ${planId}, total clients: ${clientArray.length + 1}, authenticated: ${
			auth.isAuthenticated
		}`
	);
};

/**
 * Send a step update to all clients subscribed to a recovery plan
 * @param planId The recovery plan ID
 * @param stepId The step ID or order
 * @param status The step status
 * @param metadata Optional additional metadata
 */
export const sendStepUpdate = (
	planId: string,
	stepId: string,
	status: string,
	metadata: Record<string, any> = {}
) => {
	console.log(`Sending step update for plan ${planId} step ${stepId} with status ${status}`);

	const clientArray = clients.get(planId);
	if (!clientArray || clientArray.length === 0) {
		console.log(`No clients connected for plan ${planId}`);
		return;
	}

	const data = JSON.stringify({
		type: "step_update",
		stepId,
		status,
		timestamp: new Date().toISOString(),
		...metadata,
	});

	console.log(`Sending update to ${clientArray.length} clients for plan ${planId}`);

	// Send to all connected clients
	clientArray.forEach((client) => {
		try {
			// For approval-related updates, check if client is authenticated
			if (
				(status === "AWAITING_APPROVAL" || status === "APPROVED" || status === "REJECTED") &&
				metadata.approver_id &&
				!client.auth.isAuthenticated
			) {
				// For unauthenticated clients, don't send approver details
				const filteredData = JSON.stringify({
					type: "step_update",
					stepId,
					status,
					timestamp: new Date().toISOString(),
					// Include checkpoint_id but omit approver details
					checkpoint_id: metadata.checkpoint_id,
				});
				client.res.write(`data: ${filteredData}\n\n`);
			} else {
				// For authenticated clients or non-approval updates, send full data
				client.res.write(`data: ${data}\n\n`);
			}
		} catch (error) {
			console.error(`Error sending SSE update to client:`, error);
		}
	});
};

/**
 * Send a plan-level update to all clients subscribed to a recovery plan
 * @param planId The recovery plan ID
 * @param status The plan status
 * @param metadata Optional additional metadata
 */
export const sendPlanUpdate = (
	planId: string,
	status: string,
	metadata: Record<string, any> = {}
) => {
	console.log(`Sending plan update for plan ${planId} with status ${status}`);

	const clientArray = clients.get(planId);
	if (!clientArray || clientArray.length === 0) {
		console.log(`No clients connected for plan ${planId}`);
		return;
	}

	const data = JSON.stringify({
		type: "plan_update",
		planId,
		status,
		timestamp: new Date().toISOString(),
		...metadata,
	});

	// Send to all connected clients
	clientArray.forEach((client) => {
		try {
			client.res.write(`data: ${data}\n\n`);
		} catch (error) {
			console.error(`Error sending SSE update to client:`, error);
		}
	});
};
