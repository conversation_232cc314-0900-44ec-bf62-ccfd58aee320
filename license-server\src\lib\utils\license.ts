import jwt from "jsonwebtoken";
import { supabase } from "../supabase/client";
import { License } from "../types";

const JWT_SECRET = process.env.JWT_SECRET!;

export const generateLicenseKey = async (
	customerId: string,
	features: Record<string, any>,
	expiresAt: Date
) => {
	const licenseKey = jwt.sign(
		{
			customerId,
			features,
			expiresAt: expiresAt.toISOString(),
		},
		JWT_SECRET,
		{ expiresIn: "1y" }
	);

	const { data: license, error } = await supabase
		.from("licenses")
		.insert({
			customer_id: customerId,
			license_key: licenseKey,
			status: "active",
			features,
			expires_at: expiresAt.toISOString(),
			last_verified: new Date().toISOString(),
		})
		.select()
		.single();

	if (error) throw error;
	return license;
};

export const verifyLicense = async (licenseKey: string): Promise<License> => {
	try {
		const decoded = jwt.verify(licenseKey, JWT_SECRET) as any;

		const { data: license, error } = await supabase
			.from("licenses")
			.select("*")
			.eq("license_key", licenseKey)
			.single();

		if (error) throw error;

		if (license.status !== "active") {
			throw new Error("License is not active");
		}

		if (new Date(license.expires_at) < new Date()) {
			await supabase.from("licenses").update({ status: "expired" }).eq("id", license.id);
			throw new Error("License has expired");
		}

		// Update last verified timestamp
		await supabase
			.from("licenses")
			.update({
				last_verified: new Date().toISOString(),
				usage_count: license.usage_count + 1,
			})
			.eq("id", license.id);

		return license;
	} catch (error) {
		if (error instanceof jwt.JsonWebTokenError) {
			throw new Error("Invalid license key");
		}
		throw error;
	}
};

export const deactivateLicense = async (licenseId: string) => {
	const { error } = await supabase
		.from("licenses")
		.update({ status: "inactive" })
		.eq("id", licenseId);

	if (error) throw error;
};

export const getLicenseStatus = async (licenseId: string) => {
	const { data: license, error } = await supabase
		.from("licenses")
		.select("*")
		.eq("id", licenseId)
		.single();

	if (error) throw error;
	return license;
};
