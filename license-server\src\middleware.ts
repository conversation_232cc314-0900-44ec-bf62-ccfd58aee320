import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(req: NextRequest) {
	// Handle root route - redirect to dashboard (client-side auth will handle login redirect if needed)
	if (req.nextUrl.pathname === "/") {
		return NextResponse.redirect(new URL("/admin/dashboard", req.url));
	}

	// Allow all other routes to proceed - auth will be handled client-side
	return NextResponse.next();
}

export const config = {
	matcher: ["/"],
};
