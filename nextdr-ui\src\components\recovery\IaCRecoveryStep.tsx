import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pencil, Code, Calendar } from "lucide-react";
import { RecoveryStep } from "@/lib/types";

interface IaCRecoveryStepProps {
	step: RecoveryStep;
	onEdit: () => void;
}

// Helper function to format timestamp
const formatTimestamp = (timestamp: string) => {
	if (!timestamp) return "N/A";
	const date = new Date(timestamp);
	return date.toLocaleString();
};

const IaCRecoveryStep: React.FC<IaCRecoveryStepProps> = ({ step, onEdit }) => {
	const config = step.configuration as {
		project_naming?: string;
		components?: string[];
		project_naming_option?: "unique" | "same" | "custom";
		custom_project_name?: string;
		snapshot_point_id?: string;
		selected_components?: string[];
	};

	const projectNaming = config.project_naming_option || config.project_naming || "unique";
	const components = config.selected_components || config.components || [];
	const customProjectName = config.custom_project_name || "";
	const snapshotPointId = config.snapshot_point_id || "";

	return (
		<Card className="relative">
			<CardContent className="p-4">
				<div className="flex justify-between items-start mb-4">
					<div>
						<h3 className="font-medium">{step.name}</h3>
						<p className="text-sm text-muted-foreground">Infrastructure as Code Operation</p>
					</div>
					<Button variant="outline" size="sm" onClick={onEdit} className="absolute top-4 right-4">
						<Pencil className="h-4 w-4 mr-2" />
						Edit
					</Button>
				</div>

				<div className="space-y-2">
					<div>
						<p className="text-sm font-medium">Project Naming</p>
						<p className="text-sm text-muted-foreground">
							{projectNaming === "unique"
								? "Generate unique name"
								: projectNaming === "same"
								? "Keep same name"
								: `Custom name: ${customProjectName}`}
						</p>
					</div>

					{snapshotPointId && (
						<div>
							<p className="text-sm font-medium">Snapshot Point</p>
							<p className="text-sm text-muted-foreground flex items-center">
								<Calendar className="h-3 w-3 mr-1" />
								{snapshotPointId}
							</p>
						</div>
					)}

					<div>
						<p className="text-sm font-medium">Components to Recover</p>
						<div className="flex flex-wrap gap-2 mt-1">
							{components.map((component) => (
								<span
									key={component}
									className="px-2 py-1 text-xs bg-blue-900/20 text-blue-500 rounded-full"
								>
									{component}
								</span>
							))}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

export default IaCRecoveryStep;
