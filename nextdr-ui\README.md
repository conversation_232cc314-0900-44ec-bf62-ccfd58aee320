# orKrestrate.AI UI

This is the frontend UI for the orKrestrate.AI.

### 1. Install pnpm

If you don't have pnpm installed, you can install it using npm:

```bash
npm install -g pnpm
```

### 3. Install dependencies

```bash
pnpm install
```

### 4. Set up environment variables

Create a `.env` file in the root of the nextdr-ui directory:

```
VITE_API_SERVER_URL=http://localhost:8081
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. Start the development server

```bash
pnpm dev
```

This will start the development server at http://localhost:5173.

### 6. Build for production

```bash
pnpm build
```

The build artifacts will be stored in the `dist/` directory.
