"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth/AuthProvider";

interface AdminLayoutProps {
	children: React.ReactNode;
	title?: string;
}

export default function AdminLayout({ children, title = "Admin Dashboard" }: AdminLayoutProps) {
	const router = useRouter();
	const { user, loading, signOut } = useAuth();

	useEffect(() => {
		if (!loading && !user) {
			router.push("/login?redirectTo=" + window.location.pathname);
		}
	}, [user, loading, router]);

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-xl">Loading...</div>
			</div>
		);
	}

	if (!user) {
		return null; // Will redirect in useEffect
	}

	// Check if user has admin role
	const userRole = user.user_metadata?.role || "customer";
	const isAdmin = userRole === "admin";

	if (!isAdmin) {
		return (
			<div className="min-h-screen bg-gray-100">
				{/* Navigation */}
				<nav className="bg-white shadow-sm">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="flex justify-between h-16">
							<div className="flex">
								<div className="flex-shrink-0 flex items-center">
									<h1 className="text-xl font-bold">License Server</h1>
								</div>
							</div>
							<div className="flex items-center">
								<span className="text-sm text-gray-700 mr-4">Welcome, {user.email}</span>
								<button
									onClick={async () => {
										await signOut();
										router.push("/login");
									}}
									className="ml-4 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
								>
									Sign Out
								</button>
							</div>
						</div>
					</div>
				</nav>

				{/* Access Denied Message */}
				<main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
					<div className="px-4 py-6 sm:px-0">
						<div className="bg-red-50 border border-red-200 rounded-md p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
										<path
											fillRule="evenodd"
											d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<h3 className="text-sm font-medium text-red-800">Access Denied</h3>
									<div className="mt-2 text-sm text-red-700">
										<p>
											You don't have admin privileges to access this page. Your current role is:{" "}
											<strong>{userRole}</strong>
										</p>
										<p className="mt-2">
											Please contact an administrator to get admin access, or{" "}
											<a href="/setup" className="font-medium underline">
												visit the setup page
											</a>{" "}
											to configure admin access.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</main>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-100">
			{/* Navigation */}
			<nav className="bg-white shadow-sm">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between h-16">
						<div className="flex">
							<div className="flex-shrink-0 flex items-center">
								<a href="/admin/dashboard" className="text-xl font-bold text-gray-900 hover:text-gray-700">
									License Server
								</a>
							</div>
							<div className="hidden sm:ml-6 sm:flex sm:space-x-8">
								<a
									href="/admin/dashboard"
									className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
								>
									Dashboard
								</a>
								<a
									href="/admin/licenses"
									className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
								>
									Licenses
								</a>
								<a
									href="/admin/customers"
									className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
								>
									Customers
								</a>
							</div>
						</div>
						<div className="flex items-center">
							<span className="text-sm text-gray-700 mr-4">Welcome, {user.email}</span>
							<button
								onClick={async () => {
									await signOut();
									router.push("/login");
								}}
								className="ml-4 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
							>
								Sign Out
							</button>
						</div>
					</div>
				</div>
			</nav>

			{/* Main content */}
			<main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
				<div className="px-4 py-6 sm:px-0">
					<h1 className="text-2xl font-semibold text-gray-900 mb-6">{title}</h1>
					{children}
				</div>
			</main>
		</div>
	);
}
