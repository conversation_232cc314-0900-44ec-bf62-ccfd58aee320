"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase/client";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/components/auth/AuthProvider";

export default function LoginPage() {
	const [email, setEmail] = useState("");
	const [loading, setLoading] = useState(false);
	const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);
	const router = useRouter();
	const searchParams = useSearchParams();
	const { user } = useAuth();
	const redirectTo = searchParams.get("redirectTo") || "/admin/dashboard";

	// Redirect if already logged in
	useEffect(() => {
		if (user) {
			router.push(redirectTo);
		}
	}, [user, router, redirectTo]);

	// Check for error messages in URL
	useEffect(() => {
		const error = searchParams.get("error");
		if (error) {
			setMessage({ type: "error", text: decodeURIComponent(error) });
		}
	}, [searchParams]);

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);
		setMessage(null);

		try {
			const { error } = await supabase.auth.signInWithOtp({
				email,
				options: {
					emailRedirectTo: `${window.location.origin}/auth/callback?redirectTo=${encodeURIComponent(
						redirectTo
					)}`,
				},
			});

			if (error) {
				console.error("Login error:", error);
				setMessage({ type: "error", text: error.message });
			} else {
				setMessage({
					type: "success",
					text: "Check your email for the magic link to sign in!",
				});
			}
		} catch (error: any) {
			console.error("Unexpected error:", error);
			setMessage({
				type: "error",
				text: "An unexpected error occurred. Please try again.",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
						Sign in to your account
					</h2>
				</div>
				<form className="mt-8 space-y-6" onSubmit={handleLogin}>
					<div className="rounded-md shadow-sm -space-y-px">
						<div>
							<label htmlFor="email-address" className="sr-only">
								Email address
							</label>
							<input
								id="email-address"
								name="email"
								type="email"
								autoComplete="email"
								required
								className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
								placeholder="Email address"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
							/>
						</div>
					</div>

					{message && (
						<div
							className={`rounded-md p-4 ${
								message.type === "error" ? "bg-red-50 text-red-700" : "bg-green-50 text-green-700"
							}`}
						>
							<p className="text-sm">{message.text}</p>
						</div>
					)}

					<div>
						<button
							type="submit"
							disabled={loading}
							className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							{loading ? "Sending magic link..." : "Send magic link"}
						</button>
					</div>
				</form>
			</div>
		</div>
	);
}
