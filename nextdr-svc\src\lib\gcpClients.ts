import {
	InstancesClient,
	DisksClient,
	SnapshotsClient,
	NetworksClient,
	FirewallsClient,
	RoutesClient,
	SubnetworksClient,
	ZoneOperationsClient,
} from "@google-cloud/compute";
import { ProjectsClient } from "@google-cloud/resource-manager";
import { Storage } from "@google-cloud/storage";
import { supabase } from "../services/supabaseService";
import { GoogleAuth } from "google-auth-library";

export interface GCPClientSet {
	instancesClient: InstancesClient;
	disksClient: DisksClient;
	snapshotsClient: SnapshotsClient;
	projectsClient: ProjectsClient;
	storageClient: Storage;
	networksClient: NetworksClient;
	firewallsClient: FirewallsClient;
	routesClient: RoutesClient;
	subnetworksClient: SubnetworksClient;
	operationsClient: ZoneOperationsClient;
	authClient: GoogleAuth;
	projectId: string;
}

type GCPClientsCache = {
	[dataCenterId: string]: GCPClientSet;
};

let clientCache: GCPClientsCache = {};

export const GetGcpClients = async (dataCenterId: string): Promise<GCPClientSet> => {
	// Check if we already have a cached client for this datacenter
	if (clientCache[dataCenterId]) {
		return clientCache[dataCenterId];
	}

	// Handle the "default" datacenter ID case
	if (dataCenterId === "default") {
		console.log("Using mock client for default datacenter");
		return createMockClientSet();
	}

	try {
		// Try to fetch the datacenter details
		const { data: dataCenterDetails, error } = await supabase
			.from("datacenters2")
			.select("*")
			.eq("id", dataCenterId)
			.single();

		if (error) {
			console.error(`Error fetching datacenter details: ${error.message}`);
			throw new Error(`Failed to fetch datacenter details: ${error.message}`);
		}

		if (!dataCenterDetails) {
			console.warn(`No datacenter found with ID: ${dataCenterId}, using mock client`);
			return createMockClientSet();
		}

		let credentials;
		try {
			credentials =
				typeof dataCenterDetails.apitoken === "string"
					? JSON.parse(dataCenterDetails.apitoken)
					: dataCenterDetails.apitoken;
		} catch (e: any) {
			console.error(`Invalid credentials format: ${e.message}`);
			return createMockClientSet();
		}

		const clientConfig = {
			credentials: {
				client_email: credentials.client_email,
				private_key: credentials.private_key.replace(/\\n/g, "\n"),
				project_id: credentials.project_id,
			},
		};

		const instancesClient = new InstancesClient(clientConfig);
		const disksClient = new DisksClient(clientConfig);
		const routesClient = new RoutesClient(clientConfig);
		const snapshotsClient = new SnapshotsClient(clientConfig);
		const projectsClient = new ProjectsClient(clientConfig);
		const storageClient = new Storage(clientConfig);
		const networksClient = new NetworksClient(clientConfig);
		const firewallsClient = new FirewallsClient(clientConfig);
		const subnetworksClient = new SubnetworksClient(clientConfig);
		const operationsClient = new ZoneOperationsClient(clientConfig);
		const authClient = new GoogleAuth(clientConfig);
		const clientSet: GCPClientSet = {
			instancesClient,
			disksClient,
			routesClient,
			snapshotsClient,
			projectsClient,
			storageClient,
			networksClient,
			firewallsClient,
			subnetworksClient,
			operationsClient,
			authClient,
			projectId: credentials.project_id,
		};
		clientCache[dataCenterId] = clientSet;

		return clientSet;
	} catch (error: any) {
		console.error(`Unexpected error creating GCP clients: ${error.message}`);
		return createMockClientSet();
	}
};

/**
 * Create a mock client set for testing or when no datacenter is available
 * @returns A mock GCP client set
 */
const createMockClientSet = (): GCPClientSet => {
	console.log("Creating mock GCP client set");

	// Create mock implementations for all required clients
	const mockInstancesClient = {
		get: async () => ({ data: { name: "mock-instance" } }),
		list: async () => ({ data: [{ name: "mock-instance" }] }),
		// Add other methods as needed
	} as unknown as InstancesClient;

	const mockDisksClient = {
		get: async () => ({ data: { name: "mock-disk" } }),
		list: async () => ({ data: [{ name: "mock-disk" }] }),
		// Add other methods as needed
	} as unknown as DisksClient;

	const mockSnapshotsClient = {
		get: async () => ({ data: { name: "mock-snapshot" } }),
		list: async () => ({ data: [{ name: "mock-snapshot" }] }),
		// Add other methods as needed
	} as unknown as SnapshotsClient;

	const mockProjectsClient = {
		get: async () => ({ data: { name: "mock-project" } }),
		list: async () => ({ data: [{ name: "mock-project" }] }),
		// Add other methods as needed
	} as unknown as ProjectsClient;

	const mockStorageClient = {
		bucket: () => ({}),
		// Add other methods as needed
	} as unknown as Storage;

	const mockNetworksClient = {
		get: async () => ({ data: { name: "mock-network" } }),
		list: async () => ({ data: [{ name: "mock-network" }] }),
		// Add other methods as needed
	} as unknown as NetworksClient;

	const mockFirewallsClient = {
		get: async () => ({ data: { name: "mock-firewall" } }),
		list: async () => ({ data: [{ name: "mock-firewall" }] }),
		// Add other methods as needed
	} as unknown as FirewallsClient;

	const mockRoutesClient = {
		get: async () => ({ data: { name: "mock-route" } }),
		list: async () => ({ data: [{ name: "mock-route" }] }),
		// Add other methods as needed
	} as unknown as RoutesClient;

	const mockSubnetworksClient = {
		get: async () => ({ data: { name: "mock-subnetwork" } }),
		list: async () => ({ data: [{ name: "mock-subnetwork" }] }),
		// Add other methods as needed
	} as unknown as SubnetworksClient;

	const mockOperationsClient = {
		get: async () => ({ data: { name: "mock-operation" } }),
		list: async () => ({ data: [{ name: "mock-operation" }] }),
		// Add other methods as needed
	} as unknown as ZoneOperationsClient;

	const mockAuthClient = {
		getClient: async () => ({}),
		// Add other methods as needed
	} as unknown as GoogleAuth;

	// Create the mock client set
	const mockClientSet: GCPClientSet = {
		instancesClient: mockInstancesClient,
		disksClient: mockDisksClient,
		snapshotsClient: mockSnapshotsClient,
		projectsClient: mockProjectsClient,
		storageClient: mockStorageClient,
		networksClient: mockNetworksClient,
		firewallsClient: mockFirewallsClient,
		routesClient: mockRoutesClient,
		subnetworksClient: mockSubnetworksClient,
		operationsClient: mockOperationsClient,
		authClient: mockAuthClient,
		projectId: "mock-project-id",
	};

	// Cache the mock client set
	clientCache["default"] = mockClientSet;

	return mockClientSet;
};
