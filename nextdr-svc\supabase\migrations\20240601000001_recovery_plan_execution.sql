-- Create recovery_plan_progress table
CREATE TABLE IF NOT EXISTS recovery_plan_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recovery_plan_id UUID NOT NULL REFERENCES recovery_plans_new(id) ON DELETE CASCADE,
  step_id UUID NOT NULL REFERENCES recovery_steps_new(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'awaiting_approval', 'approved', 'rejected')),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  execution_metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(recovery_plan_id, step_id)
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_recovery_plan_progress_plan ON recovery_plan_progress(recovery_plan_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_progress_step ON recovery_plan_progress(step_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_progress_status ON recovery_plan_progress(status);

-- Create recovery_plan_checkpoints table
CREATE TABLE IF NOT EXISTS recovery_plan_checkpoints (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recovery_plan_id UUID NOT NULL REFERENCES recovery_plans_new(id) ON DELETE CASCADE,
  step_id UUID NOT NULL REFERENCES recovery_steps_new(id) ON DELETE CASCADE,
  approver_id UUID REFERENCES user_profiles(id),
  approver_role TEXT,
  approval_required BOOLEAN NOT NULL DEFAULT true,
  approval_status TEXT CHECK (approval_status IN ('pending', 'approved', 'rejected')),
  approved_at TIMESTAMPTZ,
  approved_by UUID REFERENCES user_profiles(id),
  approval_metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(recovery_plan_id, step_id),
  CHECK ((approver_id IS NOT NULL AND approver_role IS NULL) OR (approver_id IS NULL AND approver_role IS NOT NULL))
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_recovery_plan_checkpoints_plan ON recovery_plan_checkpoints(recovery_plan_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_checkpoints_step ON recovery_plan_checkpoints(step_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_checkpoints_approver ON recovery_plan_checkpoints(approver_id);
CREATE INDEX IF NOT EXISTS idx_recovery_plan_checkpoints_role ON recovery_plan_checkpoints(approver_role);

-- Update approval_tokens table
ALTER TABLE IF EXISTS approval_tokens
ADD COLUMN IF NOT EXISTS is_used BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS used_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS used_by UUID REFERENCES user_profiles(id),
ADD COLUMN IF NOT EXISTS is_checkpoint BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS checkpoint_id UUID REFERENCES recovery_plan_checkpoints(id) ON DELETE CASCADE;

-- Create index for checkpoint lookups
CREATE INDEX IF NOT EXISTS idx_approval_tokens_checkpoint ON approval_tokens(checkpoint_id);

-- Update recovery_plans_new table
ALTER TABLE IF EXISTS recovery_plans_new
ADD COLUMN IF NOT EXISTS current_execution_id UUID,
ADD COLUMN IF NOT EXISTS execution_status TEXT CHECK (execution_status IN ('not_started', 'in_progress', 'paused', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS execution_started_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS execution_completed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS execution_metadata JSONB DEFAULT '{}'::jsonb;

-- Enable RLS on new tables
ALTER TABLE recovery_plan_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE recovery_plan_checkpoints ENABLE ROW LEVEL SECURITY;