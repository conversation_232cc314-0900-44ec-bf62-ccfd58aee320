import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Settings, Refresh<PERSON><PERSON>, <PERSON>ertCircle, CheckCircle2 } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import PageLayout from "@/components/layout/PageLayout";
import { useAuth } from "@/lib/context/AuthContext";
import { format } from "date-fns";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { IntegrationConfig, SyncStats } from "@/types/integrations";
import {
	useIntegrationConfig,
	useUpdateIntegrationConfig,
	useTriggerSync,
	useSyncStats,
} from "@/lib/api/hooks/integrations";

const IntegrationsPage = () => {
	const { isAdmin, isLoading: isLoadingAdmin } = useIsAdmin();
	const [isEditing, setIsEditing] = useState(false);
	const [config, setConfig] = useState<Partial<IntegrationConfig["config"]>>({
		service_account_key: "",
		domain: "",
		sync_interval_minutes: 15,
	});

	// Use the new API hooks
	const { data: integrationConfig, isLoading: isLoadingConfig } = useIntegrationConfig();
	const { data: syncStats, isLoading: isLoadingStats } = useSyncStats();
	const updateConfigMutation = useUpdateIntegrationConfig();
	const triggerSyncMutation = useTriggerSync();

	// Update local config state when integration config loads
	useEffect(() => {
		if (integrationConfig?.config) {
			setConfig(integrationConfig.config);
		}
	}, [integrationConfig]);

	const handleUpdateConfig = (updates: Partial<IntegrationConfig>) => {
		updateConfigMutation.mutate(updates, {
			onSuccess: () => {
				setIsEditing(false);
			},
		});
	};

	const handleTriggerSync = () => {
		triggerSyncMutation.mutate();
	};

	if (isLoadingAdmin) {
		return (
			<PageLayout title="Integrations">
				<div className="space-y-4">
					<Skeleton className="h-8 w-full" />
					<Skeleton className="h-8 w-full" />
					<Skeleton className="h-8 w-full" />
				</div>
			</PageLayout>
		);
	}

	if (!isAdmin) {
		return (
			<PageLayout title="Integrations">
				<div className="flex h-[50vh] items-center justify-center">
					<p className="text-muted-foreground">Access denied. Admin privileges required.</p>
				</div>
			</PageLayout>
		);
	}

	return (
		<PageLayout title="Integrations">
			<div className="space-y-6">
				<div className="flex justify-between items-center">
					<div className="flex items-center gap-3">
						<Settings className="h-6 w-6 text-blue-500" />
						<h2 className="text-xl font-semibold">GCP AD Integration</h2>
						<Badge variant={integrationConfig?.enabled ? "default" : "secondary"}>
							{integrationConfig?.enabled ? "Enabled" : "Disabled"}
						</Badge>
					</div>
					<div className="flex gap-2">
						<Button
							variant="outline"
							onClick={() => setIsEditing(!isEditing)}
							disabled={updateConfigMutation.isPending}
						>
							{isEditing ? "Cancel" : "Edit Configuration"}
						</Button>
						<Button
							onClick={handleTriggerSync}
							disabled={!integrationConfig?.enabled || triggerSyncMutation.isPending}
						>
							<RefreshCw className="h-4 w-4 mr-2" />
							{triggerSyncMutation.isPending ? "Syncing..." : "Trigger Sync"}
						</Button>
					</div>
				</div>

				<div className="grid gap-6 md:grid-cols-2">
					<Card>
						<CardHeader>
							<CardTitle>Configuration</CardTitle>
						</CardHeader>
						<CardContent>
							{isLoadingConfig ? (
								<div className="space-y-4">
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
								</div>
							) : (
								<div className="space-y-4">
									<div className="flex items-center justify-between">
										<Label htmlFor="enabled">Enable Integration</Label>
										<Switch
											id="enabled"
											checked={integrationConfig?.enabled}
											onCheckedChange={(checked) => {
												// When enabling, include the current config
												const updateData: Partial<IntegrationConfig> = { enabled: checked };
												if (checked && integrationConfig?.config) {
													updateData.config = integrationConfig.config;
												}
												handleUpdateConfig(updateData);
											}}
										/>
									</div>

									{isEditing ? (
										<>
											<div className="space-y-2">
												<Label htmlFor="service_account_key">GCP Service Account Key (JSON)</Label>
												<Textarea
													id="service_account_key"
													value={config.service_account_key}
													onChange={(e) =>
														setConfig({ ...config, service_account_key: e.target.value })
													}
													placeholder="Paste your GCP service account JSON key here..."
													rows={8}
													className="font-mono text-sm"
												/>
												<p className="text-xs text-muted-foreground">
													Paste the entire JSON content from your GCP service account key file
												</p>
											</div>
											<div className="space-y-2">
												<Label htmlFor="domain">Domain</Label>
												<Input
													id="domain"
													value={config.domain}
													onChange={(e) => setConfig({ ...config, domain: e.target.value })}
													placeholder="Enter your organization's domain (e.g., company.com)"
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="sync_interval">Sync Interval (minutes)</Label>
												<Input
													id="sync_interval"
													type="number"
													value={config.sync_interval_minutes}
													onChange={(e) =>
														setConfig({
															...config,
															sync_interval_minutes: parseInt(e.target.value),
														})
													}
													min={1}
													max={60}
												/>
											</div>
											<Button
												className="w-full"
												onClick={() =>
													handleUpdateConfig({ config: config as IntegrationConfig["config"] })
												}
												disabled={updateConfigMutation.isPending}
											>
												{updateConfigMutation.isPending ? "Saving..." : "Save Configuration"}
											</Button>
										</>
									) : (
										<div className="space-y-2 text-sm">
											<p>
												<span className="font-medium">Service Account:</span>{" "}
												{integrationConfig?.config.service_account_key ? "Configured" : "Not set"}
											</p>
											<p>
												<span className="font-medium">Domain:</span>{" "}
												{integrationConfig?.config.domain || "Not set"}
											</p>
											<p>
												<span className="font-medium">Sync Interval:</span>{" "}
												{integrationConfig?.config.sync_interval_minutes || 15} minutes
											</p>
										</div>
									)}
								</div>
							)}
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Sync Status</CardTitle>
						</CardHeader>
						<CardContent>
							{isLoadingStats ? (
								<div className="space-y-4">
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
									<Skeleton className="h-8 w-full" />
								</div>
							) : (
								<div className="space-y-4">
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium">Sync Status</span>
										<Badge
											variant={
												integrationConfig?.sync_status === "success"
													? "default"
													: integrationConfig?.sync_status === "in_progress"
													? "secondary"
													: integrationConfig?.sync_status === "error"
													? "destructive"
													: "outline"
											}
										>
											{integrationConfig?.sync_status || "idle"}
										</Badge>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium">Last Sync</span>
										<span className="text-sm text-muted-foreground">
											{syncStats?.last_sync_at
												? format(new Date(syncStats.last_sync_at), "MMM d, yyyy HH:mm:ss")
												: "Never"}
										</span>
									</div>
									<div className="grid grid-cols-2 gap-4">
										<div className="space-y-1">
											<p className="text-sm font-medium">Users</p>
											<div className="flex items-center gap-2">
												<span className="text-2xl font-bold">{syncStats?.synced_users || 0}</span>
												<span className="text-sm text-muted-foreground">
													of {syncStats?.total_users || 0}
												</span>
											</div>
										</div>
										<div className="space-y-1">
											<p className="text-sm font-medium">Groups</p>
											<div className="flex items-center gap-2">
												<span className="text-2xl font-bold">{syncStats?.synced_groups || 0}</span>
												<span className="text-sm text-muted-foreground">
													of {syncStats?.total_groups || 0}
												</span>
											</div>
										</div>
									</div>
									{syncStats?.conflicts ? (
										<div className="flex items-center gap-2 text-yellow-500">
											<AlertCircle className="h-4 w-4" />
											<span className="text-sm">{syncStats.conflicts} conflicts detected</span>
										</div>
									) : (
										<div className="flex items-center gap-2 text-green-500">
											<CheckCircle2 className="h-4 w-4" />
											<span className="text-sm">No conflicts</span>
										</div>
									)}
								</div>
							)}
						</CardContent>
					</Card>
				</div>
			</div>
		</PageLayout>
	);
};

export default IntegrationsPage;
