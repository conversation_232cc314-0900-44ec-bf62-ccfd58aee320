export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
	public: {
		Tables: {
			user_profiles: {
				Row: {
					id: string;
					email: string;
					role: "admin" | "user";
					status: "active" | "invited" | "suspended";
					created_at: string;
					updated_at: string;
					last_sign_in_at: string | null;
					metadata: Json;
				};
				Insert: {
					id: string;
					email: string;
					role?: "admin" | "user";
					status?: "active" | "invited" | "suspended";
					created_at?: string;
					updated_at?: string;
					last_sign_in_at?: string | null;
					metadata?: Json;
				};
				Update: {
					id?: string;
					email?: string;
					role?: "admin" | "user";
					status?: "active" | "invited" | "suspended";
					created_at?: string;
					updated_at?: string;
					last_sign_in_at?: string | null;
					metadata?: Json;
				};
			};
			audit_logs: {
				Row: {
					id: string;
					user_id: string | null;
					action: string;
					entity_type: string;
					entity_id: string | null;
					details: Json;
					created_at: string;
					ip_address: string | null;
					user_agent: string | null;
					retention_period: string;
					is_immutable: boolean;
				};
				Insert: {
					id?: string;
					user_id?: string | null;
					action: string;
					entity_type: string;
					entity_id?: string | null;
					details?: Json;
					created_at?: string;
					ip_address?: string | null;
					user_agent?: string | null;
					retention_period?: string;
					is_immutable?: boolean;
				};
				Update: {
					id?: string;
					user_id?: string | null;
					action?: string;
					entity_type?: string;
					entity_id?: string | null;
					details?: Json;
					created_at?: string;
					ip_address?: string | null;
					user_agent?: string | null;
					retention_period?: string;
					is_immutable?: boolean;
				};
			};
			internal_groups: {
				Row: {
					id: string;
					name: string;
					description: string;
					type: "internal";
					created_at: string;
					updated_at: string;
					created_by: string;
				};
				Insert: {
					id?: string;
					name: string;
					description: string;
					type: "internal";
					created_at?: string;
					updated_at?: string;
					created_by: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string;
					type?: "internal";
					created_at?: string;
					updated_at?: string;
					created_by?: string;
				};
			};
			group_members: {
				Row: {
					id: string;
					group_id: string;
					user_id: string;
					created_at: string;
					created_by: string;
				};
				Insert: {
					id?: string;
					group_id: string;
					user_id: string;
					created_at?: string;
					created_by: string;
				};
				Update: {
					id?: string;
					group_id?: string;
					user_id?: string;
					created_at?: string;
					created_by?: string;
				};
			};
		};
		Views: {
			[_ in never]: never;
		};
		Functions: {
			update_user_role: {
				Args: {
					user_id: string;
					new_role: string;
				};
				Returns: void;
			};
			log_user_action: {
				Args: {
					action: string;
					entity_id: string;
					details?: Json;
					ip_address?: string;
					user_agent?: string;
				};
				Returns: string;
			};
			export_audit_logs: {
				Args: {
					start_date?: string;
					end_date?: string;
					action_filter?: string;
					entity_type_filter?: string;
				};
				Returns: {
					id: string;
					timestamp: string;
					actor_email: string;
					action: string;
					entity_type: string;
					entity_id: string;
					details: Json;
					ip_address: string;
					user_agent: string;
				}[];
			};
		};
		Enums: {
			[_ in never]: never;
		};
	};
}
