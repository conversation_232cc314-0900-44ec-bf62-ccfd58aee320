-- Add unique constraint on source column for integration_configs table
-- This is required for upsert operations with onConflict: "source"

-- First, remove any duplicate records if they exist
DELETE FROM integration_configs a USING integration_configs b 
WHERE a.id > b.id AND a.source = b.source;

-- Add unique constraint on source column
ALTER TABLE integration_configs 
ADD CONSTRAINT integration_configs_source_unique UNIQUE (source);

-- Also add unique constraint to sync_stats for consistency
DELETE FROM sync_stats a USING sync_stats b 
WHERE a.id > b.id AND a.source = b.source;

ALTER TABLE sync_stats 
ADD CONSTRAINT sync_stats_source_unique UNIQUE (source);
