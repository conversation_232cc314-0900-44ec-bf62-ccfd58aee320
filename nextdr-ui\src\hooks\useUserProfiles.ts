import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";
import { toast } from "sonner";
import { UserRole } from "@/types/rbac";
import { UserProfile } from "@/types/integrations";

export function useUserProfiles() {
	const queryClient = useQueryClient();

	const {
		data: profiles,
		isLoading,
		error,
	} = useQuery({
		queryKey: ["userProfiles"],
		queryFn: async () => {
			const { data: users, error: usersError } = await supabase
				.from("user_profiles")
				.select("*")
				.order("created_at", { ascending: false });

			if (usersError) throw usersError;

			const { data: memberships, error: membershipsError } = await supabase.from("group_members")
				.select(`
					user_id,
					internal_groups!inner(
						id,
						name
					)
				`);

			if (membershipsError) throw membershipsError;

			const transformedData = users.map((user) => ({
				...user,
				groups:
					memberships?.filter((m) => m.user_id === user.id).map((m) => m.internal_groups) || [],
			}));

			return transformedData as UserProfile[];
		},
	});

	const inviteUser = useMutation({
		mutationFn: async ({ email, role }: { email: string; role: UserRole }) => {
			const {
				data: { user },
				error: inviteError,
			} = await supabase.auth.admin.inviteUserByEmail(email);
			if (inviteError) throw inviteError;

			if (user) {
				const { error: profileError } = await supabase
					.from("user_profiles")
					.update({ role, status: "invited", source: "native" })
					.eq("id", user.id);

				if (profileError) throw profileError;
			}

			return user;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success("User invited successfully");
		},
		onError: (error) => {
			toast.error("Failed to invite user: " + error.message);
		},
	});

	const updateUserRole = useMutation({
		mutationFn: async ({ userId, role }: { userId: string; role: UserRole }) => {
			const { error } = await supabase.rpc("update_user_role", {
				user_id: userId,
				new_role: role,
			});

			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success("User role updated successfully");
		},
		onError: (error) => {
			toast.error("Failed to update user role: " + error.message);
		},
	});

	const removeUser = useMutation({
		mutationFn: async (userId: string) => {
			const { error } = await supabase.auth.admin.deleteUser(userId);
			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success("User removed successfully");
		},
		onError: (error) => {
			toast.error("Failed to remove user: " + error.message);
		},
	});

	const suspendUser = useMutation({
		mutationFn: async (userId: string) => {
			const { error } = await supabase
				.from("user_profiles")
				.update({ status: "suspended" })
				.eq("id", userId);

			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success("User suspended successfully");
		},
		onError: (error) => {
			toast.error("Failed to suspend user: " + error.message);
		},
	});

	const activateUser = useMutation({
		mutationFn: async (userId: string) => {
			const { error } = await supabase
				.from("user_profiles")
				.update({ status: "active" })
				.eq("id", userId);

			if (error) throw error;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success("User activated successfully");
		},
		onError: (error) => {
			toast.error("Failed to activate user: " + error.message);
		},
	});

	return {
		profiles,
		isLoading,
		error,
		inviteUser,
		updateUserRole,
		removeUser,
		suspendUser,
		activateUser,
	};
}
