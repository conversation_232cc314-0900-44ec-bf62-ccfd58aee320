#!/bin/bash
# ndrRegistry.sh - A Bash module providing common registry related functions

source "$(dirname "${BASH_SOURCE[0]}")/ndrCommon.sh"

# --- FUNCTIONS ---

# Create registry with version, date, home dir and optional module name
function ndr_RegistryCreate() 
{
  local logSectionDesc="Creating $gCOMPANY_NAME Application Registry"
  ndr_logSecStart "$logSectionDesc"

  if [[ $# -lt 3 ]]; then
    ndr_logError "Usage: ndr_RegistryCreate <version> <home_dir> [optional: <module_name>]"
    return 1
  fi

  local version="$1"
  local home_dir="$2"
  local module_name="${3:-}"
  local install_date
  install_date=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  
  if [[ -z "$version" || -z "$home_dir" ]]; then
    ndr_logError "Version and home directory must be provided."
    return 1
  fi

  # if registry does not exist, create initial registry file
  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logInfo "Creating initial registry at [$gREGISTRY_FILE]"

    if [[ ! -d "$gREGISTRY_DIR" ]]; then
      mkdir -p "$gREGISTRY_DIR"
      ndr_logInfo "Creating initial registry directory [$gREGISTRY_DIR]."
    fi

    # create empty file with empty json object
    echo '{}' > "$gREGISTRY_FILE"

    ndr_RegistryAddKey "$gREGISTRY_ENTRY_VERSION" "$version"
    ndr_RegistryAddKey "$gREGISTRY_ENTRY_INSTALL_DATE" "$install_date"
    ndr_RegistryAddKey "$gREGISTRY_ENTRY_HOME_DIR" "$home_dir"
  
    # seed soem initial module status entries
    ndr_RegistryAddKey "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
    ndr_RegistryAddKey "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
    ndr_RegistryAddKey "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME" "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"


#    cat > "$gREGISTRY_FILE" <<EOF
#{
#  "$gREGISTRY_ENTRY_VERSION": "$version",
#  "$gREGISTRY_ENTRY_INSTALL_DATE": "$install_date",
#  "$gREGISTRY_ENTRY_HOME_DIR": "$home_dir",
#  "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME": "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED",
#  "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME": "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED",
#  "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME": "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED"
#}
#EOF
  else
    ndr_logInfo "Registry already exists at [$gREGISTRY_FILE]. Updating existing registry."
  fi

  ndr_RegistryAddKey "$gREGISTRY_ENTRY_UPDATE_DATE" "$install_date"
  
  # if optional module name is provided, update the registry with the module status
  if [[ -n "$module_name" ]]; then
    ndr_RegistryAddKey "$module_name" "$gREGISTRY_ENTRY_MODULE_STATUS_INSTALLED"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to update module status [$module_name -> $gREGISTRY_ENTRY_MODULE_STATUS_INSTALLED] in registry."
      return 1
    fi
  fi

  ndr_logInfo "Registry created at $gREGISTRY_FILE"

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Add/Update a key in the registry
function ndr_RegistryAddKey () 
{
  local key="$1"
  local value="$2"

  if [[ $# -ne 2 ]]; then
    ndr_logWarn "Usage: ndr_RegistryAddKey <key> <value>"
    return 1
  fi

  if [[ -z "$key" || -z "$value" ]]; then
    ndr_logError "Key and value must be provided."
    return 1
  fi

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logWarn "Registry not found at $gREGISTRY_FILE"
    return 1
  fi

  tmp_file=$(mktemp)
    
  if jq -e --arg k "$key" '.[$k]' "$gREGISTRY_FILE" > /dev/null; then
    # key already exists, update it
    jq --arg key "$key" --arg value "$value" '.[$key] = $value' "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to update $key to $value in registry"
      return 1
    fi
    ndr_logInfo "Updated $key to $value in registry"
  else
    # key does not exist, add it
    jq --arg k "$key" --arg v "$value" '. + {($k): $v}' "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to add $key with $value to registry"
      return 1
    fi
    ndr_logInfo "Added $key with $value to registry"
  fi

  return 0
}

# Retrieve a value by key and prints value to stdout upon success
function ndr_RegistryQueryKey() 
{
  if [[ $# -ne 1 ]]; then
    ndr_logError "Usage: ndr_RegistryQueryKey <key>"
    return 1
  fi

  local key="$1"

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logWarn "Registry not found at $gREGISTRY_FILE"
    return 1
  fi

  if [[ -z "$key" ]]; then
    ndr_logError "Key must be provided."
    return 1
  fi

  local value
  #jq -r --arg key "$key" '.[$key] // "Key not found"' "$gREGISTRY_FILE"
  value=$(jq -r --arg key "$key" '.[$key] // empty' "$gREGISTRY_FILE")
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to query $key in registry"
    return 1
  fi

  echo "$value" # print to stdout
  return 0 # signals success
}

# Remove a specific key
function ndr_RegistryDeleteKey() 
{
  if [[ $# -ne 1 ]]; then
    ndr_logError "Usage: ndr_RegistryDeleteKey <key>"
    return 1
  fi

  local key="$1"

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logWarn "Registry not found at $gREGISTRY_FILE"
    return 1
  fi

  if [[ -z "$key" ]]; then
    ndr_logError "Key must be provided."
    return 1
  fi

  tmp_file=$(mktemp)
  jq --arg key "$key" 'del(.[$key])' "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to delete $key in registry"
    return 1
  fi

  ndr_logInfo "Deleted key $key from registry"

  return 0
}

# add a section array to the registry
function ndr_RegistryAddKeySectionArray() 
{
  local key="$1"
  shift
  local values=("$@")

  if [[ $# -lt 2 ]]; then
    ndr_logWarn "Usage: ndr_RegistryAddKeySectionArray <key> <value1> [value2 ...]"
    return 1
  fi

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logWarn "Registry not found at $gREGISTRY_FILE"
    return 1
  fi

  # Convert array to JSON array
  local jq_array
  jq_array=$(printf '%s\n' "${values[@]}" | jq -R . | jq -s .)

  tmp_file=$(mktemp)
  jq --argjson arr "$jq_array" --arg key "$key" '.[$key] = $arr' "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to add/udpdate array '$key' in registry"
    return 1
  fi

  ndr_logInfo "Added/updated array '$key' in registry."

  return 0
}

function ndr_RegistryQueryKeySectionArray () 
{
  local key="$1"

  if [[ $# -ne 1 ]]; then
    ndr_logWarn "Usage: ndr_RegistryQueryKeySectionArray <key>"
    return 1
  fi

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logError "Registry file not found: $gREGISTRY_FILE"
    return 1
  fi

  # Check if key exists and is an array
  if ! jq -e --arg key "$key" '(.[$key] | type == "array")' "$gREGISTRY_FILE" > /dev/null; then
    ndr_logWarn "Key '$key' not found or is not an array."
    return 1
  fi

  # Return the array items space-separated
  jq -r --arg key "$key" '.[$key][]' "$gREGISTRY_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to query array '$key' in registry"
    return 1
  fi

  return 0
}

function ndr_RegistryAddKeyServiceEntry () 
{
  # Argument validation
  if [[ $# -ne 4 ]]; then
    ndr_logError "Usage: ndr_RegistryAddKeyServiceEntry <service> <container> <image> <tag>"
    return 1
  fi
  
  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logError "Registry file not found: $gREGISTRY_FILE"
    return 1
  fi

  local service="$1"
  local container="$2"
  local image="$3"
  local tag="$4"
  
  if [[ -z "$service" || -z "$container" || -z "$image" || -z "$tag" ]]; then
    ndr_logError "All parameters (service, container, image, tag) must be provided."
    return 1
  fi
  
  # Build the new service object
  local tmp_file
  tmp_file=$(mktemp)

  # Remove existing entry for this service (if exists)
  jq --arg svc "$service" \
    'if .supabase_docker_services then .supabase_docker_services |= map(select(.service != $svc)) else . end' \
    "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to remove existing service entry for $service"
    return 1
  fi

  # Append the new object
  jq --arg svc "$service" \
     --arg cont "$container" \
     --arg img "$image" \
     --arg tag "$tag" \
     '.supabase_docker_services += [{"service": $svc, "container": $cont, "image": $img, "tag": $tag}]' \
     "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"
  return_code=$?
  if [ $return_code != 0 ]; then
    ndr_logError "Failed to add service entry for $service"
    return 1
  fi

  return 0
}

function ndr_RegistryAddKeyServiceEntries ()
{
  local logSectionDesc="Adding Supabase Service Entries to Registry"
  ndr_logSecStart "$logSectionDesc"  

  if [ ${#ndr_supabase_container_services[@]} -eq 0 ]; then
    ndr_logInfo "No Supabase service array entries found."
    return 0
  fi

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logError "Registry file not found: $gREGISTRY_FILE"
    return 1
  fi

  ndr_logInfo "Adding ${#ndr_supabase_container_services[@]} service entries to the registry."

  for service in "${ndr_supabase_container_services[@]}"; do
  
    #local service="${ndr_supabase_container_services[$service]}"
    local container_name="${ndr_supabase_container_service_container_names[$service]}"
    local image_name="${ndr_supabase_container_service_image_names[$service]}"
    local image_tag="${ndr_supabase_container_service_image_tags[$service]}"

    #ndr_logInfo "Adding service entry for $service: $container_name ($image_name:$image_tag)"

    ndr_RegistryAddKeyServiceEntry "$service" "$container_name" "$image_name" "$image_tag"
    return_code=$?
    if [ $return_code != 0 ]; then
      ndr_logError "Failed to add service entry for $service: $container_name ($image_name:$image_tag)"
      return 1
    fi

    ndr_logInfo "Added service entry for $service: $container_name ($image_name:$image_tag)"
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

# Example service list registry file structure:
#{
#  "supabase_docker_services": [
#    {
#      "service": "svc1",
#      "container": "container1",
#      "image": "repo/image1",
#      "tag": "1.0"
#    },
#    {
#      "service": "svc2",
#      "container": "container2",
#      "image": "repo/image2",
#      "tag": "2.0"
#    }
#  ]
#}

function ndr_RegistryReadServiceEntries () 
{
  local logSectionDesc="Reading Supabase Service Entries from Registry"
  ndr_logSecStart "$logSectionDesc"  

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logError "Registry file not found: $gREGISTRY_FILE"
    return 1
  fi

  # clear the arrays
  ndr_supabase_container_services=()
  ndr_supabase_container_service_container_names=()
  ndr_supabase_container_service_image_names=()
  ndr_supabase_container_service_image_tags=()

  mapfile -t ndr_supabase_container_services < <(jq -r '.supabase_docker_services[].service' "$gREGISTRY_FILE")
  return_code=$?
  if [[ $return_code -ne 0 ]]; then
    ndr_logError "Failed to parse services from $gREGISTRY_FILE"
    return 1
  fi

  for service in "${ndr_supabase_container_services[@]}"; do
    local container image tag
    container=$(jq -r --arg svc "$service" '.supabase_docker_services[] | select(.service == $svc) | .container' "$gREGISTRY_FILE")
    image=$(jq -r --arg svc "$service" '.supabase_docker_services[] | select(.service == $svc) | .image' "$gREGISTRY_FILE")
    tag=$(jq -r --arg svc "$service" '.supabase_docker_services[] | select(.service == $svc) | .tag' "$gREGISTRY_FILE")

    # Save to global associative arrays keyed by service name
    ndr_supabase_container_service_container_names["$service"]="$container"
    ndr_supabase_container_service_image_names["$service"]="$image"
    ndr_supabase_container_service_image_tags["$service"]="$tag"
    echo "Service: $service"
    echo "  Container: $container"
    echo "  Image:     $image"
    echo "  Tag:       $tag"
    echo ""
  done

  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_RegistryRemoveServiceEntries () 
{
  local logSectionDesc="Removing Supabase Service Entries from Registry"
  ndr_logSecStart "$logSectionDesc"  

  if [[ ! -f "$gREGISTRY_FILE" ]]; then
    ndr_logError "Registry file not found: $gREGISTRY_FILE"
    return 1
  fi

  # Remove the '' section
  tmp_file=$(mktemp)
  jq 'del(.supabase_docker_services)' "$gREGISTRY_FILE" > "$tmp_file" && mv "$tmp_file" "$gREGISTRY_FILE"

  if [ $? -eq 0 ]; then
    echo "Supabase services section removed successfully."
  else
    echo "Failed to remove Supabase services section."
    return 1
  fi

  ndr_logSecEnd "$logSectionDesc"

  return 0
}


# Remove the entire registry mechanism
function ndr_RegistryUninstall() 
{
  local logSectionDesc="Removing $gCOMPANY_NAME Application Registry"
  ndr_logSecStart "$logSectionDesc"

  if [[ ! -d "$gREGISTRY_DIR" ]]; then
    ndr_logWarn "Registry directory not found: $gREGISTRY_DIR"
    return 0
  fi

  local supabase_status service_status ui_status

  supabase_status=$(jq -r '.module_status_supabase // "not_installed"' "$gREGISTRY_FILE")
  service_status=$(jq -r '.module_status_service // "not_installed"' "$gREGISTRY_FILE")
  ui_status=$(jq -r '.module_status_ui // "not_installed"' "$gREGISTRY_FILE")

  if [[ "$supabase_status" != "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED" || \
        "$service_status" != "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED" || \
        "$ui_status" != "$gREGISTRY_ENTRY_MODULE_STATUS_NOT_INSTALLED" ]]; then
    ndr_logInfo "Cannot uninstall registry: One or more modules are still marked as installed."
    ndr_logInfo "$gREGISTRY_ENTRY_MODULE_STATUS_SUPABASE_NAME = $supabase_status"
    ndr_logInfo "$gREGISTRY_ENTRY_MODULE_STATUS_SERVICE_NAME  = $service_status"
    ndr_logInfo "$gREGISTRY_ENTRY_MODULE_STATUS_UI_NAME       = $ui_status"
    return 2
  fi

  rm -rf "$gREGISTRY_DIR"
  ndr_logInfo "Registry uninstalled: $gREGISTRY_DIR removed."
  
  ndr_logSecEnd "$logSectionDesc"

  return 0
}

function ndr_isModuleInstalledReg () 
{
  if [[ $# -ne 1 ]]; then
    ndr_logError "Usage: ndr_isModuleInstalledReg <module_key>"
    return 1
  fi

  local key="$1"
  local regValue=""

  if [[ -z "$key" ]]; then
    ndr_logError "Key must be provided."
    return 1
  fi

  regValue=$(ndr_RegistryQueryKey "$key")
  return_code=$?
  if [[ $return_code -eq 0 && "$regValue" == "$gREGISTRY_ENTRY_MODULE_STATUS_INSTALLED" ]]; then
    return 0
  fi

  return 1
}

function ndr_getHomeDirReg () 
{
  local key="$gREGISTRY_ENTRY_HOME_DIR"
  local regValue=""

  regValue=$(ndr_RegistryQueryKey "$key")
  return_code=$?
  if [[ $return_code -eq 0 && -n "$regValue" ]]; then
    echo "$regValue"
    return 0
  fi

  ndr_logError "Failed to retrieve home directory from registry."

  return 1
}

function ndr_getVersionReg () 
{
  local key="$gREGISTRY_ENTRY_VERSION"
  local regValue=""

  regValue=$(ndr_RegistryQueryKey "$key")
  return_code=$?
  if [[ $return_code -eq 0 && -n "$regValue" ]]; then
    echo "$regValue"
    return 0
  fi

  ndr_logError "Failed to retrieve version from registry."

  return 1
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  ndr_logWarn "This is a bash module--there is no direct execution capabilities in this file."
  exit 0
fi
