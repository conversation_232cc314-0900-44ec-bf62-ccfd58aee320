import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { InstancesClient } from "@google-cloud/compute";
import { VMConfig } from "../models/vmConfig";
import { saveVMConfig } from "../services/supabaseService";
import { GetGcpClients } from "../lib/gcpClients";

export const getVMConfig: RequestHandler = async (req, res) => {
	try {
		const { project_id, vm_name, datacenterId } = req.query;

		if (!project_id || !vm_name || !datacenterId) {
			res.status(400).json({
				error: "All of project_id, vm_name, and datacenterId are required query parameters",
			});
			return;
		}

		const clients = await GetGcpClients(datacenterId as string);
		const instancesClient = clients.instancesClient;

		const [instance] = await instancesClient.get({
			project: project_id as string,
			zone: "us-central1-c",
			instance: vm_name as string,
		});

		const vmConfig: VMConfig = {
			name: instance.name ?? "",
			id: String(instance.id ?? ""),
			tags: instance.tags?.items ?? [],
			machine_type: instance.machineType?.split("/").pop() ?? "",
			zone: instance.zone?.split("/").pop() ?? "",
			status: instance.status ?? "",
			network_interfaces:
				instance.networkInterfaces?.map((ni) => ({
					network: ni.network?.split("/").pop() ?? "",
					subnetwork: ni.subnetwork?.split("/").pop() ?? "",
					network_ip: ni.networkIP ?? "",
					access_configs: ni.accessConfigs ?? [],
				})) ?? [],
			disks:
				instance.disks?.map((disk) => ({
					type: disk.type ?? "",
					mode: disk.mode ?? "",
					source: disk.source?.split("/").pop() ?? "",
					device_name: disk.deviceName ?? "",
				})) ?? [],
		};

		// Save to Supabase
		// await saveVMConfig(vmConfig);

		res.status(200).json(vmConfig);
		return;
	} catch (error: any) {
		console.error("Error fetching VM configuration:", error);
		res.status(500).json({ error: error.message });
		return;
	}
};
