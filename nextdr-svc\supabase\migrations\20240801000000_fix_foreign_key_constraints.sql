-- Migration to fix foreign key constraints for recovery_steps_new and related tables

-- 1. First, drop the existing constraint on approval_tokens
ALTER TABLE approval_tokens
DROP CONSTRAINT IF EXISTS approval_tokens_step_id_fkey;

-- 2. Recreate the constraint with ON DELETE CASCADE
ALTER TABLE approval_tokens
ADD CONSTRAINT approval_tokens_step_id_fkey
FOREIGN KEY (step_id) REFERENCES recovery_steps_new(id) ON DELETE CASCADE;

-- 3. Check if there are any other constraints that need to be fixed
-- For recovery_plan_checkpoints
ALTER TABLE recovery_plan_checkpoints
DROP CONSTRAINT IF EXISTS recovery_plan_checkpoints_step_id_fkey;

ALTER TABLE recovery_plan_checkpoints
ADD CONSTRAINT recovery_plan_checkpoints_step_id_fkey
FOREIGN KEY (step_id) REFERENCES recovery_steps_new(id) ON DELETE CASCADE;

-- For recovery_plan_progress
ALTER TABLE recovery_plan_progress
DROP CONSTRAINT IF EXISTS recovery_plan_progress_step_id_fkey;

ALTER TABLE recovery_plan_progress
ADD CONSTRAINT recovery_plan_progress_step_id_fkey
FOREIGN KEY (step_id) REFERENCES recovery_steps_new(id) ON DELETE CASCADE;

-- Also check for any constraints on fk_step
ALTER TABLE approval_tokens
DROP CONSTRAINT IF EXISTS fk_step;

ALTER TABLE approval_tokens
ADD CONSTRAINT fk_step
FOREIGN KEY (step_id) REFERENCES recovery_steps_new(id) ON DELETE CASCADE;
