import path from 'path';
import { GCPClientSet } from '../lib/gcpClients';
import fs from 'fs';
import os from 'os';

// enable ssh on the vm
export const enableSsh = async (vmDetails: { instanceName: string; zone: string; ipAddress: string }, clients: GCPClientSet, projectId: string) => {
	const username = "<EMAIL>";
	const publicKeyPath = "~/.ssh/orkrestrate_id_rsa.pub";

  let publicKey: string;
  try {
    publicKey = fs.readFileSync(publicKeyPath, 'utf-8').trim();
  } catch (err) {
    console.error(`❌ Failed to read SSH public key from ${publicKeyPath}:`, err);
    throw err;
  }

	console.log("Enabling SSH on the VM", vmDetails);
	const { instanceName, zone, ipAddress } = vmDetails;

	const instancesClient = clients.instancesClient;
  // Get instance metadata
  const [instance] = await instancesClient.get({
    project: projectId,
    zone: zone,
    instance: instanceName,
  });

  const metadata = instance.metadata || {};
  const items = metadata.items || [];

  // Append SSH key
  const sshKeyEntry = `${username}:${publicKey}`;
  let sshKeyItem = items.find(item => item.key === 'ssh-keys');
  if (sshKeyItem) {
    sshKeyItem.value += `\n${sshKeyEntry}`;
  } else {
    items.push({ key: 'ssh-keys', value: sshKeyEntry });
  }

  // Update metadata
  const request = {
    project: projectId,
    zone,
    instance: instanceName,
    instancesSetMetadataRequestResource: {
      fingerprint: metadata.fingerprint,
      items,
    },
  };
  const [operation] = await instancesClient.setMetadata(request);
  await operation.promise();

  console.log(`✅ SSH key added to ${instanceName}`);
}

// return the metadata with the ssh key added
export function addSshKeyToMetadata(metadata: any) {
	const items = metadata.items || [];

  const username = "orkrestrate";
	
	const sshKeyPath = path.join(os.homedir(), '.ssh', 'orkrestrate_id_rsa.pub');
  console.log("SSH key path", sshKeyPath);

  let publicKey: string;
  try {
    publicKey = fs.readFileSync(sshKeyPath, 'utf-8').trim();
  } catch (err) {
    console.error(`❌ Failed to read SSH public key from ${sshKeyPath}:`, err);
    throw err;
  }

	const sshKeyEntry = `${username}:${publicKey}`;

	let sshKeyItem = items.find((item: { key: string }) => item.key === 'ssh-keys');
	if (sshKeyItem) {
		sshKeyItem.value += `\n${sshKeyEntry}`;
	} else {
		items.push({ key: 'ssh-keys', value: sshKeyEntry });
	}

	return {
		...metadata,
		items,
	};
}