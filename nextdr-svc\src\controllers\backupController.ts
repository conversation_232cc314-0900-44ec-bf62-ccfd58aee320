import { Request, Response, NextFunction, RequestHandler } from "express";
import {
	InstancesClient,
	DisksClient,
	SnapshotsClient,
	NetworksClient,
	FirewallsClient,
	RoutesClient,
	SubnetworksClient,
	ZoneOperationsClient,
} from "@google-cloud/compute";
import { ProjectsClient } from "@google-cloud/resource-manager";
import { Storage } from "@google-cloud/storage";
import { v1 } from "@google-cloud/sql";
import dotenv from "dotenv";
import { getVMConfig } from "../services/vmService";
import { VMConfig } from "../models/vmConfig";
import {
	VmProperties,
	ProjectConfig,
	BackupExecution,
	VMSnapshotRecord,
	DiskSnapshotRecord,
} from "../models/gcp_backup";
import { google } from "googleapis";
import { GoogleAuth, OAuth2Client, JWT } from "google-auth-library";
import { supabase } from "services/supabaseService";
import { v4 as uuidv4 } from "uuid";
import { GCP<PERSON>lientSet, GetGcpClients } from "../lib/gcpClients";

dotenv.config();

// const projectId = process.env.GCP_PROJECT_ID!;
const PROJECT_ID = "nextdr";
const ZONE = "us-central1-c";
const INSTANCE_NAME = "instance-20250311-043524";
const computeClient = new DisksClient();
const instancesClient = new InstancesClient();
const snapshotsClient = new SnapshotsClient();
const projectsClient = new ProjectsClient();
const storageClient = new Storage();
//const compute = new Compute();
const networksClient = new NetworksClient();
const firewallsClient = new FirewallsClient();
const routesClient = new RoutesClient();
const subnetworksClient = new SubnetworksClient();
const operationsClient = new ZoneOperationsClient();

interface CloudSQLInstance {
	name: string;
	settings: {
		databaseVersion: string;
		tier: string;
	};

	state: string;
	region: string;
}

// /**
//  * Create a snapshot (backup) of a GCP VM instance
//  */
// export const createBackupForVM = async (
//   vmId: string,
//   frequency: string,
//   retention: number
// ) => {
//   try {
//     const snapshotName = `${vmId}-backup-${Date.now()}`;
//     const [operation] = await computeClient.createSnapshot({
//       project: PROJECT_ID,
//       zone: ZONE,
//       disk: vmId,
//       snapshotResource: { name: snapshotName },
//     });

//     console.log(`Snapshot creation started: ${snapshotName}`);
//     return { message: 'Snapshot creation started', snapshotName };
//   } catch (error: any) {
//     console.error('Error creating backup:', error);
//     throw error;
//   }
// };

export const getSnapshots: RequestHandler = async (req, res) => {
	try {
		const { instanceName } = req.params;
		const datacenterId = req.query.datacenterId;
		if (!datacenterId) {
			res.status(400).json({ error: "Datacenter ID is required" });
			return;
		}

		if (!instanceName) {
			res.status(400).json({ error: "Instance name is required" });
			return;
		}

		const clients: GCPClientSet = await GetGcpClients(datacenterId as string);

		const [snapshots] = await clients.snapshotsClient.list({
			project: clients.projectId,
			filter: `name eq ${instanceName}-backup-.*`,
		});
		// just return the snapshot names and disk information
		const snapshotNames = snapshots.map((snapshot) => snapshot.name);
		res.status(200).json({ snapshotNames });
		return;
	} catch (error: any) {
		console.error("Error fetching snapshots:", error);
		res.status(500).json({ error: error.message });
		return;
	}
};

// Function to generate a valid new project ID
function generateNewProjectId(parentProjectId: string): string {
	const timestamp = Date.now().toString().slice(-6); // Get last 6 digits of timestamp
	let newProjectId = `copy-${parentProjectId}-${timestamp}`;
	newProjectId = newProjectId.toLowerCase().replace(/[^a-z0-9-]/g, ""); // Ensure valid format
	return newProjectId.slice(0, 30); // Ensure max length 30 chars
}

// Step 1: Create a new GCP Project as a copy of another project
async function createProjectCopy() {
	const NEW_PROJECT_ID = generateNewProjectId(PROJECT_ID);
	const [operation] = await projectsClient.createProject({
		project: { projectId: NEW_PROJECT_ID, name: "New Project Name" },
	});
	await operation.promise();
	console.log(`Project ${NEW_PROJECT_ID} created.`);

	// Copy IAM Policies
	const [policy] = await projectsClient.getIamPolicy({
		resource: `projects/${PROJECT_ID}`,
	});
	await projectsClient.setIamPolicy({
		resource: `projects/${NEW_PROJECT_ID}`,
		policy: policy,
	});
	console.log("IAM Policies copied.");
}

// const createCleanVMFromSnapshot = async (vmConfig: VMConfig, snapshotName: string) => {
//   // Modify vmConfig to use the snapshot
//   vmConfig.disks = vmConfig.disks?.map(disk => ({
//     type: disk.type,
//     mode: disk.mode,
//     device_name: disk.device_name,
//     source: `projects/${PROJECT_ID}/global/snapshots/${snapshotName}`
//   }));

//   await instancesClient.insert({
//     project: PROJECT_ID,
//     zone: ZONE,
//     instanceResource: vmConfig
//   });
// }

export const restoreFromSnapshot: RequestHandler = async (req, res) => {
	try {
		const { instanceName, snapshotName } = req.body;
		if (!instanceName || !snapshotName) {
			res.status(400).json({ error: "Instance name and snapshot name are required" });
			return;
		}

		await createProjectCopy();
		const vmConfig = await getVMConfig(instanceName);
		//await createCleanVMFromSnapshot(vmConfig, snapshotName);
		res.status(200).json({ message: "VM restoration started" });
	} catch (error: any) {
		res.status(500).json({ error: error.message });
	}
};

export const listVMs: RequestHandler = async (req, res) => {
	try {
		const zone = (req.query.zone as string) || "us-central1-c"; // Default zone if not specified
		const datacenterId = req.query.datacenterId;

		// const projectId = req.query.projectId;
		if (!datacenterId) {
			res.status(400).json({ error: "Datacenter ID is required" });
			return;
		}

		// const projectId = (req.query.projectId as string) || PROJECT_ID;
		// if (!projectId) {
		// 	res.status(400).json({ error: "Project ID is required" });
		// 	return;
		// }

		const clients: GCPClientSet = await GetGcpClients(datacenterId as string);

		const [instances] = await clients.instancesClient.list({
			project: clients.projectId,
			zone: zone,
		});

		const vmList = instances.map((instance) => ({
			name: instance.name,
			status: instance.status,
			zone: instance.zone?.split("/").pop(),
			machineType: instance.machineType?.split("/").pop(),
			creationTimestamp: instance.creationTimestamp,
			projectId: instance.selfLink?.split("/")[6] || PROJECT_ID, // Extract project from selfLink
			//networkInterfaces: instance.networkInterfaces || []
		}));

		res.status(200).json({
			vms: vmList,
			total: vmList.length,
			zone,
		});
		return;
	} catch (error: any) {
		console.error("Error fetching VM list:", error);
		res.status(500).json({ error: error.message });
		return;
	}
};

export const getNetworkInterfaces: RequestHandler = async (req, res) => {
	try {
		const zone = (req.query.zone as string) || "us-central1-c";
		const datacenterId = req.query.datacenterId;

		if (!datacenterId) {
			res.status(400).json({ error: "Datacenter ID is required" });
			return;
		}

		const clients: GCPClientSet = await GetGcpClients(datacenterId as string);
		const [instances] = await clients.instancesClient.list({
			project: clients.projectId,
			zone: zone,
		});

		const networkInterfaces = instances.flatMap(
			(instance) =>
				instance.networkInterfaces?.map((ni) => ({
					name: ni.name,
					network: ni.network,
					subnetwork: ni.subnetwork,
					networkIP: ni.networkIP,
					accessConfigs: ni.accessConfigs,
					network_ip: ni.networkIP,
					access_configs: ni.accessConfigs,
				})) || []
		);

		res.status(200).json({ networkInterfaces });
	} catch (error: any) {
		res.status(500).json({ error: error.message });
	}
};

export const getIAMAccountsRoles: RequestHandler = async (req, res) => {
	try {
		// const { projectId } = req.params;

		const datacenterId = req.query.datacenterId;
		if (!datacenterId) {
			res.status(400).json({ error: "Datacenter ID is required" });
			return;
		}

		const clients: GCPClientSet = await GetGcpClients(datacenterId as string);

		const [policy] = await clients.projectsClient.getIamPolicy({
			resource: `projects/${clients.projectId}`,
			options: {
				requestedPolicyVersion: 3, // Get the latest policy version
			},
		});

		const iamRoles =
			policy.bindings?.map((binding) => ({
				role: binding.role,
				members: binding.members,
				condition: binding.condition, // Include full condition object
				roleType: binding.role?.split("/").pop()?.replace("roles/", "") || binding.role, // Get readable role name
			})) || [];

		res.status(200).json({
			iamRoles,
			etag: policy.etag,
			version: policy.version,
		});
	} catch (error: any) {
		console.error("Error fetching IAM roles:", error);
		res.status(500).json({ error: error.message });
	}
};

export const getProjectResources: RequestHandler = async (req, res) => {
	try {
		const datacenterId = req.query.datacenterId;
		if (!datacenterId) {
			res.status(400).json({ error: "Datacenter ID is required" });
			return;
		}

		const clients: GCPClientSet = await GetGcpClients(datacenterId as string);

		const [buckets] = await clients.storageClient.getBuckets({
			project: clients.projectId,
		});

		res.status(200).json({
			storage: buckets.map((bucket) => ({
				name: bucket.name,
				location: bucket.metadata.location,
				storageClass: bucket.metadata.storageClass,
				created: bucket.metadata.timeCreated,
			})),
		});
	} catch (error: any) {
		console.error("Error fetching project resources:", error);
		res.status(500).json({ error: error.message });
	}
};

interface RecoveryPlan {
	source_instance: string;
	target_instance: string;
	snapshot_name: string;
	zone: string;
	network_config?: {
		vpc_name: string;
		subnet_name: string;
	};
}

interface RecoveryPlanStep {
	plan_id: string;
	step_order: number;
	action_type: string;
	params?: Record<string, any>;
}

// export const executeRecoveryPlan: RequestHandler = async (req, res) => {
//   try {
//     const { planId } = req.params;

//     // get the plan from the database
//     const { data: plan, error } = await supabase
//       .from('recovery_plans')
//       .select('*')
//       .eq('id', planId)
//       .single();

//     if (error || !plan) {
//       res.status(404).json({ error: 'Recovery plan not found' });
//       return;
//     }

//     // get the steps for the plan from the database
//     const { data: steps, error: stepsError } = await supabase
//       .from('recovery_plan_steps')
//       .select('*')
//       .eq('plan_id', planId);

//     if (stepsError) {
//       res.status(500).json({ error: stepsError.message });
//       return;
//     }

//     // sort the steps by step_order
//     const sortedSteps = steps.sort((a: RecoveryPlanStep, b: RecoveryPlanStep) => a.step_order - b.step_order);

//     // execute each step according to the step_order
//     for (const step of sortedSteps) {
//       switch (step.action_type) {
//         case 'create_project':
//           await createProjectCopy();
//           break;
//       }
//     }
//     res.status(200).json({
//       message: 'Recovery plan execution started',
//       planId,
//      // targetInstance: recoveryPlan.target_instance
//     });
//     return;
//   } catch (error: any) {
//     console.error('Error executing recovery plan:', error);
//     res.status(500).json({ error: error.message });
//     return;
//   }
// };

export const createBackup: RequestHandler = async (req, res) => {
	try {
		const { vmId, frequency, retention, datacenterId } = req.body;
		console.log(
			`Creating backup for VM ${vmId} with frequency ${frequency} and retention ${retention}`
		);
		const result = await createBackupForVM(vmId, frequency, retention, datacenterId);
		res.status(200).json(result);
	} catch (error: any) {
		res.status(500).json({ error: error.message });
	}
};

export const createBackupForVM = async (
	vmId: string,
	frequency: string,
	retention: number,
	datacenterId: string
) => {
	try {
		if (!vmId || typeof vmId !== "string" || vmId.trim() === "") {
			throw new Error("Invalid VM ID: VM ID must be a non-empty string");
		}

		console.log(
			`Starting backup process for VM ${vmId} with frequency ${frequency} and retention ${retention}`
		);

		const backupExecution: BackupExecution = {
			executionId: uuidv4(),
			scheduleId: frequency,
			triggeredAt: new Date().toISOString(),
			status: "PENDING",
			projectConfigSnapshot: {} as ProjectConfig,
			vmSnapshots: [],
		};

		console.log(`Generating project config for project ${PROJECT_ID}...`);
		try {
			backupExecution.projectConfigSnapshot = await generateProjectConfig(PROJECT_ID, datacenterId);
			console.log(`Project config generated successfully`);
		} catch (configError) {
			console.error(`Error generating project config: ${configError}`);
			throw configError;
		}

		console.log(`Generating VM config for VM ${vmId}...`);
		let vm_snapshot: VMSnapshotRecord;
		try {
			vm_snapshot = {
				vmConfig: await generateVmConfig(PROJECT_ID, ZONE, vmId, datacenterId),
				snapshots: [],
			};
			console.log(`VM config generated successfully for ${vmId}`);
			backupExecution.vmSnapshots.push(vm_snapshot);
		} catch (vmConfigError) {
			console.error(`Error generating VM config for ${vmId}: ${vmConfigError}`);
			throw vmConfigError;
		}

		console.log(`Processing ${vm_snapshot.vmConfig.disks.length} disks for VM ${vmId}...`);
		for (const disk of vm_snapshot.vmConfig.disks) {
			try {
				console.log(`Processing disk with source: ${disk.source}`);
				const diskUrlParts = disk.source!.split("/");
				const diskName = diskUrlParts[diskUrlParts.length - 1];
				console.log(`Extracted disk name: ${diskName}`);
				const snapshotName = `${diskName}-backup-${Date.now()}`;

				console.log(`Creating snapshot ${snapshotName} for disk ${diskName}...`);
				const [operation] = await computeClient.createSnapshot({
					project: PROJECT_ID,
					zone: ZONE,
					disk: diskName,
					snapshotResource: {
						name: snapshotName,
					},
				});
				console.log(`Snapshot creation operation started: ${operation.name}`);

				// Wait for the operation to complete
				console.log(`Waiting for snapshot operation to complete...`);
				while (true) {
					const [status] = await operationsClient.get({
						project: PROJECT_ID,
						zone: ZONE,
						operation: operation.name!.split("/").pop()!,
					});
					if (status.status === "DONE") {
						console.log(`Snapshot operation completed successfully`);
						break;
					}
					await new Promise((resolve) => setTimeout(resolve, 1000));
				}

				const snapshot_record: DiskSnapshotRecord = {
					deviceName: disk.deviceName!,
					diskName: disk.source.split("/").pop()!,
					snapshotId: snapshotName,
				};
				vm_snapshot.snapshots.push(snapshot_record);
				console.log(`✅ Snapshot created for disk ${diskName}: ${snapshotName}`);
			} catch (diskError) {
				console.error(`Error processing disk ${disk.deviceName}: ${diskError}`);
				throw diskError;
			}
		}
		backupExecution.status = "SUCCESS";
		// save the backup execution to the database
		// const { data: backupExecutionData, error: backupExecutionError } = await supabase
		//   .from('backup_executions')
		//   .insert(backupExecution);
		// if (backupExecutionError) {
		//   console.error('Error saving backup execution:', backupExecutionError);
		//   throw new Error('Error saving backup execution');
		//}
		return { message: `Snapshot creation started for vm ${vmId}` };
	} catch (error: any) {
		console.error("Error creating backup:", error);
		throw error;
	}
};

export async function generateVmConfig(
	projectId: string,
	zone: string,
	vmName: string,
	datacenterId: string
): Promise<VmProperties> {
	console.log(`Generating VM config for ${vmName} in project ${projectId} in zone ${zone}`);
	const [vm] = await instancesClient.get({ project: projectId, zone, instance: vmName });
	//console.log(`VM: ${JSON.stringify(vm)}`);
	const vmConfig: VmProperties = {
		name: vm.name!,
		id: String(vm.id!), // Convert to string
		zone: zone,
		status: vm.status!,
		machineType: vm.machineType!.split("/").pop()!,
		tags: vm.tags?.items ?? [],
		disks: (vm.disks || []).map((disk) => ({
			deviceName: disk.deviceName!,
			type: disk.type!,
			boot: disk.boot!,
			autoDelete: disk.autoDelete!,
			source: disk.source!,
			interface: disk.interface!,
			mode: disk.mode!,
		})),
		networkInterfaces: (vm.networkInterfaces || []).map((net) => ({
			network: net.network!,
			subnetwork: net.subnetwork!,
			networkIP: net.networkIP!,
			accessConfigs: net.accessConfigs?.map((cfg) => ({
				type: cfg.type!,
				name: cfg.name!,
				natIP: cfg.natIP || undefined, // Convert null to undefined
			})),
		})),
		serviceAccounts: (vm.serviceAccounts || []).map((sa) => ({
			email: sa.email!,
			scopes: sa.scopes!,
		})),
		metadata: {
			items: (vm.metadata?.items || []).map((item) => ({
				key: item.key!,
				value: item.value!,
			})),
		},
		scheduling: {
			preemptible: vm.scheduling?.preemptible ?? false,
			onHostMaintenance: vm.scheduling?.onHostMaintenance ?? "MIGRATE",
			automaticRestart: vm.scheduling?.automaticRestart ?? true,
		},
		labels: vm.labels ?? {},
		canIpForward: vm.canIpForward ?? false,
		deletionProtection: vm.deletionProtection ?? false,
	};

	return vmConfig;
}

export async function generateProjectConfig(
	projectId: string,
	datacenterId: string
): Promise<ProjectConfig> {
	console.log("****projectId:", projectId);
	console.log("GOOGLE_APPLICATION_CREDENTIALS:", process.env.GOOGLE_APPLICATION_CREDENTIALS);

	const auth = new GoogleAuth({
		scopes: ["https://www.googleapis.com/auth/cloud-platform"],
		keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
	});

	const authClient = await auth.getClient();

	const crm = google.cloudresourcemanager({
		version: "v3",
		auth: authClient as OAuth2Client | JWT,
	});
	// const iam = google.iam({
	//   version: 'v1',  // Keep v1 for IAM
	//   auth: authClient.toString(),
	// });

	// Get project metadata
	const projectResp = await crm.projects.get({
		name: `projects/${projectId}`, // Use full resource name format
	});
	const project = projectResp.data;

	console.log("****project:", project);

	// Get IAM policies
	const policyResp = await crm.projects.getIamPolicy({
		resource: `projects/${projectId}`, // Fix this too
		requestBody: {},
	});
	console.log("****policyResp:", policyResp);

	// Get enabled APIs
	const serviceUsage = google.serviceusage({
		version: "v1",
		auth: authClient as OAuth2Client | JWT, // pass the authenticated client
	});

	const apisResp = await serviceUsage.services.list({
		parent: `projects/${projectId}`,
		filter: "state:ENABLED",
	});
	console.log("****apisResp:", apisResp);

	const enabledApis = apisResp.data.services?.map((svc) => svc.config?.name!) ?? [];

	// Get networks
	const [networks] = await networksClient.list({ project: projectId });
	const networkConfigs = await Promise.all(
		networks.map(async (network) => {
			const subnetsResp = await subnetworksClient.list({
				project: projectId,
				region: network.subnetworks?.[0]?.split("/regions/")[1]?.split("/")[0] ?? "us-central1",
			});

			const firewallsResp = await firewallsClient.list({ project: projectId });
			const routesResp = await routesClient.list({ project: projectId });

			return {
				name: network.name!,
				autoCreateSubnetworks: network.autoCreateSubnetworks!,
				routingMode: network.routingConfig?.routingMode || "REGIONAL",
				subnets: (subnetsResp[0] || []).map((subnet) => ({
					name: subnet.name!,
					region: subnet.region!.split("/").pop()!,
					ipCidrRange: subnet.ipCidrRange!,
					privateIpGoogleAccess: subnet.privateIpGoogleAccess ?? false,
				})),
				firewalls: (firewallsResp[0] || []).map((fw) => ({
					name: fw.name!,
					direction: fw.direction!,
					priority: fw.priority!,
					sourceRanges: fw.sourceRanges || [],
					allowed:
						fw.allowed?.map((rule) => ({
							IPProtocol: rule.IPProtocol!,
							ports: rule.ports || undefined, // Convert null to undefined
						})) || [],
					disabled: fw.disabled ?? false,
				})),
				routes: (routesResp[0] || [])
					.filter((r) => r.network === network.selfLink)
					.map((route) => ({
						name: route.name!,
						destRange: route.destRange!,
						nextHopGateway: route.nextHopGateway ? route.nextHopGateway!.split("/").pop()! : "",
						priority: route.priority!,
					})),
			};
		})
	);
	console.log("****networkConfigs:", networkConfigs);

	const projectConfig: ProjectConfig = {
		projectId: project.projectId!,
		labels: project.labels ?? {},
		iamPolicies: (policyResp.data.bindings || []).map((binding) => ({
			role: binding.role!,
			members: binding.members || [],
		})),
		enabledApis,
		networks: networkConfigs,
		serviceAccounts: [], // Add service account fetching if needed
	};

	return projectConfig;
}
