import React from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, CheckCircle, XCircle, ArrowRight, User, Shield } from "lucide-react";
import { useModalStore } from "@/lib/store/useStore";
import { RecoveryStep } from "@/lib/types";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";

interface CheckpointInfoModalProps {
	checkpoint: {
		id: string;
		stepId: string;
		status: string;
		approver_id?: string;
		approver_role?: string;
		approved_by?: string;
		approved_at?: string;
		comment?: string;
		timestamp?: string;
	};
	step: RecoveryStep;
	onResume?: () => void;
}

const CheckpointInfoModal: React.FC = () => {
	const { isOpen, onClose, modalType, modalData } = useModalStore();
	const supabase = useSupabaseClient();

	const isModalOpen = isOpen && modalType === "checkpointInfo";
	const { checkpoint, step, onResume } = (modalData as CheckpointInfoModalProps) || {};

	const { data: approver } = useQuery({
		queryKey: ["user", checkpoint?.approver_id],
		queryFn: async () => {
			if (!checkpoint?.approver_id) return null;

			const { data, error } = await supabase
				.from("user_profiles")
				.select("id, email, first_name, last_name, role")
				.eq("id", checkpoint.approver_id)
				.single();

			if (error) throw error;
			return data;
		},
		enabled: isModalOpen && !!checkpoint?.approver_id,
	});

	const { data: approvedBy } = useQuery({
		queryKey: ["user", checkpoint?.approved_by],
		queryFn: async () => {
			if (!checkpoint?.approved_by) return null;

			const { data, error } = await supabase
				.from("user_profiles")
				.select("id, email, first_name, last_name, role")
				.eq("id", checkpoint.approved_by)
				.single();

			if (error) throw error;
			return data;
		},
		enabled: isModalOpen && !!checkpoint?.approved_by,
	});

	if (!isModalOpen || !checkpoint || !step) return null;

	const isAwaitingApproval = checkpoint.status === "AWAITING_APPROVAL";
	const isApproved = checkpoint.status === "APPROVED";
	const isRejected = checkpoint.status === "REJECTED";

	const formatDate = (dateString?: string) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleString();
	};

	const getApproverName = (user: any) => {
		if (!user) return "Unknown";
		return user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.email;
	};

	return (
		<Dialog open={isModalOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center justify-between">
						<span>Checkpoint Information</span>
						{isAwaitingApproval && (
							<Badge
								variant="outline"
								className="bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
							>
								<Clock className="h-3 w-3 mr-1" />
								Awaiting Approval
							</Badge>
						)}
						{isApproved && (
							<Badge
								variant="outline"
								className="bg-green-500/10 text-green-600 border-green-500/20"
							>
								<CheckCircle className="h-3 w-3 mr-1" />
								Approved
							</Badge>
						)}
						{isRejected && (
							<Badge variant="destructive" className="bg-red-500/10 text-red-600 border-red-500/20">
								<XCircle className="h-3 w-3 mr-1" />
								Rejected
							</Badge>
						)}
					</DialogTitle>
					<DialogDescription>
						This checkpoint requires approval before execution can continue.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					<div>
						<h3 className="text-sm font-medium mb-2">Step Details</h3>
						<div className="space-y-2 text-sm">
							<div className="flex justify-between">
								<span className="text-muted-foreground">Name:</span>
								<span>{step.name}</span>
							</div>
							<div className="flex justify-between">
								<span className="text-muted-foreground">Type:</span>
								<span>{step.operation_type}</span>
							</div>
							<div className="flex justify-between">
								<span className="text-muted-foreground">Order:</span>
								<span>{step.step_order}</span>
							</div>
						</div>
					</div>

					<Separator />

					<div>
						<h3 className="text-sm font-medium mb-2">Approval Information</h3>
						<div className="space-y-2 text-sm">
							<div className="flex justify-between items-center">
								<span className="text-muted-foreground flex items-center">
									<User className="h-3 w-3 mr-1" />
									Approver:
								</span>
								<span>
									{checkpoint.approver_id ? getApproverName(approver) : "Role-based approval"}
								</span>
							</div>

							{checkpoint.approver_role && (
								<div className="flex justify-between items-center">
									<span className="text-muted-foreground flex items-center">
										<Shield className="h-3 w-3 mr-1" />
										Required Role:
									</span>
									<Badge variant="outline">{checkpoint.approver_role}</Badge>
								</div>
							)}

							<div className="flex justify-between">
								<span className="text-muted-foreground">Status:</span>
								<span>{checkpoint.status}</span>
							</div>

							{isApproved && (
								<>
									<div className="flex justify-between">
										<span className="text-muted-foreground">Approved By:</span>
										<span>{getApproverName(approvedBy)}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-muted-foreground">Approved At:</span>
										<span>{formatDate(checkpoint.approved_at)}</span>
									</div>
								</>
							)}

							{checkpoint.comment && (
								<div>
									<span className="text-muted-foreground block mb-1">Comment:</span>
									<div className="bg-muted p-2 rounded text-xs">{checkpoint.comment}</div>
								</div>
							)}
						</div>
					</div>

					{isAwaitingApproval && (
						<div className="bg-yellow-500/10 p-3 rounded-md text-sm">
							<p className="flex items-center text-yellow-700">
								<Clock className="h-4 w-4 mr-2" />
								Waiting for approval from{" "}
								{checkpoint.approver_id
									? getApproverName(approver)
									: `a user with the ${checkpoint.approver_role} role`}
							</p>
							<p className="mt-2 text-xs text-muted-foreground">
								An email has been sent to the approver. Once approved, execution will continue
								automatically.
							</p>
						</div>
					)}

					{isApproved && (
						<div className="bg-green-500/10 p-3 rounded-md text-sm">
							<p className="flex items-center text-green-700">
								<CheckCircle className="h-4 w-4 mr-2" />
								This checkpoint has been approved
							</p>
							<p className="mt-2 text-xs text-muted-foreground">
								Execution can continue to the next steps.
							</p>
						</div>
					)}

					{isRejected && (
						<div className="bg-red-500/10 p-3 rounded-md text-sm">
							<p className="flex items-center text-red-700">
								<XCircle className="h-4 w-4 mr-2" />
								This checkpoint has been rejected
							</p>
							<p className="mt-2 text-xs text-muted-foreground">
								Execution has been halted. You may need to create a new recovery plan.
							</p>
						</div>
					)}
				</div>

				<DialogFooter className="flex justify-between">
					<Button variant="outline" onClick={onClose}>
						Close
					</Button>

					{isApproved && onResume && (
						<Button
							onClick={() => {
								// Show a toast notification before resuming
								import("@/components/ui/sonner").then(({ toast }) => {
									toast.info("Resuming execution...", {
										description: "The recovery plan will continue with the next steps",
									});
								});

								// Call the resume function and close the modal
								onResume();
								onClose();
							}}
							className="bg-green-600 hover:bg-green-700"
						>
							<ArrowRight className="h-4 w-4 mr-2" />
							Resume Execution
						</Button>
					)}
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default CheckpointInfoModal;
