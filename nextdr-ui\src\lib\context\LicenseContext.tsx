import React, { createContext, useContext, useEffect, useState, useRef } from "react";
import { useLicenseStore } from "@/lib/store/useStore";
import { toast } from "@/components/ui/sonner";
import * as apiClient from "@/lib/api/api-client";

interface LicenseContextType {
	isLicensed: boolean;
	isLoading: boolean;
	licenseInfo: {
		customerId: string;
		expiresAt: string;
	} | null;
	checkLicense: () => Promise<boolean>;
}

const LicenseContext = createContext<LicenseContextType>({
	isLicensed: false,
	isLoading: true,
	licenseInfo: null,
	checkLicense: async () => false,
});

export const useLicense = () => useContext(LicenseContext);

interface LicenseProviderProps {
	children: React.ReactNode;
}

export const LicenseProvider: React.FC<LicenseProviderProps> = ({ children }) => {
	const [isLicensed, setIsLicensed] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [licenseInfo, setLicenseInfo] = useState<{ customerId: string; expiresAt: string } | null>(
		null
	);
	const { setLicense } = useLicenseStore();
	const isCheckingLicense = useRef(false);

	const checkLicense = async (): Promise<boolean> => {
		if (isCheckingLicense.current) {
			return isLicensed;
		}

		isCheckingLicense.current = true;
		setIsLoading(true);

		try {
			console.log("Checking license status");
			const response = await apiClient.verifyLicenseStatus();

			if (response.valid) {
				setIsLicensed(true);
				setLicenseInfo({
					customerId: response.customerId,
					expiresAt: response.expiresAt,
				});

				setLicense({
					isActive: true,
					expiresAt: response.expiresAt,
					customerId: response.customerId,
					features: response.features || [],
				});

				setIsLoading(false);
				isCheckingLicense.current = false;
				return true;
			}

			setIsLicensed(false);
			setLicenseInfo(null);
			setLicense({
				isActive: false,
				expiresAt: null,
				customerId: null,
				features: [],
			});

			setIsLoading(false);
			isCheckingLicense.current = false;
			return false;
		} catch (error) {
			console.error("Error checking license:", error);
			toast.error("Failed to verify license status");

			setIsLicensed(false);
			setLicenseInfo(null);
			setLicense({
				isActive: false,
				expiresAt: null,
				customerId: null,
				features: [],
			});

			setIsLoading(false);
			isCheckingLicense.current = false;
			return false;
		}
	};

	useEffect(() => {
		checkLicense().catch((error) => {
			console.error("License check failed:", error);
			toast.error("Failed to verify license status");
		});

		const interval = setInterval(() => {
			checkLicense().catch((error) => {
				console.error("Periodic license check failed:", error);
			});
		}, 5 * 60 * 1000);

		return () => clearInterval(interval);
	}, []);

	return (
		<LicenseContext.Provider value={{ isLicensed, isLoading, licenseInfo, checkLicense }}>
			{children}
		</LicenseContext.Provider>
	);
};

export default LicenseProvider;
