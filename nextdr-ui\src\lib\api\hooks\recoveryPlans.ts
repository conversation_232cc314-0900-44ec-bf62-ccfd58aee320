import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../../supabase-client";
import { toast } from "@/components/ui/sonner";
import * as apiClient from "../api-client";
import { RecoveryPlan, RecoveryStep as RecoveryStepType } from "@/lib/types";
import { useEffect, useState, useRef } from "react";

type RecoveryStep = RecoveryStepType;

export const useRecoveryPlans = () => {
	return useQuery({
		queryKey: ["recoveryPlans"],
		queryFn: async () => {
			const { data, error } = await supabase
				.from("recovery_plans_new")
				.select("*")
				.order("name", { ascending: true });

			if (error) {
				throw new Error(error.message);
			}

			return data as RecoveryPlan[];
		},
	});
};

export const useAddRecoveryPlan = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (recoveryPlan: Omit<RecoveryPlan, "id" | "created_at">) => {
			const insertData = { ...recoveryPlan };

			if (insertData.app_group_id !== undefined) {
				const appGroupId =
					typeof insertData.app_group_id === "string"
						? parseInt(insertData.app_group_id, 10)
						: insertData.app_group_id;

				if (!isNaN(appGroupId)) {
					// @ts-ignore
					insertData.app_group_id = appGroupId;
				} else {
					delete insertData.app_group_id;
				}
			}

			const { data, error } = await supabase
				.from("recovery_plans_new")
				.insert(insertData)
				.select()
				.single();

			if (error) {
				throw new Error(error.message);
			}

			return data as RecoveryPlan;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery plan added successfully");
		},
		onError: (error) => {
			console.log("error", error);
			toast.error(`Failed to add recovery plan: ${error.message}`);
		},
	});
};

export const useUpdateRecoveryPlan = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (recoveryPlan: RecoveryPlan) => {
			const updateData: Partial<RecoveryPlan> = {
				name: recoveryPlan.name,
				description: recoveryPlan.description,
			};

			if (recoveryPlan.app_group_id !== undefined) {
				const appGroupId =
					typeof recoveryPlan.app_group_id === "string"
						? parseInt(recoveryPlan.app_group_id, 10)
						: recoveryPlan.app_group_id;

				if (!isNaN(appGroupId)) {
					// @ts-ignore
					updateData.app_group_id = appGroupId;
				}
			}

			const { data, error } = await supabase
				.from("recovery_plans_new")
				.update(updateData)
				.eq("id", recoveryPlan.id)
				.select()
				.single();

			if (error) {
				throw new Error(error.message);
			}

			return data as RecoveryPlan;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery plan updated successfully");
		},
		onError: (error) => {
			toast.error(`Failed to update recovery plan: ${error.message}`);
		},
	});
};

export const useDeleteRecoveryPlan = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (planId: string) => {
			const { error } = await supabase.from("recovery_plans_new").delete().eq("id", planId);

			if (error) {
				throw new Error(error.message);
			}

			return { success: true };
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery plan deleted successfully");
		},
		onError: (error) => {
			toast.error(`Failed to delete recovery plan: ${error.message}`);
		},
	});
};

export const useRecoverySteps = (planId: string) => {
	return useQuery({
		queryKey: ["recoverySteps", planId],
		queryFn: async () => {
			const { data, error } = await supabase
				.from("recovery_steps_new")
				.select("*")
				.eq("recovery_plan_id", planId)
				.order("step_order", { ascending: true });

			if (error) {
				throw new Error(error.message);
			}

			return data as RecoveryStep[];
		},
		enabled: !!planId,
	});
};

export const useAddRecoveryStep = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (step: Omit<RecoveryStep, "id" | "created_at">) => {
			console.log("Adding recovery step:", step);

			// Validate that recovery_plan_id is present
			if (!step.recovery_plan_id) {
				console.error("Missing recovery_plan_id in step data:", step);
				throw new Error("Missing recovery_plan_id. Cannot add step without a plan.");
			}

			// Ensure we have a valid step_order
			if (step.step_order === undefined || step.step_order === null) {
				step.step_order = 0;
			}

			// Insert the step with all required fields
			const { data, error } = await supabase
				.from("recovery_steps_new")
				.insert({
					...step,
					recovery_plan_id: step.recovery_plan_id, // Explicitly include this to ensure it's set
					status: step.status || "pending",
				})
				.select()
				.single();

			if (error) {
				console.error("Error adding recovery step:", error);
				throw new Error(error.message);
			}

			console.log("Step added successfully:", data);
			return data as RecoveryStep;
		},
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["recoverySteps", data.recovery_plan_id] });
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery step added successfully");
		},
		onError: (error) => {
			console.error("Failed to add recovery step:", error);
			toast.error(`Failed to add recovery step: ${error.message}`);
		},
	});
};

export const useUpdateRecoveryStep = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (step: RecoveryStep) => {
			console.log("Updating recovery step:", step);

			if (!step.id || !step.recovery_plan_id) {
				throw new Error("Missing required fields for step update");
			}

			const { data, error } = await supabase
				.from("recovery_steps_new")
				.update({
					name: step.name,
					configuration: step.configuration,
					step_order: step.step_order,
				})
				.eq("id", step.id)
				.select()
				.single();

			if (error) {
				console.error("Error updating step:", error);
				throw new Error(error.message);
			}

			console.log("Step updated successfully:", data);
			return data as RecoveryStep;
		},
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["recoverySteps", data.recovery_plan_id] });
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery step updated successfully");
		},
		onError: (error) => {
			console.error("Mutation error:", error);
			toast.error(`Failed to update recovery step: ${error.message}`);
		},
	});
};

export const useDeleteRecoveryStep = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (stepId: string) => {
			console.log("Starting deletion process for step ID:", stepId);

			try {
				// First, get the recovery_plan_id for this step
				const { data: step, error: getError } = await supabase
					.from("recovery_steps_new")
					.select("recovery_plan_id")
					.eq("id", stepId)
					.single();

				if (getError) {
					console.error("Error fetching step before deletion:", getError);
					throw new Error(getError.message);
				}

				if (!step || !step.recovery_plan_id) {
					console.error("Step not found or missing recovery_plan_id:", step);
					throw new Error("Step not found or missing recovery plan ID");
				}

				const planId = step.recovery_plan_id;
				console.log("Found plan ID for step:", planId);

				// Delete related records in the correct order to handle foreign key constraints
				try {
					// 1. First check for and delete checkpoint-related approval tokens
					console.log("Checking for checkpoints for step:", stepId);
					const { data: checkpoints, error: checkpointError } = await supabase
						.from("recovery_plan_checkpoints")
						.select("id")
						.eq("step_id", stepId);

					if (checkpointError) {
						console.warn("Error finding checkpoints:", checkpointError);
					} else if (checkpoints && checkpoints.length > 0) {
						// Extract checkpoint IDs
						const checkpointIds = checkpoints.map((checkpoint) => checkpoint.id);
						console.log("Found checkpoint IDs:", checkpointIds);

						// Delete approval tokens for these checkpoints
						console.log("Deleting checkpoint-related approval tokens");
						try {
							const { error: checkpointTokenDeleteError } = await supabase
								.from("approval_tokens")
								.delete()
								.eq("is_checkpoint", true)
								.in("checkpoint_id", checkpointIds);

							if (checkpointTokenDeleteError) {
								console.warn(
									"Error deleting checkpoint-related approval tokens:",
									checkpointTokenDeleteError
								);
							}
						} catch (err) {
							console.warn("Exception deleting checkpoint tokens:", err);
						}
					} else {
						console.log("No checkpoints found for this step");
					}

					// 2. Delete recovery plan checkpoints for this step
					console.log("Deleting recovery plan checkpoints for step:", stepId);
					try {
						await supabase.from("recovery_plan_checkpoints").delete().eq("step_id", stepId);
						console.log("Checkpoints deleted successfully");
					} catch (err) {
						console.warn("Exception deleting checkpoints:", err);
					}

					// 3. Delete recovery plan progress entries for this step
					console.log("Deleting recovery plan progress entries for step:", stepId);
					try {
						await supabase.from("recovery_plan_progress").delete().eq("step_id", stepId);
						console.log("Progress entries deleted successfully");
					} catch (err) {
						console.warn("Exception deleting progress entries:", err);
					}

					// 4. Delete any remaining approval tokens associated with this step
					console.log("Deleting approval tokens for step:", stepId);
					try {
						await supabase.from("approval_tokens").delete().eq("step_id", stepId);
						console.log("Approval tokens deleted successfully");
					} catch (err) {
						console.warn("Exception deleting approval tokens:", err);
					}

					// 5. Now delete the step itself
					console.log("Deleting step:", stepId);
					const { error: deleteError } = await supabase
						.from("recovery_steps_new")
						.delete()
						.eq("id", stepId);

					if (deleteError) {
						console.error("Error deleting step:", deleteError);
						throw new Error(deleteError.message);
					}

					console.log("Step deleted successfully:", stepId);
					return { stepId, planId };
				} catch (err) {
					console.error("Error during deletion process:", err);
					throw new Error(`Error during deletion process: ${err.message}`);
				}
			} catch (err) {
				console.error("Top-level error in deletion process:", err);
				throw new Error(`Failed to delete recovery step: ${err.message}`);
			}
		},
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["recoverySteps", data.planId] });
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery step deleted successfully");
		},
		onError: (error) => {
			console.error("Delete step error:", error);
			toast.error(`Failed to delete recovery step: ${error.message}`);
		},
	});
};

export const useExecuteRecoveryPlan = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			planId,
			datacenterId,
			resumeExecution = false,
		}: {
			planId: string;
			datacenterId: string;
			resumeExecution?: boolean;
		}) => {
			try {
				return await apiClient.executeRecoveryPlan(planId, datacenterId, resumeExecution);
			} catch (error: any) {
				throw new Error(error.message || "Failed to execute recovery plan");
			}
		},
		onSuccess: (data) => {
			// Invalidate recovery plans to refresh status
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery plan execution started");
			return data;
		},
		onError: (error) => {
			toast.error(`Failed to execute recovery plan: ${error.message}`);
		},
	});
};

export const useResumeRecoveryPlanExecution = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ planId, checkpointId }: { planId: string; checkpointId: string }) => {
			try {
				return await apiClient.resumeRecoveryPlanExecution(planId, checkpointId);
			} catch (error: any) {
				throw new Error(error.message || "Failed to resume recovery plan execution");
			}
		},
		onSuccess: (data) => {
			// Invalidate recovery plans to refresh status
			queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
			toast.success("Recovery plan execution resumed");
			return data;
		},
		onError: (error) => {
			toast.error(`Failed to resume recovery plan execution: ${error.message}`);
		},
	});
};

export const useProcessApproval = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			token,
			decision,
			comment = "",
		}: {
			token: string;
			decision: "approved" | "rejected";
			comment?: string;
		}) => {
			try {
				console.log(`Processing approval: token=${token}, decision=${decision}`);
				return await apiClient.processApproval(token, decision, comment);
			} catch (error: any) {
				console.error("Error processing approval:", error);
				throw new Error(error.message || "Failed to process approval");
			}
		},
		onSuccess: (data) => {
			console.log("Approval processed successfully:", data);

			// Invalidate recovery plans to refresh status
			if (data.type === "checkpoint") {
				queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
				toast.success(`Checkpoint ${data.checkpoint.approval_status} successfully`);

				// If approved, the plan will auto-resume, so we don't need to do anything else
				// The SSE events will update the UI automatically
				if (data.checkpoint.approval_status === "approved") {
					toast.success("Recovery plan execution will resume automatically");
				}
			} else {
				queryClient.invalidateQueries({ queryKey: ["recoverySteps", data.step.recovery_plan_id] });
				toast.success(`Step ${data.step.approval_metadata?.approval_status} successfully`);
			}
			return data;
		},
		onError: (error) => {
			console.error("Failed to process approval:", error);
			toast.error(`Failed to process approval: ${error.message}`);
		},
	});
};

/**
 * Custom hook to subscribe to SSE events for a recovery plan execution
 * @param planId The ID of the recovery plan to listen for events
 * @returns Object containing step statuses, plan status, and execution state
 */
export const useRecoveryPlanExecution = (planId: string | undefined) => {
	const [stepStatuses, setStepStatuses] = useState<Record<string, apiClient.RecoveryStepStatus>>(
		{}
	);
	const [planStatus, setPlanStatus] = useState<apiClient.RecoveryPlanStatus>("NOT_STARTED");
	const [checkpoints, setCheckpoints] = useState<Record<string, any>>({});
	const [isExecuting, setIsExecuting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [lastEvent, setLastEvent] = useState<apiClient.RecoveryEvent | null>(null);
	const [isLoadingInitialState, setIsLoadingInitialState] = useState(false);

	const isMountedRef = useRef(true);
	const queryClient = useQueryClient();

	// Function to fetch the initial execution state
	const fetchInitialExecutionState = async () => {
		if (!planId) return;

		try {
			setIsLoadingInitialState(true);

			// Fetch the current recovery plan to get execution status
			const { data: plan, error: planError } = await supabase
				.from("recovery_plans_new")
				.select("*")
				.eq("id", planId)
				.single();

			if (planError) {
				console.error("Error fetching recovery plan:", planError);
				return;
			}

			// If the plan has an execution in progress or paused, update the UI
			if (plan.execution_status && plan.current_execution_id) {
				// Set plan status based on execution_status
				const status = plan.execution_status.toUpperCase() as apiClient.RecoveryPlanStatus;
				setPlanStatus(status);

				// If execution is in progress, set isExecuting to true
				if (status === "IN_PROGRESS") {
					setIsExecuting(true);
				}

				// Fetch progress for all steps in this execution
				const { data: progress, error: progressError } = await supabase
					.from("recovery_plan_progress")
					.select("*")
					.eq("recovery_plan_id", planId)
					.eq("execution_id", plan.current_execution_id);

				if (progressError) {
					console.error("Error fetching step progress:", progressError);
					return;
				}

				// Update step statuses based on progress
				const newStepStatuses: Record<string, apiClient.RecoveryStepStatus> = {};

				for (const stepProgress of progress || []) {
					// Convert database status to UI status format
					let uiStatus: apiClient.RecoveryStepStatus;

					switch (stepProgress.status) {
						case "completed":
							uiStatus = "COMPLETED";
							break;
						case "in_progress":
							uiStatus = "IN_PROGRESS";
							break;
						case "failed":
							uiStatus = "FAILED";
							break;
						case "awaiting_approval":
							uiStatus = "AWAITING_APPROVAL";
							break;
						case "approved":
							uiStatus = "APPROVED";
							break;
						case "rejected":
							uiStatus = "REJECTED";
							break;
						default:
							uiStatus = "PENDING" as apiClient.RecoveryStepStatus;
					}

					// Get the step to get its step_order
					const { data: step, error: stepError } = await supabase
						.from("recovery_steps_new")
						.select("step_order")
						.eq("id", stepProgress.step_id)
						.single();

					if (stepError) {
						console.warn(`Error fetching step ${stepProgress.step_id}:`, stepError);
						continue;
					}

					// Use step_order as the key for step statuses
					newStepStatuses[step.step_order.toString()] = uiStatus;
				}

				// Update step statuses state
				setStepStatuses(newStepStatuses);

				// Fetch checkpoints
				const { data: checkpointsData, error: checkpointsError } = await supabase
					.from("recovery_plan_checkpoints")
					.select("*")
					.eq("recovery_plan_id", planId);

				if (checkpointsError) {
					console.error("Error fetching checkpoints:", checkpointsError);
					return;
				}

				// Update checkpoints state
				const newCheckpoints: Record<string, any> = {};

				for (const checkpoint of checkpointsData || []) {
					// Get the step to get its step_order
					const { data: step, error: stepError } = await supabase
						.from("recovery_steps_new")
						.select("step_order")
						.eq("id", checkpoint.step_id)
						.single();

					if (stepError) {
						console.warn(`Error fetching step ${checkpoint.step_id}:`, stepError);
						continue;
					}

					newCheckpoints[checkpoint.id] = {
						stepId: step.step_order.toString(),
						status: checkpoint.approval_status.toUpperCase(),
						approver_id: checkpoint.approver_id,
						approver_role: checkpoint.approver_role,
						approved_by: checkpoint.approved_by,
						approved_at: checkpoint.approved_at,
						comment: checkpoint.approval_metadata?.comment,
						timestamp: checkpoint.updated_at,
					};
				}

				// Update checkpoints state
				setCheckpoints(newCheckpoints);
			}
		} catch (error) {
			console.error("Error fetching initial execution state:", error);
		} finally {
			setIsLoadingInitialState(false);
		}
	};

	useEffect(() => {
		isMountedRef.current = true;

		if (!planId) return;

		// Fetch initial execution state when component mounts
		fetchInitialExecutionState();

		let cleanup: (() => void) | undefined;

		const startListening = async () => {
			if (isMountedRef.current) {
				setError(null);
			}

			try {
				cleanup = await apiClient.createRecoveryPlanEventSource(
					planId,
					(event) => {
						console.log("Received SSE event:", event);
						if (!isMountedRef.current) return;

						setLastEvent(event);

						// Handle different event types
						if (event.type === "step_update") {
							console.log(`Received step_update event for step ${event.stepId}: ${event.status}`);

							// Update step status
							setStepStatuses((prev) => ({
								...prev,
								[event.stepId]: event.status,
							}));

							// If this is a checkpoint-related event, update checkpoints
							if (event.checkpoint_id) {
								console.log(
									`Updating checkpoint ${event.checkpoint_id} with status ${event.status}`
								);
								setCheckpoints((prev) => ({
									...prev,
									[event.checkpoint_id]: {
										stepId: event.stepId,
										status: event.status,
										approver_id: event.approver_id,
										approver_role: event.approver_role,
										approved_by: event.approved_by,
										approved_at: event.approved_at,
										comment: event.comment,
										timestamp: event.timestamp,
									},
								}));
							}

							// Update execution state based on step status
							if (event.status === "IN_PROGRESS") {
								console.log(`Step ${event.stepId} is in progress, setting isExecuting to true`);
								setIsExecuting(true);

								// Show toast notification for step in progress
								toast.info(`Executing step ${event.stepId}...`);
							} else if (event.status === "AWAITING_APPROVAL") {
								console.log(
									`Step ${event.stepId} is awaiting approval, setting isExecuting to false`
								);
								setIsExecuting(false);

								// Show toast notification for approval required
								if (event.checkpoint_id) {
									toast.info("Approval required to continue execution", {
										description: "The recovery plan is paused waiting for approval",
									});
								}
							} else if (event.status === "COMPLETED") {
								// Show toast notification for step completion
								toast.success(`Step ${event.stepId} completed successfully`);
							} else if (event.status === "FAILED") {
								// Show toast notification for step failure
								toast.error(`Step ${event.stepId} failed: ${event.error || "Unknown error"}`);
							}

							// If a step is approved, automatically update UI state
							if (event.status === "APPROVED" && event.checkpoint_id) {
								console.log(`Step ${event.stepId} is approved, updating checkpoint status`);
								// Update the checkpoint status
								setCheckpoints((prev) => ({
									...prev,
									[event.checkpoint_id]: {
										...prev[event.checkpoint_id],
										status: "APPROVED",
										approved_by: event.approved_by,
										approved_at: event.approved_at,
										comment: event.comment,
									},
								}));

								// Show toast notification for approval
								toast.success("Approval confirmed, execution will resume", {
									description: event.comment
										? `Comment: ${event.comment}`
										: "The recovery plan will continue execution",
								});

								// If execution is resuming automatically, update state
								console.log(`Setting isExecuting to true as step is approved`);
								setIsExecuting(true);
							}
						} else if (event.type === "plan_update") {
							console.log(`Received plan_update event: ${event.status}`);
							// Update plan status
							setPlanStatus(event.status);

							// Update execution state based on plan status
							if (
								event.status === "IN_PROGRESS" ||
								event.status === "RESUMING" ||
								event.status === "EXECUTING"
							) {
								console.log(`Plan is ${event.status}, setting isExecuting to true`);
								setIsExecuting(true);

								if (event.status === "RESUMING" && event.checkpoint_id) {
									toast.success("Recovery plan execution is resuming", {
										description: event.message || "Continuing with the next steps",
									});
								} else if (event.status === "EXECUTING" && event.next_step_id) {
									toast.success("Execution continuing", {
										description:
											event.message ||
											`Executing next step: ${event.next_step_name || event.next_step_id}`,
									});
								}
							} else if (event.status === "RESUME_FAILED") {
								console.log(`Plan resume failed, setting isExecuting to false`);
								setIsExecuting(false);

								toast.error("Failed to resume execution", {
									description: event.message || "Check the logs for details",
								});
							} else if (
								event.status === "PAUSED" ||
								event.status === "COMPLETED" ||
								event.status === "FAILED"
							) {
								console.log(`Plan is ${event.status}, setting isExecuting to false`);
								setIsExecuting(false);

								if (event.status === "PAUSED" && event.message) {
									toast.info("Recovery plan execution paused", {
										description: event.message,
									});
								}
							}

							// If plan is completed or failed, invalidate queries to refresh data
							if (event.status === "COMPLETED" || event.status === "FAILED") {
								console.log(`Plan is ${event.status}, invalidating queries to refresh data`);
								queryClient.invalidateQueries({ queryKey: ["recoveryPlans"] });
								queryClient.invalidateQueries({ queryKey: ["recoverySteps", planId] });

								// When plan is completed, set all steps to completed status
								if (event.status === "COMPLETED") {
									// Update all steps to COMPLETED status
									console.log("Setting all steps to COMPLETED status");

									// Get all step IDs from the stepStatuses
									const stepIds = Object.keys(stepStatuses);

									// Update each step status to COMPLETED
									stepIds.forEach((stepId) => {
										setStepStatuses((prev) => ({
											...prev,
											[stepId]: "COMPLETED",
										}));
									});

									toast.success("Recovery plan execution completed successfully");
								} else {
									toast.error("Recovery plan execution failed", {
										description: event.message || "Check the logs for details",
									});
								}
							}
						}
					},
					(error) => {
						console.error("SSE connection error:", error);
						if (isMountedRef.current) {
							setError("Lost connection to server. Please refresh the page.");
							setIsExecuting(false);
						}
					}
				);
			} catch (error) {
				console.error("Error creating SSE connection:", error);
				if (isMountedRef.current) {
					setError("Failed to connect to server. Please refresh the page.");
				}
			} finally {
				if (isMountedRef.current) {
					setIsExecuting(false);
				}
			}
		};

		if (planId) {
			startListening();
		}

		return () => {
			isMountedRef.current = false;

			if (cleanup) {
				cleanup();
			}
		};
	}, [planId, queryClient]);

	const executeRecoveryPlan = async (datacenterId: string, resumeExecution: boolean = false) => {
		if (!planId) {
			setError("No recovery plan selected");
			return;
		}

		try {
			if (isMountedRef.current) {
				if (!resumeExecution) {
					setStepStatuses({});
					setCheckpoints({});
				}
				setIsExecuting(true);
				setError(null);
			}

			await apiClient.executeRecoveryPlan(planId, datacenterId, resumeExecution);

			if (isMountedRef.current) {
				toast.success(
					resumeExecution ? "Recovery plan execution resumed" : "Recovery plan execution started"
				);
			}
		} catch (error) {
			if (isMountedRef.current) {
				setIsExecuting(false);
				setError(error instanceof Error ? error.message : "Failed to execute recovery plan");
				toast.error("Failed to execute recovery plan");
			}
		}
	};

	const resumeExecution = async (checkpointId: string, datacenterId?: string) => {
		if (!planId) {
			setError("No recovery plan selected");
			return;
		}

		try {
			if (isMountedRef.current) {
				setIsExecuting(true);
				setError(null);
			}

			// Get the recovery plan to get the datacenter ID if not provided
			let effectiveDatacenterId = datacenterId;
			if (!effectiveDatacenterId) {
				try {
					// Try to get the datacenter ID from the plan metadata
					const { data: plan } = await supabase
						.from("recovery_plans_new")
						.select("*")
						.eq("id", planId)
						.single();

					effectiveDatacenterId = plan?.execution_metadata?.datacenter_id;
				} catch (error) {
					console.error("Error getting datacenter ID from plan:", error);
				}
			}

			// Call the API to resume execution
			await apiClient.resumeRecoveryPlanExecution(planId, checkpointId, effectiveDatacenterId);

			if (isMountedRef.current) {
				// Don't show a toast here as the caller will handle it
				console.log("Recovery plan execution resumed successfully");
			}
		} catch (error) {
			if (isMountedRef.current) {
				setIsExecuting(false);
				setError(
					error instanceof Error ? error.message : "Failed to resume recovery plan execution"
				);
				// Don't show a toast here as the caller will handle it
				console.error("Failed to resume recovery plan execution:", error);
			}
			throw error; // Re-throw the error so the caller can handle it
		}
	};

	const calculateProgress = (totalSteps: number): number => {
		if (totalSteps === 0) return 0;

		// If plan is completed, always return 100%
		if (planStatus === "COMPLETED") {
			return 100;
		}

		// If all steps are completed, return 100% even if the plan status isn't updated yet
		const allStepsCompleted =
			Object.values(stepStatuses).length === totalSteps &&
			Object.values(stepStatuses).every(
				(status) =>
					status === "COMPLETED" ||
					status === "FAILED" ||
					status === "APPROVED" ||
					status === "REJECTED"
			);

		if (allStepsCompleted) {
			return 100;
		}

		const completedSteps = Object.values(stepStatuses).filter(
			(status) =>
				status === "COMPLETED" ||
				status === "FAILED" ||
				status === "APPROVED" ||
				status === "REJECTED"
		).length;

		const inProgressSteps = Object.values(stepStatuses).filter(
			(status) => status === "IN_PROGRESS"
		).length;

		const awaitingApprovalSteps = Object.values(stepStatuses).filter(
			(status) => status === "AWAITING_APPROVAL"
		).length;

		// Count awaiting approval as 75% complete for that step
		// Count in-progress as 50% complete for that step
		const progress = Math.round(
			((completedSteps + inProgressSteps * 0.5 + awaitingApprovalSteps * 0.75) / totalSteps) * 100
		);

		// Ensure progress is between 0 and 100
		return Math.min(100, Math.max(0, progress));
	};

	const getCheckpointForStep = (stepId: string) => {
		const checkpointId = Object.keys(checkpoints).find((id) => checkpoints[id].stepId === stepId);

		return checkpointId ? { id: checkpointId, ...checkpoints[checkpointId] } : null;
	};

	return {
		stepStatuses,
		planStatus,
		checkpoints,
		isExecuting,
		error,
		lastEvent,
		isLoadingInitialState,
		executeRecoveryPlan,
		resumeExecution,
		calculateProgress,
		getCheckpointForStep,
		refreshExecutionState: fetchInitialExecutionState,
	};
};
