{"name": "nextdr", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/compute": "^4.12.0", "@google-cloud/iam": "^1.3.1", "@google-cloud/resource-manager": "^5.3.1", "@google-cloud/sql": "^0.20.1", "@google-cloud/storage": "^7.16.0", "@supabase/supabase-js": "^2.49.4", "@types/cookie-parser": "^1.4.8", "@types/cron": "^2.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.1.4", "dotenv": "^16.4.7", "express": "^4.21.2", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.10", "@types/uuid": "^10.0.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}