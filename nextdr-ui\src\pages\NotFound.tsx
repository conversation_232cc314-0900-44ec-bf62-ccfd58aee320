
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen flex flex-col items-center justify-center bg-dr-dark p-6"
    >
      <RefreshCw className="h-20 w-20 text-dr-purple mb-6 rotate-180" />
      <h1 className="text-4xl font-bold mb-2">404</h1>
      <p className="text-xl text-muted-foreground mb-8">The page you're looking for doesn't exist</p>
      <Button asChild className="bg-dr-purple hover:bg-dr-purple-dark">
        <Link to="/">Return to Dashboard</Link>
      </Button>
    </motion.div>
  );
};

export default NotFound;
