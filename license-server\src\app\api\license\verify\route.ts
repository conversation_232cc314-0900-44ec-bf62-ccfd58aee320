import { NextResponse } from "next/server";
import { verifyLicense } from "../../../../lib/utils/license";

export async function POST(request: Request) {
	try {
		const { licenseKey } = await request.json();

		if (!licenseKey) {
			return NextResponse.json({ error: "License key is required" }, { status: 400 });
		}

		const license = await verifyLicense(licenseKey);
		return NextResponse.json(license);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 400 });
	}
}
