import express, { Request, Response, NextFunction } from "express";
import { getStepByToken, handleApprovalDecision } from "../controllers/approvalController";

const router = express.Router();

// Middleware to get step details by token
const getStepByTokenMiddleware = (req: Request, res: Response, next: NextFunction) => {
  getStepByToken(req, res, next).catch(next);
};

// Middleware to handle approval decision
const handleApprovalDecisionMiddleware = (req: Request, res: Response, next: NextFunction) => {
  handleApprovalDecision(req, res, next).catch(next);
};

// Get step details by approval token
router.get("/:token", getStepByTokenMiddleware);

// Process approval decision
router.post("/:token", handleApprovalDecisionMiddleware);

export default router;
