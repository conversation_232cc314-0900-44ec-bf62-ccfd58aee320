import { BaseStepExecutor, ExecutionContext, ExecutionResult } from "./baseStepExecutor";
import { GetGcpClients } from "../../lib/gcpClients";

/**
 * Executor for VM restore operations
 */
export class VMRestoreExecutor extends BaseStepExecutor {
	constructor() {
		super("Restore virtual machine");
	}

	async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
		this.log(`Starting VM restore for step: ${step.name}`);

		try {
			this.validate(step);
			const config = this.parseConfiguration(step);

			// Validate required configuration
			this.validateRequiredConfig(config, ["instanceName", "snapshotName"]);

			const { instanceName, snapshotName, zone, machineType, networkTags } = config;

			this.log(`Restoring VM ${instanceName} from snapshot ${snapshotName}`);

			// Get GCP clients
			const clients = await GetGcpClients(context.datacenterId);
			if (!clients) {
				throw new Error("Failed to get GCP clients");
			}

			// Check if instance already exists
			const existingInstance = await this.checkInstanceExists(clients, instanceName, zone);
			if (existingInstance) {
				this.log(`Instance ${instanceName} already exists, skipping restore`, "warn");
				return this.createSuccessResult(`VM ${instanceName} already exists`, {
					instanceName,
					status: "already_exists",
				});
			}

			// Get snapshot details
			const snapshot = await this.getSnapshot(clients, snapshotName);
			if (!snapshot) {
				throw new Error(`Snapshot ${snapshotName} not found`);
			}

			// Create instance from snapshot
			const operation = await this.createInstanceFromSnapshot(
				clients,
				instanceName,
				snapshotName,
				zone,
				machineType,
				networkTags
			);

			// Wait for operation to complete
			await this.waitForOperation(clients, operation, zone);

			// Verify instance is running
			const instance = await this.getInstance(clients, instanceName, zone);
			if (!instance || instance.status !== "RUNNING") {
				throw new Error(`Instance ${instanceName} is not running after restore`);
			}

			this.log(`Successfully restored VM ${instanceName}`);

			return this.createSuccessResult(`VM ${instanceName} restored successfully`, {
				instanceName,
				zone,
				status: instance.status,
				machineType: instance.machineType,
				createdAt: new Date().toISOString(),
			});
		} catch (error: any) {
			this.log(`VM restore failed: ${error.message}`, "error");
			return this.createErrorResult(`Failed to restore VM: ${error.message}`, error.message);
		}
	}

	/**
	 * Check if instance exists
	 */
	private async checkInstanceExists(
		clients: any,
		instanceName: string,
		zone: string
	): Promise<boolean> {
		try {
			const instance = await this.getInstance(clients, instanceName, zone);
			return !!instance;
		} catch (error) {
			return false;
		}
	}

	/**
	 * Get instance details
	 */
	private async getInstance(clients: any, instanceName: string, zone: string): Promise<any> {
		try {
			const [instance] = await clients.compute.instances.get({
				project: clients.projectId,
				zone,
				instance: instanceName,
			});
			return instance;
		} catch (error: any) {
			if (error.code === 404) {
				return null;
			}
			throw error;
		}
	}

	/**
	 * Get snapshot details
	 */
	private async getSnapshot(clients: any, snapshotName: string): Promise<any> {
		try {
			const [snapshot] = await clients.compute.snapshots.get({
				project: clients.projectId,
				snapshot: snapshotName,
			});
			return snapshot;
		} catch (error: any) {
			if (error.code === 404) {
				return null;
			}
			throw error;
		}
	}

	/**
	 * Create instance from snapshot
	 */
	private async createInstanceFromSnapshot(
		clients: any,
		instanceName: string,
		snapshotName: string,
		zone: string,
		machineType?: string,
		networkTags?: string[]
	): Promise<any> {
		interface InstanceConfig {
			name: string;
			machineType: string;
			disks: {
				boot: boolean;
				autoDelete: boolean;
				initializeParams: {
					sourceSnapshot: string;
				};
			}[];
			networkInterfaces: {
				network: string;
				accessConfigs: {
					type: string;
					name: string;
				}[];
			}[];
			tags?: { items: string[] };
		}

		const instanceConfig: InstanceConfig = {
			name: instanceName,
			machineType: `zones/${zone}/machineTypes/${machineType || "e2-medium"}`,
			disks: [
				{
					boot: true,
					autoDelete: true,
					initializeParams: {
						sourceSnapshot: `projects/${clients.projectId}/global/snapshots/${snapshotName}`,
					},
				},
			],
			networkInterfaces: [
				{
					network: "global/networks/default",
					accessConfigs: [
						{
							type: "ONE_TO_ONE_NAT",
							name: "External NAT",
						},
					],
				},
			],
		};

		// Add network tags if specified
		if (networkTags && networkTags.length > 0) {
			instanceConfig.tags = { items: networkTags };
		}

		const [operation] = await clients.compute.instances.insert({
			project: clients.projectId,
			zone,
			requestBody: instanceConfig,
		});

		return operation;
	}

	/**
	 * Wait for operation to complete
	 */
	private async waitForOperation(clients: any, operation: any, zone: string): Promise<void> {
		const maxWaitTime = 10 * 60 * 1000; // 10 minutes
		const pollInterval = 5000; // 5 seconds
		const startTime = Date.now();

		while (Date.now() - startTime < maxWaitTime) {
			const [currentOp] = await clients.compute.zoneOperations.get({
				project: clients.projectId,
				zone,
				operation: operation.name,
			});

			if (currentOp.status === "DONE") {
				if (currentOp.error) {
					throw new Error(`Operation failed: ${JSON.stringify(currentOp.error)}`);
				}
				return;
			}

			this.log(`Operation ${operation.name} status: ${currentOp.status}`);
			await this.sleep(pollInterval);
		}

		throw new Error(`Operation ${operation.name} timed out`);
	}

	validate(step: any): boolean {
		super.validate(step);

		if (step.operation_type !== "Restore virtual machine") {
			throw new Error(`Invalid operation type for VM restore: ${step.operation_type}`);
		}

		return true;
	}
}
