import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../../supabase-client";
import { toast } from "@/components/ui/sonner";
import * as apiClient from "../api-client";
import { SupabaseSnapshot } from "@/lib/types";

export const useVMSnapshots = (instanceName: string) => {
  return useQuery({
    queryKey: ["vmSnapshots", instanceName],
    queryFn: async () => {
      try {
        return await apiClient.getVMSnapshots(instanceName);
      } catch (error: any) {
        throw new Error(error.message || "Failed to fetch VM snapshots");
      }
    },
    enabled: !!instanceName,
  });
};

export const useSnapshots = (
  instanceName: string,
  datacenterId: string,
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: ["snapshots", instanceName, datacenterId],
    queryFn: async () => {
      try {
        return await apiClient.getSnapshots(instanceName, datacenterId);
      } catch (error: any) {
        throw new Error(error.message || "Failed to fetch snapshots");
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled : !!instanceName && !!datacenterId,
  });
};

export const useGroupSnapshots = (groupId: string) => {
  return useQuery({
    queryKey: ["groupSnapshots", groupId],
    queryFn: async () => {
      try {
        const { data: schedules, error }: { data: SupabaseSnapshot[]; error: any } = await supabase
          .from("snapshot_schedules")
          .select()
          .eq("group_id", groupId);

        if (error) {
          throw new Error(error.message);
        }

        return schedules;
      } catch (error: any) {
        throw new Error(error.message || "Failed to fetch group snapshots");
      }
    },
    enabled: !!groupId,
  });
};

export const useAddSnapshotSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (schedule: Omit<SupabaseSnapshot, "id" | "created_at">) => {
      console.log("Creating snapshot schedule:", schedule);

      const { data, error } = await supabase
        .from("snapshot_schedules")
        .insert(schedule)
        .select()
        .single();

      if (error) {
        console.error("Error creating snapshot schedule:", error);
        throw new Error(error.message);
      }

      console.log("Snapshot schedule created successfully:", data);
      return data as SupabaseSnapshot;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["groupSnapshots", data.group_id] });
      toast.success("Snapshot schedule created successfully");
    },
    onError: (error) => {
      console.error("Mutation error:", error);
      toast.error(`Failed to create snapshot schedule: ${error.message}`);
    },
  });
};

export const useUpdateSnapshot = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (snapshot: SupabaseSnapshot) => {
      console.log("Updating snapshot:", snapshot);

      if (!snapshot.id || !snapshot.group_id) {
        throw new Error("Missing required fields for snapshot update");
      }

      const { data, error } = await supabase
        .from("snapshot_schedules")
        .update({
          frequency: snapshot.frequency,
          retention_period: snapshot.retention_period,
          start_time: snapshot.start_time,
          vm_ids: snapshot.vm_ids,
          status: snapshot.status,
          day_of_week: snapshot.day_of_week,
          day_of_month: snapshot.day_of_month,
        })
        .eq("id", snapshot.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating snapshot:", error);
        throw new Error(error.message);
      }

      console.log("Snapshot updated successfully:", data);
      return data as SupabaseSnapshot;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["groupSnapshots", data.group_id] });
      toast.success("Snapshot schedule updated successfully");
    },
    onError: (error) => {
      console.error("Mutation error:", error);
      toast.error(`Failed to update snapshot schedule: ${error.message}`);
    },
  });
};

export const useAddBackupSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (schedule: Omit<SupabaseSnapshot, "id" | "created_at">) => {
      const response = await apiClient.createBackup(
        schedule.vm_ids[0],
        schedule.frequency,
        schedule.retention_period.toString(),
        schedule.datacenter_id
      );

      return response.data;
    },
  });
};
