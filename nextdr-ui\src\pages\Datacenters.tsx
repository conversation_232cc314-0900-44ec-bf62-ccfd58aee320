import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Plus, Database, Trash2, Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";
import { Datacenter, VM } from "@/lib/types";
import { useModalStore } from "@/lib/store/useStore";
import DatacenterModal from "@/components/datacenters/DatacenterModal";
import { useDatacenters, useDeleteDatacenter } from "@/lib/api/hooks/datacenters";
import { useVMsInZone } from "@/lib/api/hooks/vms";

const DatacentersPage = () => {
	const { onOpen } = useModalStore();
	const { data: datacenters = [], isLoading: isLoadingDatacenters } = useDatacenters();
	const [selectedDatacenter, setSelectedDatacenter] = useState<Datacenter | null>(null);

	const { data: vmData, isLoading: isLoadingVMData } = useVMsInZone(
		selectedDatacenter?.zone || "us-central1-c",
		selectedDatacenter?.project_id || "",
		selectedDatacenter?.id || "",
		{
			enabled: !!selectedDatacenter?.project_id && !!selectedDatacenter?.id,
		}
	);

	const { mutate: deleteDatacenter } = useDeleteDatacenter();

	const handleAddDatacenter = () => {
		onOpen("datacenter-form");
	};

	const handleSelectDatacenter = (datacenter: Datacenter) => {
		setSelectedDatacenter(datacenter);
	};

	// Animation variants
	const container = {
		hidden: { opacity: 0 },
		show: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1,
			},
		},
	};

	const item = {
		hidden: { opacity: 0, y: 20 },
		show: { opacity: 1, y: 0, transition: { duration: 0.4 } },
	};

	// Get hypervisor type icon
	const getHypervisorIcon = (type: string) => {
		switch (type) {
			case "GCP":
				return "🌐";
			case "AWS":
				return "☁️";
			case "VMware":
				return "💾";
			case "Proxmox":
				return "🖥️";
			default:
				return "🔌";
		}
	};

	// Get status badge
	const getStatusBadge = (status?: string) => {
		switch (status) {
			case "connected":
				return (
					<span className="px-2 py-1 text-xs bg-green-900/20 text-green-500 rounded-full">
						Connected
					</span>
				);
			case "disconnected":
				return (
					<span className="px-2 py-1 text-xs bg-amber-900/20 text-amber-500 rounded-full">
						Disconnected
					</span>
				);
			case "error":
				return (
					<span className="px-2 py-1 text-xs bg-red-900/20 text-red-500 rounded-full">Error</span>
				);
			default:
				return (
					<span className="px-2 py-1 text-xs bg-gray-900/20 text-gray-500 rounded-full">
						Unknown
					</span>
				);
		}
	};

	return (
		<PageLayout title="Datacenters">
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-2xl font-bold">Datacenters</h1>
					<p className="text-muted-foreground">Manage your hypervisor connections</p>
				</div>
				<Button onClick={handleAddDatacenter} className="bg-dr-purple hover:bg-dr-purple-dark">
					<Plus className="mr-2 h-4 w-4" />
					Add Datacenter
				</Button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
				{/* Datacenter List */}
				<motion.div
					variants={container}
					initial="hidden"
					animate="show"
					className="lg:col-span-1 space-y-4"
				>
					{isLoadingDatacenters ? (
						<div className="flex justify-center items-center py-8">
							<Loader2 className="h-6 w-6 animate-spin mr-2" />
							<span>Loading datacenters...</span>
						</div>
					) : datacenters.length > 0 ? (
						datacenters.map((dc) => (
							<motion.div
								key={dc.id}
								variants={item}
								onClick={() => handleSelectDatacenter(dc as Datacenter)}
								className={`cursor-pointer transition-colors ${
									selectedDatacenter?.id === dc.id ? "border-dr-purple" : "border-border"
								}`}
							>
								<Card
									className={`overflow-hidden ${
										selectedDatacenter?.id === dc.id
											? "border-dr-purple bg-secondary/50"
											: "bg-card"
									}`}
								>
									<CardContent className="p-4">
										<div className="flex items-center">
											<div className="flex-shrink-0 mr-4 text-2xl">
												{getHypervisorIcon(dc.hypervisor_type)}
											</div>
											<div className="flex-1">
												<h3 className="font-medium">{dc.project_id}</h3>
												<div className="text-xs text-muted-foreground flex items-center">
													<span className="mr-2">{dc.hypervisor_type}</span>
													{/* {getStatusBadge(dc.status)} */}
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						))
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<p>No datacenters found</p>
							<Button onClick={handleAddDatacenter} variant="outline" size="sm" className="mt-2">
								Add Datacenter
							</Button>
						</div>
					)}
				</motion.div>

				{/* Datacenter Details */}
				<div className="lg:col-span-3">
					{selectedDatacenter ? (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
						>
							<Card>
								<CardContent className="p-6">
									<div className="flex justify-between items-center mb-4">
										<div className="flex items-center">
											<div className="p-2 rounded-full bg-dr-purple/20 mr-4">
												<Database className="h-5 w-5 text-dr-purple" />
											</div>
											<div>
												<h2 className="text-xl font-bold">{selectedDatacenter.name}</h2>
												<p className="text-sm text-muted-foreground">
													{selectedDatacenter.hypervisor_type}
													{selectedDatacenter.project_id &&
														` • Project: ${selectedDatacenter.project_id}`}
												</p>
											</div>
										</div>
										<div className="flex items-center space-x-2">
											<Button
												variant="destructive"
												size="sm"
												onClick={() => deleteDatacenter(selectedDatacenter?.id || "")}
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										</div>
									</div>

									{/* VM List */}
									<div className="mt-6">
										<h3 className="text-lg font-medium mb-4">Virtual Machines</h3>
										<div className="bg-secondary rounded-md overflow-hidden">
											<table className="w-full">
												<thead>
													<tr className="border-b border-border">
														<th className="text-left py-3 px-4 font-medium text-sm">Name</th>
														<th className="text-left py-3 px-4 font-medium text-sm">Status</th>
														<th className="text-left py-3 px-4 font-medium text-sm">
															Machine Type
														</th>
														<th className="text-left py-3 px-4 font-medium text-sm">Project</th>
														{/* <th className="text-left py-3 px-4 font-medium text-sm">IP Address</th> */}
													</tr>
												</thead>
												<tbody>
													{isLoadingVMData ? (
														<tr>
															<td colSpan={5} className="py-8 text-center">
																<div className="flex justify-center items-center">
																	<Loader2 className="h-5 w-5 animate-spin mr-2" />
																	<span>Loading VMs...</span>
																</div>
															</td>
														</tr>
													) : vmData && vmData.length > 0 ? (
														vmData.map((vm) => (
															<tr key={vm.name} className="border-b border-border">
																<td className="py-3 px-4 text-sm">{vm.name}</td>
																<td className="py-3 px-4 text-sm">
																	<span
																		className={`px-2 py-1 text-xs rounded-full ${
																			vm.status === "RUNNING"
																				? "bg-green-900/20 text-green-500"
																				: "bg-gray-900/20 text-gray-500"
																		}`}
																	>
																		{vm.status}
																	</span>
																</td>
																<td className="py-3 px-4 text-sm">
																	{vm.machineType?.split("/").pop()?.replace("custom-", "") || "-"}
																</td>
																<td className="py-3 px-4 text-sm">{vm.projectId}</td>
															</tr>
														))
													) : (
														<tr>
															<td colSpan={5} className="py-8 text-center text-muted-foreground">
																No VMs found in this datacenter
															</td>
														</tr>
													)}
												</tbody>
											</table>
										</div>
									</div>
								</CardContent>
							</Card>
						</motion.div>
					) : (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.4 }}
							className="h-full flex items-center justify-center"
						>
							<div className="text-center">
								<Database className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
								<h3 className="text-lg font-medium mb-1">No datacenter selected</h3>
								<p className="text-muted-foreground mb-4">Select a datacenter to view details</p>
								<Button
									onClick={handleAddDatacenter}
									className="bg-dr-purple hover:bg-dr-purple-dark"
								>
									Add Datacenter
								</Button>
							</div>
						</motion.div>
					)}
				</div>
			</div>

			{/* Datacenter Modal */}
			<DatacenterModal />
		</PageLayout>
	);
};

export default DatacentersPage;
