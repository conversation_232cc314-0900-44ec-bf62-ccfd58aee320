import { Request, Response, NextFunction } from "express";
import { processApproval, validateApprovalToken } from "../services/approvalService";
import { supabase } from "../services/supabaseService";
import { sendStepUpdate, sendPlanUpdate } from "../routes/sseManager/sseManager";
import { canApproveActions } from "../middleware/rbac";

/**
 * Get approval details by token
 */
export const getStepByToken = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { token } = req.params;

		if (!token) {
			res.status(400).json({ error: "Token is required" });
			return;
		}

		// Validate token
		const { data: tokenData, error: tokenError } = await supabase
			.from("approval_tokens")
			.select("*")
			.eq("token", token)
			.single();

		if (tokenError || !tokenData) {
			res.status(404).json({ error: "Invalid or expired approval token" });
			return;
		}

		// Check if token is expired
		if (new Date(tokenData.expires_at) < new Date()) {
			res.status(400).json({ error: "Approval token has expired" });
			return;
		}

		// Check if token has already been used
		if (tokenData.is_used) {
			res.status(400).json({ error: "This approval token has already been used" });
			return;
		}

		// Check if this is a checkpoint-based approval
		if (tokenData.is_checkpoint && tokenData.checkpoint_id) {
			// Get checkpoint details
			const { data: checkpoint, error: checkpointError } = await supabase
				.from("recovery_plan_checkpoints")
				.select(
					`
          *,
          recovery_steps_new!inner(
            id,
            name,
            description,
            operation_type,
            step_order,
            recovery_plan_id
          ),
          recovery_plans_new!inner(
            id,
            name,
            description
          )
        `
				)
				.eq("id", tokenData.checkpoint_id)
				.single();

			if (checkpointError || !checkpoint) {
				res.status(404).json({ error: "Failed to get checkpoint details" });
				return;
			}

			// Return checkpoint details with step and plan info
			res.status(200).json({
				type: "checkpoint",
				checkpoint,
				step: checkpoint.recovery_steps_new,
				plan: checkpoint.recovery_plans_new,
				token: tokenData,
			});
			return;
		} else {
			// Legacy flow - get step details directly
			const { data: step, error: stepError } = await supabase
				.from("recovery_steps_new")
				.select(
					`
          *,
          recovery_plans_new!inner(
            id,
            name,
            description
          )
        `
				)
				.eq("id", tokenData.step_id)
				.single();

			if (stepError || !step) {
				res.status(404).json({ error: "Failed to get step details" });
				return;
			}

			// Return step details
			res.status(200).json({
				type: "step",
				step,
				plan: step.recovery_plans_new,
				token: tokenData,
			});
		}
	} catch (error: any) {
		console.error("Error getting approval details by token:", error);
		res.status(500).json({ error: error.message || "Internal server error" });
	}
};

/**
 * Process approval decision
 */
export const handleApprovalDecision = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { token } = req.params;
		const { decision, comment } = req.body;

		if (!token) {
			res.status(400).json({ error: "Token is required" });
			return;
		}

		if (!decision || (decision !== "approved" && decision !== "rejected")) {
			res.status(400).json({ error: "Valid decision (approved/rejected) is required" });
			return;
		}

		try {
			// Validate the token with more robust error handling
			console.log(`Validating approval token: ${token}`);

			let tokenData;
			try {
				tokenData = await validateApprovalToken(token);
				console.log(`Token validated successfully for step: ${tokenData.step_id}`);
			} catch (validationError: any) {
				console.error(`Error validating approval token: ${validationError.message}`);

				// Check if this is a connection error
				if (validationError.message?.includes("fetch failed")) {
					// For connection errors, we'll use a more user-friendly message
					res.status(503).json({
						error:
							"Unable to validate approval token due to a connection issue. Please try again later.",
						details: validationError.message,
					});
				} else {
					// For other validation errors
					res.status(400).json({
						error: `Invalid approval token: ${validationError.message}`,
						details: validationError.stack,
					});
				}
				return;
			}

			// If the request is coming from an authenticated user (not email link)
			if (req.user?.id) {
				console.log(`Request from authenticated user: ${req.user.id}, role: ${req.user.role}`);
				// Check if user has approval permission
				const userRole = req.user.role;
				if (!userRole || !["admin", "approver"].includes(userRole)) {
					res.status(403).json({
						error:
							"Insufficient permissions to approve recovery steps. Required role: admin or approver",
					});
					return;
				}

				// Check if this user is the expected approver or has admin role
				if (tokenData.approver_id !== req.user.id && userRole !== "admin") {
					res.status(403).json({
						error:
							"You are not authorized to approve this step. Only the assigned approver or an admin can approve it.",
					});
					return;
				}
			} else {
				// For email-based approvals (no authenticated user)
				console.log(`Processing email-based approval for token: ${token}`);
			}
		} catch (error: any) {
			console.error(`Unexpected error in approval controller: ${error.message}`, error);
			res.status(500).json({
				error: "An unexpected error occurred while processing your approval request.",
				details: error.message,
			});
			return;
		}

		// Get client IP address and user agent
		const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress || "unknown";
		const userAgent = req.headers["user-agent"] || "unknown";

		// Process approval
		console.log(`Processing approval decision: ${decision} for token ${token}`);

		const result = await processApproval(
			token,
			decision as "approved" | "rejected",
			comment || "",
			clientIp as string,
			userAgent as string,
			req.user?.id // Pass the authenticated user ID if available
		);

		console.log(`Approval processed successfully, result type: ${result.type}`);

		// Send SSE updates based on the type of approval
		if (result.type === "checkpoint") {
			const checkpoint = result.checkpoint;
			console.log(`Handling checkpoint approval for checkpoint ${checkpoint.id}`);

			// Get the step associated with this checkpoint
			const { data: step } = await supabase
				.from("recovery_steps_new")
				.select("*")
				.eq("id", checkpoint.step_id)
				.single();

			if (step) {
				// Send step update
				sendStepUpdate(
					checkpoint.recovery_plan_id,
					step.step_order.toString(),
					decision === "approved" ? "APPROVED" : "REJECTED",
					{
						checkpoint_id: checkpoint.id,
						comment: comment || undefined,
						approved_by: checkpoint.approved_by,
						approved_at: checkpoint.approved_at,
					}
				);

				// Send plan update if approved (execution will resume)
				if (decision === "approved") {
					sendPlanUpdate(checkpoint.recovery_plan_id, "RESUMING", {
						checkpoint_id: checkpoint.id,
						message: `Execution resuming after approval of checkpoint: ${step.name}`,
					});
				} else {
					sendPlanUpdate(checkpoint.recovery_plan_id, "PAUSED", {
						checkpoint_id: checkpoint.id,
						message: `Execution paused due to rejection of checkpoint: ${step.name}`,
					});
				}
			}

			// Return updated checkpoint
			res.status(200).json({
				message: `Checkpoint ${decision} successfully`,
				type: "checkpoint",
				checkpoint: result.checkpoint,
			});
		} else {
			// Legacy flow - step approval
			const step = result.data;

			// Send step update
			sendStepUpdate(
				step.recovery_plan_id,
				step.step_order.toString(),
				decision === "approved" ? "APPROVED" : "REJECTED",
				{
					comment: comment || undefined,
					approved_by: step.approval_metadata?.approved_by,
					approved_at: step.approval_metadata?.approved_at,
				}
			);

			// Return updated step
			res.status(200).json({
				message: `Step ${decision} successfully`,
				type: "step",
				step: result.data,
			});
		}
	} catch (error: any) {
		console.error("Error processing approval decision:", error);
		res.status(500).json({ error: error.message || "Internal server error" });
	}
};
