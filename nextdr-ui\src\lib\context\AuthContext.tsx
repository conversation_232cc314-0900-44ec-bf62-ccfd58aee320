import React, { createContext, useContext, useEffect, useState } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "../supabase-client";
import { useLicenseStore } from "../store/useStore";
import { toast } from "@/components/ui/sonner";

interface AuthContextType {
	user: User | null;
	session: Session | null;
	isLoading: boolean;
	signIn: (
		email: string,
		password: string
	) => Promise<{
		error: string | null;
	}>;
	signUp: (
		email: string,
		password: string
	) => Promise<{
		error: string | null;
	}>;
	signInWithMagicLink: (email: string) => Promise<{
		error: string | null;
	}>;
	signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [user, setUser] = useState<User | null>(null);
	const [session, setSession] = useState<Session | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const { clearLicense } = useLicenseStore();

	useEffect(() => {
		const setData = async () => {
			const {
				data: { session },
				error,
			} = await supabase.auth.getSession();

			if (error) {
				console.error("Error getting session:", error.message);
			}

			setSession(session);
			setUser(session?.user ?? null);
			setIsLoading(false);
		};

		const {
			data: { subscription },
		} = supabase.auth.onAuthStateChange((_event, session) => {
			setSession(session);
			setUser(session?.user ?? null);
			setIsLoading(false);
		});

		setData();

		return () => {
			subscription.unsubscribe();
		};
	}, []);

	const signIn = async (email: string, password: string) => {
		try {
			const { error } = await supabase.auth.signInWithPassword({
				email,
				password,
			});

			if (error) {
				return { error: error.message };
			}

			toast.success("Successfully signed in!");
			return { error: null };
		} catch (error) {
			console.error("Sign in error:", error);
			return { error: "An unexpected error occurred" };
		}
	};

	const signUp = async (email: string, password: string) => {
		try {
			const { error } = await supabase.auth.signUp({
				email,
				password,
			});

			if (error) {
				return { error: error.message };
			}

			toast.success("Successfully signed up! Please check your email for verification.");
			return { error: null };
		} catch (error) {
			console.error("Sign up error:", error);
			return { error: "An unexpected error occurred" };
		}
	};

	const signInWithMagicLink = async (email: string) => {
		try {
			const { error } = await supabase.auth.signInWithOtp({
				email,
				options: {
					emailRedirectTo: window.location.origin,
				},
			});

			if (error) {
				return { error: error.message };
			}

			toast.success("Magic link sent! Please check your email.");
			return { error: null };
		} catch (error) {
			console.error("Magic link error:", error);
			return { error: "An unexpected error occurred" };
		}
	};

	const signOut = async () => {
		await supabase.auth.signOut();
		clearLicense();
		toast.info("You have been signed out");
	};

	return (
		<AuthContext.Provider
			value={{ user, session, isLoading, signIn, signUp, signInWithMagicLink, signOut }}
		>
			{children}
		</AuthContext.Provider>
	);
};

export const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};
