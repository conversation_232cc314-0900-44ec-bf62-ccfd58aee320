import { NextResponse } from "next/server";
import { createClient, isAdmin } from "@/lib/supabase/server";

export async function POST(request: Request) {
	try {
		const isUserAdmin = await isAdmin();

		if (!isUserAdmin) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { customerId, features, expiresAt } = await request.json();

		if (!customerId || !features || !expiresAt) {
			return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
		}

		// For now, return a placeholder response since generateLicenseKey is not available
		const license = {
			id: "temp-" + Date.now(),
			license_key: "TEMP-" + Math.random().toString(36).substr(2, 9).toUpperCase(),
			customer_id: customerId,
			features,
			expires_at: expiresAt,
			status: "active",
			created_at: new Date().toISOString(),
		};

		return NextResponse.json(license);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
