import { UserRole } from "@/types/rbac";

export type IntegrationSource = "native" | "gcp_ad";

export interface IntegrationConfig {
	id: string;
	source: IntegrationSource;
	enabled: boolean;
	last_sync_at: string | null;
	sync_status: "idle" | "syncing" | "error" | "synced" | "in_progress" | "success";
	error_message?: string;
	config: {
		service_account_key: string;
		domain: string;
		sync_interval_minutes: number;
	};
}

export interface SyncStats {
	total_users: number;
	total_groups: number;
	synced_users: number;
	synced_groups: number;
	conflicts: number;
	last_sync_at: string | null;
}

export interface UserProfile {
	id: string;
	email: string;
	role: UserRole;
	created_at: string;
	last_sign_in_at: string | null;
	status: "active" | "invited" | "suspended";
	source: IntegrationSource;
	groups?: {
		id: string;
		name: string;
	}[];
	sync_metadata?: {
		external_id: string;
		last_synced_at: string;
		sync_status: "synced" | "conflict" | "error";
	};
}
