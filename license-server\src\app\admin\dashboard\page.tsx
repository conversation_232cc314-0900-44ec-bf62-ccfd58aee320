"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth/AuthProvider";

export default function DashboardPage() {
	const router = useRouter();
	const { user, loading, signOut } = useAuth();

	useEffect(() => {
		if (!loading) {
			if (!user) {
				router.push("/login?redirectTo=/admin/dashboard");
				return;
			}
		}
	}, [user, loading, router]);

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-xl">Loading...</div>
			</div>
		);
	}

	if (!user) {
		return null; // Will redirect in useEffect
	}

	// Check if user has admin role
	const userRole = user.user_metadata?.role || "customer";
	const isAdmin = userRole === "admin";

	if (!isAdmin) {
		return (
			<div className="min-h-screen bg-gray-100">
				{/* Navigation */}
				<nav className="bg-white shadow-sm">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="flex justify-between h-16">
							<div className="flex">
								<div className="flex-shrink-0 flex items-center">
									<h1 className="text-xl font-bold">License Server</h1>
								</div>
							</div>
							<div className="flex items-center">
								<span className="text-sm text-gray-700 mr-4">Welcome, {user.email}</span>
								<button
									onClick={async () => {
										await signOut();
										router.push("/login");
									}}
									className="ml-4 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
								>
									Sign Out
								</button>
							</div>
						</div>
					</div>
				</nav>

				{/* Access Denied Message */}
				<main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
					<div className="px-4 py-6 sm:px-0">
						<div className="bg-red-50 border border-red-200 rounded-md p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
										<path
											fillRule="evenodd"
											d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<h3 className="text-sm font-medium text-red-800">Access Denied</h3>
									<div className="mt-2 text-sm text-red-700">
										<p>
											You don't have admin privileges to access this dashboard. Your current role
											is: <strong>{userRole}</strong>
										</p>
										<p className="mt-2">
											Please contact an administrator to get admin access, or{" "}
											<a href="/setup" className="font-medium underline">
												visit the setup page
											</a>{" "}
											to configure admin access.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</main>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-100">
			{/* Navigation */}
			<nav className="bg-white shadow-sm">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between h-16">
						<div className="flex">
							<div className="flex-shrink-0 flex items-center">
								<h1 className="text-xl font-bold">License Server</h1>
							</div>
						</div>
						<div className="flex items-center">
							<span className="text-sm text-gray-700 mr-4">Welcome, {user.email}</span>
							<button
								onClick={async () => {
									await signOut();
									router.push("/login");
								}}
								className="ml-4 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
							>
								Sign Out
							</button>
						</div>
					</div>
				</div>
			</nav>

			{/* Main content */}
			<main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
				<div className="px-4 py-6 sm:px-0">
					<h1 className="text-2xl font-semibold text-gray-900 mb-6">Dashboard</h1>
				</div>
				<div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
					<div className="py-4">
						<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
							{/* Quick Stats */}
							<div className="bg-white overflow-hidden shadow rounded-lg">
								<div className="p-5">
									<div className="flex items-center">
										<div className="flex-shrink-0">
											<svg
												className="h-6 w-6 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth="2"
													d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
												/>
											</svg>
										</div>
										<div className="ml-5 w-0 flex-1">
											<dl>
												<dt className="text-sm font-medium text-gray-500 truncate">
													Total Customers
												</dt>
												<dd className="text-lg font-medium text-gray-900">0</dd>
											</dl>
										</div>
									</div>
								</div>
								<div className="bg-gray-50 px-5 py-3">
									<div className="text-sm">
										<a
											href="/admin/customers"
											className="font-medium text-indigo-600 hover:text-indigo-500"
										>
											View all
										</a>
									</div>
								</div>
							</div>

							{/* Active Licenses */}
							<div className="bg-white overflow-hidden shadow rounded-lg">
								<div className="p-5">
									<div className="flex items-center">
										<div className="flex-shrink-0">
											<svg
												className="h-6 w-6 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth="2"
													d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
												/>
											</svg>
										</div>
										<div className="ml-5 w-0 flex-1">
											<dl>
												<dt className="text-sm font-medium text-gray-500 truncate">
													Active Licenses
												</dt>
												<dd className="text-lg font-medium text-gray-900">0</dd>
											</dl>
										</div>
									</div>
								</div>
								<div className="bg-gray-50 px-5 py-3">
									<div className="text-sm">
										<a
											href="/admin/licenses"
											className="font-medium text-indigo-600 hover:text-indigo-500"
										>
											View all
										</a>
									</div>
								</div>
							</div>

							{/* Expired Licenses */}
							<div className="bg-white overflow-hidden shadow rounded-lg">
								<div className="p-5">
									<div className="flex items-center">
										<div className="flex-shrink-0">
											<svg
												className="h-6 w-6 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth="2"
													d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
												/>
											</svg>
										</div>
										<div className="ml-5 w-0 flex-1">
											<dl>
												<dt className="text-sm font-medium text-gray-500 truncate">
													Expired Licenses
												</dt>
												<dd className="text-lg font-medium text-gray-900">0</dd>
											</dl>
										</div>
									</div>
								</div>
								<div className="bg-gray-50 px-5 py-3">
									<div className="text-sm">
										<a
											href="/admin/licenses"
											className="font-medium text-indigo-600 hover:text-indigo-500"
										>
											View all
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	);
}
