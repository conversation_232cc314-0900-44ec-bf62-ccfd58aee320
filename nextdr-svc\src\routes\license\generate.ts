import { Router, RequestH<PERSON><PERSON>, Request, Response } from "express";
import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";
import { supabase } from "../../services/supabaseService";

const router = Router();

const generateLicense: RequestHandler = async (req, res): Promise<any> => {
	try {
		const { customerId, exp } = req.body;
		if (!customerId || !exp) {
			return res.status(400).json({ error: "Missing required fields: customerId and exp" });
		}

		// Validate expiration date is in the future
		if (exp <= Math.floor(Date.now() / 1000)) {
			return res.status(400).json({ error: "Expiration date must be in the future" });
		}

		// Read private key
		const privateKeyPath = path.join(__dirname, "../../../keys/private.key");

		if (!fs.existsSync(privateKeyPath)) {
			return res.status(500).json({ error: "License private key not found" });
		}

		const privateKey = fs.readFileSync(privateKeyPath, "utf8");

		// Create JWT payload
		const payload = {
			sub: customerId,
			exp,
		};

		// Sign the JWT with RS256 algorithm
		const token = jwt.sign(payload, privateKey, { algorithm: "RS256" });

		// Store in Supabase
		const { data, error } = await supabase
			.from("licenses")
			.insert({
				customer_id: customerId,
				jwt: token,
			})
			.select()
			.single();

		if (error) {
			console.error("Error storing license:", error);
			return res.status(500).json({ error: "Failed to store license" });
		}

		// Return the token
		return res.status(201).json({ token });
	} catch (error) {
		console.error("License generation error:", error);
		return res.status(500).json({ error: "Failed to generate license" });
	}
};

router.post("/generate", generateLicense);

export default router;
