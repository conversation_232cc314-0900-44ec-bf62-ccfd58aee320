import { supabase } from "./supabaseService";
import { sendStepUpdate, sendPlanUpdate } from "../routes/sseManager/sseManager";

export interface StepProgress {
    stepId: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'awaiting_approval' | 'approved' | 'rejected';
    startedAt?: Date;
    completedAt?: Date;
    metadata?: any;
}

export interface ExecutionProgress {
    planId: string;
    executionId: string;
    totalSteps: number;
    completedSteps: number;
    failedSteps: number;
    pendingSteps: number;
    currentStep?: number;
    overallStatus: 'not_started' | 'in_progress' | 'paused' | 'completed' | 'failed';
    progressPercentage: number;
    steps: Map<string, StepProgress>;
}

/**
 * Progress tracking service for recovery plan executions
 * Handles step-level and plan-level progress tracking with persistence
 */
export class ProgressTracker {
    private executions: Map<string, ExecutionProgress> = new Map();

    /**
     * Initialize execution progress tracking
     */
    async initializeExecution(planId: string, executionId: string, steps: any[]): Promise<void> {
        console.log(`Initializing progress tracking for execution ${executionId}`);

        // Create progress records for all steps
        for (const step of steps) {
            await this.createStepProgress(planId, step.id, executionId);
        }

        // Initialize in-memory tracking
        const execution: ExecutionProgress = {
            planId,
            executionId,
            totalSteps: steps.length,
            completedSteps: 0,
            failedSteps: 0,
            pendingSteps: steps.length,
            currentStep: 0,
            overallStatus: 'in_progress',
            progressPercentage: 0,
            steps: new Map()
        };

        // Initialize step progress
        steps.forEach((step, index) => {
            execution.steps.set(step.id, {
                stepId: step.id,
                status: 'pending'
            });
        });

        this.executions.set(executionId, execution);

        // Send initial progress update
        this.sendProgressUpdate(execution);
    }

    /**
     * Update step status and progress
     */
    async updateStepStatus(
        planId: string, 
        stepId: string, 
        executionId: string, 
        status: string, 
        metadata: any = {}
    ): Promise<void> {
        console.log(`Updating step ${stepId} status to ${status}`);

        // Update database
        await this.updateStepProgressInDB(planId, stepId, executionId, status, metadata);

        // Update in-memory tracking
        const execution = this.executions.get(executionId);
        if (execution) {
            const stepProgress = execution.steps.get(stepId);
            if (stepProgress) {
                const oldStatus = stepProgress.status;
                stepProgress.status = status as any;
                stepProgress.metadata = { ...stepProgress.metadata, ...metadata };

                if (status === 'in_progress' && oldStatus === 'pending') {
                    stepProgress.startedAt = new Date();
                } else if (['completed', 'failed', 'approved', 'rejected'].includes(status)) {
                    stepProgress.completedAt = new Date();
                }

                // Update counters
                this.updateExecutionCounters(execution, oldStatus, status);
                
                // Calculate progress percentage
                execution.progressPercentage = this.calculateProgressPercentage(execution);

                // Send progress update
                this.sendProgressUpdate(execution);
            }
        }
    }

    /**
     * Get execution progress
     */
    getExecutionProgress(executionId: string): ExecutionProgress | undefined {
        return this.executions.get(executionId);
    }

    /**
     * Load execution progress from database
     */
    async loadExecutionProgress(planId: string, executionId: string): Promise<ExecutionProgress | null> {
        try {
            // Get plan details
            const { data: plan, error: planError } = await supabase
                .from("recovery_plans_new")
                .select("*")
                .eq("id", planId)
                .single();

            if (planError || !plan) {
                console.error("Error loading plan:", planError);
                return null;
            }

            // Get steps
            const { data: steps, error: stepsError } = await supabase
                .from("recovery_steps_new")
                .select("*")
                .eq("recovery_plan_id", planId)
                .order("step_order", { ascending: true });

            if (stepsError || !steps) {
                console.error("Error loading steps:", stepsError);
                return null;
            }

            // Get progress records
            const { data: progressRecords, error: progressError } = await supabase
                .from("recovery_plan_progress")
                .select("*")
                .eq("recovery_plan_id", planId)
                .eq("execution_metadata->>execution_id", executionId);

            if (progressError) {
                console.error("Error loading progress:", progressError);
                return null;
            }

            // Build execution progress
            const execution: ExecutionProgress = {
                planId,
                executionId,
                totalSteps: steps.length,
                completedSteps: 0,
                failedSteps: 0,
                pendingSteps: 0,
                overallStatus: plan.execution_status || 'not_started',
                progressPercentage: 0,
                steps: new Map()
            };

            // Process each step
            steps.forEach((step, index) => {
                const progressRecord = progressRecords?.find(p => p.step_id === step.id);
                const status = progressRecord?.status || 'pending';
                
                execution.steps.set(step.id, {
                    stepId: step.id,
                    status: status as any,
                    startedAt: progressRecord?.started_at ? new Date(progressRecord.started_at) : undefined,
                    completedAt: progressRecord?.completed_at ? new Date(progressRecord.completed_at) : undefined,
                    metadata: progressRecord?.execution_metadata || {}
                });

                // Update counters
                this.incrementCounter(execution, status);
            });

            // Calculate progress
            execution.progressPercentage = this.calculateProgressPercentage(execution);

            // Store in memory
            this.executions.set(executionId, execution);

            return execution;

        } catch (error) {
            console.error("Error loading execution progress:", error);
            return null;
        }
    }

    /**
     * Create step progress record in database
     */
    private async createStepProgress(planId: string, stepId: string, executionId: string): Promise<void> {
        const { error } = await supabase
            .from("recovery_plan_progress")
            .upsert({
                recovery_plan_id: planId,
                step_id: stepId,
                status: 'pending',
                execution_metadata: {
                    execution_id: executionId,
                    created_at: new Date().toISOString()
                }
            }, {
                onConflict: 'recovery_plan_id,step_id'
            });

        if (error) {
            console.error("Error creating step progress:", error);
            throw new Error(`Failed to create step progress: ${error.message}`);
        }
    }

    /**
     * Update step progress in database
     */
    private async updateStepProgressInDB(
        planId: string, 
        stepId: string, 
        executionId: string, 
        status: string, 
        metadata: any = {}
    ): Promise<void> {
        const updateData: any = {
            status,
            execution_metadata: {
                execution_id: executionId,
                updated_at: new Date().toISOString(),
                ...metadata
            }
        };

        if (status === 'in_progress') {
            updateData.started_at = new Date().toISOString();
        } else if (['completed', 'failed', 'approved', 'rejected'].includes(status)) {
            updateData.completed_at = new Date().toISOString();
        }

        const { error } = await supabase
            .from("recovery_plan_progress")
            .update(updateData)
            .eq("recovery_plan_id", planId)
            .eq("step_id", stepId);

        if (error) {
            console.error("Error updating step progress:", error);
            throw new Error(`Failed to update step progress: ${error.message}`);
        }
    }

    /**
     * Update execution counters
     */
    private updateExecutionCounters(execution: ExecutionProgress, oldStatus: string, newStatus: string): void {
        // Decrement old status counter
        this.decrementCounter(execution, oldStatus);
        
        // Increment new status counter
        this.incrementCounter(execution, newStatus);
    }

    /**
     * Increment counter for status
     */
    private incrementCounter(execution: ExecutionProgress, status: string): void {
        switch (status) {
            case 'completed':
            case 'approved':
                execution.completedSteps++;
                break;
            case 'failed':
            case 'rejected':
                execution.failedSteps++;
                break;
            case 'pending':
            case 'awaiting_approval':
                execution.pendingSteps++;
                break;
        }
    }

    /**
     * Decrement counter for status
     */
    private decrementCounter(execution: ExecutionProgress, status: string): void {
        switch (status) {
            case 'completed':
            case 'approved':
                execution.completedSteps = Math.max(0, execution.completedSteps - 1);
                break;
            case 'failed':
            case 'rejected':
                execution.failedSteps = Math.max(0, execution.failedSteps - 1);
                break;
            case 'pending':
            case 'awaiting_approval':
                execution.pendingSteps = Math.max(0, execution.pendingSteps - 1);
                break;
        }
    }

    /**
     * Calculate progress percentage
     */
    private calculateProgressPercentage(execution: ExecutionProgress): number {
        if (execution.totalSteps === 0) return 0;
        return Math.round((execution.completedSteps / execution.totalSteps) * 100);
    }

    /**
     * Send progress update via SSE
     */
    private sendProgressUpdate(execution: ExecutionProgress): void {
        sendPlanUpdate(execution.planId, "PROGRESS_UPDATE", {
            execution_id: execution.executionId,
            total_steps: execution.totalSteps,
            completed_steps: execution.completedSteps,
            failed_steps: execution.failedSteps,
            pending_steps: execution.pendingSteps,
            progress_percentage: execution.progressPercentage,
            overall_status: execution.overallStatus
        });
    }

    /**
     * Clean up completed executions
     */
    cleanup(executionId: string): void {
        this.executions.delete(executionId);
        console.log(`Cleaned up progress tracking for execution ${executionId}`);
    }
}
