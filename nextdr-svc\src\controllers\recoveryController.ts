import { Request, RequestHandler, Response } from "express";
import { supabase } from "../services/supabaseService";
import { RecoverySteps, VMSnapshotRecord } from "../models/gcp_backup";
import { getRecoverySteps, updateRecoveryStepStatus as updateRecoveryStepStatusService } from "../services/recoveryStepsService";
import { getRecoveryPlanById } from "../services/recoveryPlanService";
import { getBackupExecutionById } from "../services/backupExecutionService";
import { GoogleAuth, OAuth2Client, JWT } from "google-auth-library";
import { google } from "googleapis";
import { ProjectConfig } from "../models/gcp_backup";
import {
	DisksClient,
	ZoneOperationsClient,
	InstancesClient,
	SnapshotsClient,
} from "@google-cloud/compute";
import { GCPClientSet, GetGcpClients } from "../lib/gcpClients";
import { sendStepUpdate } from "../routes/sseManager/sseManager";

// Function to send plan updates via SSE
const sendPlanUpdate = (planId: string, status: string, metadata?: any) => {
	sendStepUpdate(planId, "plan", status, metadata);
};
import { createApprovalRequest } from "../services/approvalService";
import { v4 as uuidv4 } from "uuid";
import {
	getRecoveryPlanCheckpoints,
	createRecoveryPlanProgress,
	updateRecoveryPlanProgress,
	getRecoveryPlanProgress,
	isCheckpointApproved,
	createRecoveryPlanCheckpoint,
} from "../services/recoveryProgressService";

export const executeRecoveryPlan = async (req: Request, res: Response) => {
	console.log("Executing recovery plan");
	try {
		const { planId } = req.params;
		const { datacenterId } = req.params;
		const { resumeExecution, checkpointId, executionId: providedExecutionId } = req.body || {};

		// Log request details for debugging
		console.log(`Execute recovery plan request:`, {
			planId,
			datacenterId,
			resumeExecution,
			checkpointId,
			providedExecutionId,
			headers: {
				"x-resume-execution": req.headers["x-resume-execution"],
				"content-type": req.headers["content-type"],
			},
		});

		// Authentication is now enforced by the authMiddleware
		const userId = req.user?.id;
		if (!userId) {
			res.status(401).json({ error: "Authentication required" });
			return;
		}

		// Check if user has permission to execute recovery plans
		const userRole = req.user?.role;
		if (!userRole) {
			res.status(403).json({
				error:
					"Insufficient permissions to execute recovery plans. Required role: admin or operator",
			});
			return;
		}

		// If this is a resume request, handle it differently
		if (resumeExecution === true && checkpointId) {
			console.log(`Resuming execution for plan ${planId} after checkpoint ${checkpointId}`);

			try {
				// Get the current execution ID
				const recoveryPlan = await getRecoveryPlanById(planId);
				if (!recoveryPlan) {
					throw new Error("Recovery plan not found");
				}

				const executionId = providedExecutionId || recoveryPlan.current_execution_id;
				if (!executionId) {
					throw new Error("No execution ID found for this plan");
				}

				// Use the executeNextSteps function to continue execution
				await executeNextSteps(planId, executionId, checkpointId, datacenterId);

				res.status(200).json({
					message: "Recovery plan execution resumed successfully",
					planId,
					checkpointId,
				});
				return;
			} catch (error: any) {
				console.error(`Error resuming execution: ${error.message}`);
				res.status(500).json({ error: `Failed to resume execution: ${error.message}` });
				return;
			}
		}

		const recoveryPlan = await getRecoveryPlanById(planId);

		if (!recoveryPlan) {
			res.status(404).json({ error: "Recovery plan not found" });
			return;
		}

		const recoverySteps = await getRecoverySteps(planId);
		if (!recoverySteps) {
			res.status(404).json({ error: "Recovery steps not found" });
			return;
		}

		// Sort steps by order for execution
		const executionOrder = recoverySteps.sort((a, b) => a.step_order - b.step_order);

		// Always create a new execution ID when starting a new execution
		// This ensures we don't reuse data from previous executions
		const executionId = uuidv4();
		const isNewExecution = true;

		console.log(`Creating new execution with ID: ${executionId}`);

		// Update recovery plan with new execution ID
		await supabase
			.from("recovery_plans_new")
			.update({
				current_execution_id: executionId,
				execution_status: "in_progress",
				execution_started_at: new Date().toISOString(),
				execution_metadata: {
					...(recoveryPlan as any).execution_metadata,
					datacenter_id: datacenterId,
				},
			})
			.eq("id", planId);

		// Create a new execution record
		await supabase.from("recovery_plan_executions").insert({
			id: executionId,
			recovery_plan_id: planId,
			status: "in_progress",
			started_at: new Date().toISOString(),
			datacenter_id: datacenterId,
			created_by: userId,
		});

		// Reset all checkpoints for this plan to ensure we don't reuse old approvals
		console.log(`Resetting all checkpoints for plan ${planId}`);

		// First, get all existing checkpoints to access their approval_metadata
		const { data: existingCheckpoints, error: checkpointsError } = await supabase
			.from("recovery_plan_checkpoints")
			.select("*")
			.eq("recovery_plan_id", planId);

		if (checkpointsError) {
			console.error("Error fetching existing checkpoints:", checkpointsError);
			throw new Error(`Failed to fetch existing checkpoints: ${checkpointsError.message}`);
		}

		// Update each checkpoint individually to properly handle the approval_metadata
		for (const checkpoint of existingCheckpoints || []) {
			await supabase
				.from("recovery_plan_checkpoints")
				.update({
					approval_status: "pending",
					approved_at: null,
					approved_by: null,
					updated_at: new Date().toISOString(),
					approval_metadata: {
						...checkpoint.approval_metadata,
						comment: null,
						updated_at: new Date().toISOString(),
					},
				})
				.eq("id", checkpoint.id);
		}

		// Create new progress records for all steps
		console.log(`Creating new progress records for all steps in plan ${planId}`);
		for (const step of executionOrder) {
			await createRecoveryPlanProgress(planId, step.id, executionId);
		}

		// Get GCP clients
		const clients = await GetGcpClients(datacenterId);

		// Get checkpoints
		const checkpoints = await getRecoveryPlanCheckpoints(planId);

		// Get current progress
		const progress = await getRecoveryPlanProgress(planId);

		// Determine starting point
		let nextStepIndex = 0;

		if (!isNewExecution) {
			// Find the last completed step or the first pending step
			const lastCompletedIndex = executionOrder.findIndex((step) => {
				const stepProgress = progress.find((p) => p.step_id === step.id);
				return stepProgress && ["completed", "failed"].includes(stepProgress.status);
			});

			if (lastCompletedIndex >= 0) {
				nextStepIndex = lastCompletedIndex + 1;
			}
		}

		// Execute steps from the next step onwards
		let waitingForApproval = false;
		let currentCheckpointId = null;

		for (let i = nextStepIndex; i < executionOrder.length; i++) {
			const step = executionOrder[i];
			console.log(
				`Processing step ${i + 1}/${executionOrder.length}: ${step.name} (${step.operation_type})`
			);

			// Check if this step is a checkpoint, verification step, or approval step
			const checkpoint = checkpoints.find((cp) => cp.step_id === step.id);
			const isVerificationStep = step.operation_type === "Verification";
			const isApprovalStep = step.operation_type === "Approval";

			// Handle approval/verification steps
			if (checkpoint || isVerificationStep || isApprovalStep) {
				// This is a checkpoint or verification/approval step
				if (checkpoint) {
					currentCheckpointId = checkpoint.id;
					console.log(`Found existing checkpoint ${currentCheckpointId} for step ${step.name}`);
				} else {
					// Create or get existing checkpoint for verification/approval step
					console.log(
						`Creating or getting checkpoint for ${step.operation_type} step: ${step.name}`
					);

					// Check if approver is specified
					if (!step.approval_metadata?.approver_id && !step.approval_metadata?.approver_role) {
						console.log(`No approver specified for step ${step.name}, will use fallback approver`);
					}

					try {
						// Use the enhanced createRecoveryPlanCheckpoint function that handles existing checkpoints
						const checkpoint = await createRecoveryPlanCheckpoint(
							step.recovery_plan_id,
							step.id,
							step.approval_metadata?.approver_id,
							step.approval_metadata?.approver_role
						);
						currentCheckpointId = checkpoint.id;
						console.log(`Checkpoint for step ${step.name}: ${currentCheckpointId}`);
					} catch (error: any) {
						console.error(`Error creating/getting checkpoint for step ${step.name}:`, error);

						// Send SSE update about the error
						sendPlanUpdate(planId, "FAILED", {
							message: `Failed to create/get checkpoint for step ${step.name}: ${
								error.message || "Unknown error"
							}`,
						});

						throw new Error(`Failed to create/get checkpoint: ${error.message || "Unknown error"}`);
					}
				}

				// Check if this checkpoint is already approved
				const isApproved = await isCheckpointApproved(currentCheckpointId);
				console.log(
					`Checkpoint ${currentCheckpointId} approval status: ${
						isApproved ? "Approved" : "Not approved"
					}`
				);

				if (!isApproved) {
					// Update step status to awaiting approval
					await updateRecoveryPlanProgress(
						step.recovery_plan_id,
						step.id,
						executionId,
						"awaiting_approval"
					);

					// Send SSE update
					sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "AWAITING_APPROVAL", {
						checkpoint_id: currentCheckpointId,
						approver_id: step.approval_metadata?.approver_id,
						approver_role: step.approval_metadata?.approver_role,
					});

					// Create approval request
					await createApprovalRequest(step, checkpoint || { id: currentCheckpointId });
					console.log(`Approval request created for step ${step.name}, waiting for approval`);

					// Stop execution until this checkpoint is approved
					waitingForApproval = true;
					break;
				}

				// Checkpoint is already approved, continue execution
				console.log(`Checkpoint ${currentCheckpointId} is already approved, continuing execution`);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "approved");
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "APPROVED");
			}

			// Execute the step
			console.log(`Executing step: ${step.name}`);
			await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "in_progress");
			sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "IN_PROGRESS");

			try {
				await executeRecoveryStep(step, clients, executionId);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "completed");
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "COMPLETED");
				console.log(`Step ${step.name} completed successfully`);
			} catch (error: any) {
				console.error(`Error executing step ${step.name}:`, error);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "failed", {
					error: (error as Error).message || "Unknown error",
				});
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "FAILED", {
					error: (error as Error).message || "Unknown error",
				});

				// Stop execution on failure
				waitingForApproval = true;
				break;
			}
		}

		// Update recovery plan status
		if (waitingForApproval) {
			await supabase
				.from("recovery_plans_new")
				.update({
					execution_status: "paused",
					execution_metadata: {
						...(recoveryPlan as any).execution_metadata,
						paused_at: new Date().toISOString(),
						paused_reason: "awaiting_approval",
						current_checkpoint_id: currentCheckpointId,
					},
				})
				.eq("id", planId);

			res.status(200).json({
				message: "Recovery plan execution paused, waiting for approval",
				planId,
				executionId,
				currentCheckpointId,
			});
		} else {
			// All steps completed
			await supabase
				.from("recovery_plans_new")
				.update({
					execution_status: "completed",
					execution_completed_at: new Date().toISOString(),
				})
				.eq("id", planId);

			// Log the execution completion
			await supabase.from("audit_logs").insert({
				user_id: userId,
				action: "complete_recovery_plan_execution",
				entity_type: "recovery_plan",
				entity_id: planId,
				details: {
					execution_id: executionId,
					datacenter_id: datacenterId,
				},
			});

			res.status(200).json({
				message: "Recovery plan execution completed",
				planId,
				executionId,
			});
		}
		return;
	} catch (error: any) {
		console.error("Error executing recovery plan:", error);
		res.status(500).json({ error: error.message });
		return;
	}
};

/**
 * Execute a recovery step based on its operation type
 * @param step The recovery step to execute
 * @param clients GCP client set for API operations
 * @param executionId The current execution ID
 */
const executeRecoveryStep = async (
	step: RecoverySteps,
	clients: GCPClientSet,
	executionId: string,
	datacenterId?: string
) => {
	try {
		console.log(`Executing step ${step.name} (${step.operation_type}) with ID ${step.id}`);

		// Check if this step has already been completed in this execution
		const { data: stepProgress, error: progressError } = await supabase
			.from("recovery_plan_progress")
			.select("status")
			.eq("step_id", step.id)
			.eq("recovery_plan_id", step.recovery_plan_id)
			.eq("execution_id", executionId)
			.single();

		// For the first step (usually Verification), always execute it even if it was previously completed
		// This ensures the verification step is always shown and executed
		if (step.step_order === 1) {
			console.log(
				`First step (${step.name}) will always be executed, even if previously completed`
			);
		}
		// If the step is already completed, approved, or rejected, we can skip its execution
		// but we'll still update the UI to show it's completed
		else if (stepProgress && ["completed", "approved", "rejected"].includes(stepProgress.status)) {
			console.log(`Step ${step.name} was already ${stepProgress.status}, updating UI`);
			sendStepUpdate(
				step.recovery_plan_id,
				step.step_order.toString(),
				stepProgress.status.toUpperCase()
			);
			return;
		}

		// For verification or approval steps, create a checkpoint first
		if (step.operation_type === "Verification" || step.operation_type === "Approval") {
			console.log(`Creating checkpoint for ${step.operation_type} step: ${step.name}`);

			// Create or get existing checkpoint for this step
			const { createRecoveryPlanCheckpoint } = await import("../services/recoveryProgressService");
			console.log(`Creating or getting checkpoint for step ${step.name} (${step.id})`);

			// Store the datacenter ID in the checkpoint's approval_metadata
			const checkpointMetadata = {
				datacenter_id: datacenterId,
				execution_id: executionId,
			};

			const checkpoint = await createRecoveryPlanCheckpoint(
				step.recovery_plan_id,
				step.id,
				step.approval_metadata?.approver_id || "admin", // Default to admin if no approver specified
				step.approval_metadata?.approver_role || "admin", // Default to admin role if none specified
				checkpointMetadata // Pass the metadata with datacenter ID
			);
			console.log(`Checkpoint for step ${step.name}: ${checkpoint.id}`);

			// Create approval request
			const { createApprovalRequest } = await import("../services/approvalService");
			await createApprovalRequest(step, checkpoint);

			// Update step status to awaiting approval
			await updateRecoveryPlanProgress(
				step.recovery_plan_id,
				step.id,
				executionId,
				"awaiting_approval"
			);

			// Send SSE update
			sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "AWAITING_APPROVAL", {
				checkpoint_id: checkpoint.id,
				approver_id: step.approval_metadata?.approver_id || "admin",
				approver_role: step.approval_metadata?.approver_role || "admin",
			});

			// Throw an error to stop execution until approval is received
			throw new Error(`${step.operation_type} required before proceeding`);
		}

		// Execute the step based on its operation type
		switch (step.operation_type) {
			case "Restore virtual machine":
				console.log("Restoring virtual machine", step);
				const projectId = "recovery-458311";
				restoreVirtualMachine(step, projectId, clients);
				// if (vmDetails.instanceName && vmDetails.zone && vmDetails.ipAddress) {
				// 	await enableSsh(vmDetails as { instanceName: string; zone: string; ipAddress: string }, clients, projectId);
				// }
			break;
			case "Virus check":
				await virusCheck();
				break;
			case "Apply OS updates":
				await applyOSUpdates();
				break;
			case "Approval":
				await approval(step, clients);
				break;
			case "Verification":
				await verification(step, clients);
				break;
			case "Manual step":
				await manualStep(step);
				break;
			case "Script":
				await executeScript(step);
				break;
			case "Notification":
				await sendNotification(step);
				break;
			case "IaC":
				await executeIaC(step, clients);
				break;
			case "Database restore":
				await restoreDatabase(step, clients);
				break;
			default:
				console.warn(`Unknown operation type: ${step.operation_type}`);
				break;
		}

		// Update execution metadata
		await supabase
			.from("recovery_plan_progress")
			.update({
				execution_metadata: {
					completed_at: new Date().toISOString(),
					execution_id: executionId,
				},
			})
			.eq("step_id", step.id)
			.eq("recovery_plan_id", step.recovery_plan_id);
	} catch (error: any) {
		console.error(`Error executing step ${step.name}:`, error);

		// If this is a verification or approval step, we want to stop execution
		if (
			(step.operation_type === "Verification" &&
				error.message === "Verification required before proceeding") ||
			(step.operation_type === "Approval" &&
				error.message === "Approval required before proceeding")
		) {
			throw error;
		}

		// Update execution metadata with error
		await supabase
			.from("recovery_plan_progress")
			.update({
				execution_metadata: {
					error: error.message || "Unknown error",
					error_stack: error.stack,
					failed_at: new Date().toISOString(),
					execution_id: executionId,
				},
			})
			.eq("step_id", step.id)
			.eq("recovery_plan_id", step.recovery_plan_id);

		throw error;
	}
};

const restoreVirtualMachine = async (step: RecoverySteps, projectId: string, clients: GCPClientSet): Promise<{ instanceName: string; zone: string; ipAddress: string }> => {
	console.log("Restoring virtual machine", step.operation_type);
	console.log("Restoring virtual machine", step);
	const { recovery_plan_id, step_order, operation_type, created_at } = step;
	console.log("Recovery plan id", recovery_plan_id);
	console.log("Step order", step_order);
	console.log("Operation type", operation_type);
	console.log("Created at", created_at);

	const execution_id = "292bd20a-1bae-48ae-a0da-dc2655519aca";
	// fetch backupExecution by id
	const [backupExecution] = await getBackupExecutionById(execution_id);
	if (!backupExecution) {
		console.log("Backup execution not found");
		sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
		throw new Error("Backup execution not found");
	}
	console.log("Backup execution", backupExecution);
	// read project_config_snapshot from backupExecution
	const { projectConfigSnapshot, vmSnapshots } = backupExecution;

	// create new vm from vmSnapshots
  console.log("#######Creating new vm, projectId ", projectId, "\n\n\n");
	sendStepUpdate(recovery_plan_id, step_order.toString(), "IN_PROGRESS");
	const newVm = await createNewVm(
		recovery_plan_id,
		step_order,
		vmSnapshots[0],
		projectId,
		projectConfigSnapshot.projectId,
		clients
	);
	console.log("New vm", newVm);
	sendStepUpdate(recovery_plan_id, step_order.toString(), "COMPLETED");
	return newVm;
};

const virusCheck = async () => {
	const recovery_plan_id = "292bd20a-1bae-48ae-a0da-dc2655519aca";
	const step_order = 2;
	sendStepUpdate(recovery_plan_id, step_order.toString(), "IN_PROGRESS");
	console.log("Virus check");
};

const applyOSUpdates = async () => {
	const recovery_plan_id = "292bd20a-1bae-48ae-a0da-dc2655519aca";
	const step_order = 3;
	sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
	console.log("Applying OS updates");
};

/**
 * Process an approval step
 * @param step The approval step
 * @param clients GCP client set
 */
const approval = async (step: RecoverySteps, clients: GCPClientSet) => {
	console.log("Processing approval step", step.name);

	// Create a checkpoint for approval
	const { createRecoveryPlanCheckpoint } = await import("../services/recoveryProgressService");

	try {
		// Create or get existing checkpoint for this approval step
		console.log(`Creating or getting checkpoint for approval step ${step.name} (${step.id})`);
		const checkpoint = await createRecoveryPlanCheckpoint(
			step.recovery_plan_id,
			step.id,
			step.approval_metadata?.approver_id || "admin", // Default to admin if no approver specified
			step.approval_metadata?.approver_role || "admin" // Default to admin role if none specified
		);

		// Create approval request
		const { createApprovalRequest } = await import("../services/approvalService");
		await createApprovalRequest(step, checkpoint);

		console.log(`Approval checkpoint created/retrieved for step ${step.name}: ${checkpoint.id}`);

		// Throw an error to pause execution until approval is received
		throw new Error("Approval required before proceeding");
	} catch (error) {
		console.error("Error creating/getting approval checkpoint:", error);
		throw error;
	}
};

/**
 * Process a verification step
 * @param step The verification step
 * @param clients GCP client set
 */
const verification = async (step: RecoverySteps, clients: GCPClientSet) => {
	console.log("Processing verification step", step.name);

	// Create a checkpoint for verification
	const { createRecoveryPlanCheckpoint } = await import("../services/recoveryProgressService");

	try {
		// Create or get existing checkpoint for this verification step
		console.log(`Creating or getting checkpoint for verification step ${step.name} (${step.id})`);
		const checkpoint = await createRecoveryPlanCheckpoint(
			step.recovery_plan_id,
			step.id,
			step.approval_metadata?.approver_id || "admin", // Default to admin if no approver specified
			step.approval_metadata?.approver_role || "admin" // Default to admin role if none specified
		);

		// Create approval request
		const { createApprovalRequest } = await import("../services/approvalService");
		await createApprovalRequest(step, checkpoint);

		console.log(
			`Verification checkpoint created/retrieved for step ${step.name}: ${checkpoint.id}`
		);

		// Throw an error to pause execution until verification is approved
		throw new Error("Verification required before proceeding");
	} catch (error) {
		console.error("Error creating/getting verification checkpoint:", error);
		throw error;
	}
};

/**
 * Execute a manual step
 * @param step The manual step
 */
const manualStep = async (step: RecoverySteps) => {
	console.log("Processing manual step", step.name);

	// Manual steps are handled through the UI and don't require server-side execution
	// This function is just a placeholder
	console.log("Manual step processed successfully");
};

/**
 * Execute a script step
 * @param step The script step
 */
const executeScript = async (step: RecoverySteps) => {
	console.log("Executing script step", step.name);

	// In a real implementation, this would execute a script
	// For now, we'll just simulate success
	await new Promise((resolve) => setTimeout(resolve, 2000));

	console.log("Script executed successfully");
};

/**
 * Send a notification
 * @param step The notification step
 */
const sendNotification = async (step: RecoverySteps) => {
	console.log("Sending notification", step.name);

	try {
		// Extract notification configuration from step
		// Use either configuration (new format) or parse config (old format)
		const configObj = step.configuration || (step.config ? JSON.parse(step.config) : {});

		const recipients = configObj.recipients || [];
		const subject = configObj.subject || `Recovery Plan Notification: ${step.name}`;
		const message = configObj.message || "A recovery step has been completed.";

		// Get the recovery plan name for better context in the email
		const { data: plan, error: planError } = await supabase
			.from("recovery_plans_new")
			.select("name")
			.eq("id", step.recovery_plan_id)
			.single();

		if (planError) {
			console.warn("Could not fetch plan name:", planError.message);
		}

		const planName = plan?.name || "Recovery Plan";

		// If no recipients are specified, try to find the plan owner or admin users
		if (!recipients.length) {
			// Get the plan owner
			const { data: planDetails, error: planDetailsError } = await supabase
				.from("recovery_plans_new")
				.select("created_by")
				.eq("id", step.recovery_plan_id)
				.single();

			if (!planDetailsError && planDetails?.created_by) {
				// Get the owner's email
				const { data: owner, error: ownerError } = await supabase
					.from("user_profiles")
					.select("email")
					.eq("id", planDetails.created_by)
					.single();

				if (!ownerError && owner?.email) {
					recipients.push(owner.email);
				}
			}

			// If still no recipients, get admin users
			if (!recipients.length) {
				const { data: admins, error: adminsError } = await supabase
					.from("user_profiles")
					.select("email")
					.eq("role", "admin")
					.eq("status", "active");

				if (!adminsError && admins?.length) {
					recipients.push(...admins.map((admin) => admin.email));
				}
			}
		}

		// Send notification emails to all recipients
		if (recipients.length) {
			const { sendNotificationEmail } = await import("../services/emailService");

			for (const recipient of recipients) {
				await sendNotificationEmail(recipient, subject, message, planName, step.name);
				console.log(`Notification email sent to ${recipient} for step ${step.name}`);
			}
		} else {
			console.warn(`No recipients found for notification step ${step.name}`);
		}

		// Log the notification
		await supabase.from("audit_logs").insert({
			action: "send_notification",
			entity_type: "recovery_step",
			entity_id: step.id,
			details: {
				recovery_plan_id: step.recovery_plan_id,
				recipients,
				subject,
				message,
				configuration: configObj,
			},
		});

		console.log("Notification sent successfully");
	} catch (error) {
		console.error("Error sending notification:", error);
		throw new Error(`Failed to send notification: ${(error as Error).message}`);
	}
};

/**
 * Execute Infrastructure as Code
 * @param step The IaC step
 * @param clients GCP client set
 */
const executeIaC = async (step: RecoverySteps, clients: GCPClientSet) => {
	console.log("Executing IaC", step.name);

	// In a real implementation, this would execute IaC
	// For now, we'll just simulate success
	await new Promise((resolve) => setTimeout(resolve, 3000));

	console.log("IaC executed successfully");
};

/**
 * Restore a database
 * @param step The database restore step
 * @param clients GCP client set
 */
const restoreDatabase = async (step: RecoverySteps, clients: GCPClientSet) => {
	console.log("Restoring database", step.name);

	// In a real implementation, this would restore a database
	// For now, we'll just simulate success
	await new Promise((resolve) => setTimeout(resolve, 5000));

	console.log("Database restored successfully");
};

const createNewVm = async (
	recovery_plan_id: string,
	step_order: number,
	vmSnapshotRecord: VMSnapshotRecord,
	newProjectId: string,
	sourceProjectId: string,
	clients: GCPClientSet
) => {
	sendStepUpdate(recovery_plan_id, step_order.toString(), "IN_PROGRESS");
	console.log("***Creating new VM", vmSnapshotRecord, newProjectId);

	//const sourceProjectId = extractProjectIdFromSnapshotLink(vmSnapshotRecord.snapshots[0].snapshotLink);
	const zone = vmSnapshotRecord.vmConfig.zone;
	const targetServiceAccount = vmSnapshotRecord.vmConfig.serviceAccounts[0].email;

	// Grant access to each snapshot
	for (const snapshot of vmSnapshotRecord.snapshots) {
		await grantSnapshotAccess(snapshot.snapshotId, sourceProjectId, targetServiceAccount);
	}

	const newDisks = [];

	// 1. Create disks from snapshots
	try {
		for (const snapshot of vmSnapshotRecord.snapshots) {
			const newDiskName = `${vmSnapshotRecord.vmConfig.name}-${snapshot.deviceName}-disk`;

			console.log(`Creating disk ${newDiskName} from snapshot ${snapshot.snapshotId}...`);

			const [operation] = await clients.disksClient.insert({
				project: newProjectId,
				zone,
				diskResource: {
					name: newDiskName,
					sourceSnapshot: `projects/${sourceProjectId}/global/snapshots/${snapshot.snapshotId}`,
					type: `projects/${newProjectId}/zones/${zone}/diskTypes/pd-standard`,
				},
			});

			// Wait for the operation to complete
			while (true) {
				const [status] = await clients.operationsClient.get({
					project: newProjectId,
					operation: operation.name!.split("/").pop()!,
					zone,
				});
				if (status.status === "DONE") {
					if (status.error) {
						console.error("Error creating disk:", JSON.stringify(status.error, null, 2));
						sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
						throw new Error(
							`Disk creation failed: ${status.error.errors?.map((e) => e.message).join("; ")}`
						);
					}
					break;
				}
				await new Promise((resolve) => setTimeout(resolve, 1000));
			}

			// Wait for the disk creation to complete
			// const diskCreationStatus = await checkDiskCreationStatus(newProjectId, zone, newDiskName);
			// wait 20 seconds
			await new Promise((resolve) => setTimeout(resolve, 20000));
			console.log(`Disk ${newDiskName} created and ready.`);
			newDisks.push({
				deviceName: snapshot.deviceName,
				diskName: newDiskName,
			});
		}
	} catch (error) {
		console.error("Error creating disks:", error);
		sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
		throw error;
	}

	console.log("***New disks", newDisks);
	// 2. Create VM instance
	let bootDisk = newDisks.find((d) => d.deviceName === "boot");
	if (!bootDisk) {
		console.warn("Boot disk not explicitly marked. Defaulting to first disk...");
		bootDisk = newDisks[0]; // fallback to first disk
		bootDisk.deviceName = "boot";
	}
	if (!bootDisk) {
		console.error("Boot disk not found in snapshots!");
		sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
		throw new Error("Boot disk not found in snapshots!");
	}

	const newVmName = `${vmSnapshotRecord.vmConfig.name}-restored`;
	const machineType = vmSnapshotRecord.vmConfig.machineType.split("/").pop();

	const disksForVm = newDisks
		.sort((a, b) => (a.deviceName === "boot" ? -1 : b.deviceName === "boot" ? 1 : 0))
		.map((d) => ({
			boot: d.deviceName === "boot",
			autoDelete: true,
			source: `projects/${newProjectId}/zones/${zone}/disks/${d.diskName}`,
			type: "PERSISTENT",
			mode: "READ_WRITE",
		}));

	console.log(`Creating VM ${newVmName}...disksForVm`, disksForVm);

	console.log("***Creating VM", newVmName, zone, newProjectId);
	try {
		const [vmOperation] = await clients.instancesClient.insert({
			project: newProjectId,
			zone,
			instanceResource: {
				name: newVmName,
				machineType: `zones/${zone}/machineTypes/${machineType}`,
				disks: disksForVm,
				networkInterfaces: [
					{
						network: `projects/${newProjectId}/global/networks/default`,
						accessConfigs: [{ type: "ONE_TO_ONE_NAT" }],
					},
				],
				metadata: vmSnapshotRecord.vmConfig.metadata,
				tags: { items: vmSnapshotRecord.vmConfig.tags },
				serviceAccounts: [
					{
						email: "<EMAIL>", // ✅ Correct SA
						scopes: ["https://www.googleapis.com/auth/cloud-platform"],
					},
				],
			},
		});

		// Wait for the VM creation operation to complete
		while (true) {
			const [status] = await clients.operationsClient.get({
				project: newProjectId,
				operation: vmOperation.name!.split("/").pop()!,
				zone,
			});

			if (status.status === "DONE") {
				console.log("***VM creation operation completed");
				if (status.error) {
					console.error("***VM creation failed with error:", JSON.stringify(status.error, null, 2));
					sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
					throw new Error(
						`VM creation failed: ${status.error.errors?.map((e) => e.message).join("; ")}`
					);
				}
				break;
			}
			console.log("***VM creation operation not completed yet, waiting...");
			await new Promise((resolve) => setTimeout(resolve, 30000));
		}
	} catch (error) {
		console.error("Error creating VM:", error);
		sendStepUpdate(recovery_plan_id, step_order.toString(), "FAILED");
		throw error;
	}

	console.log(`VM ${newVmName} created successfully.`);
	sendStepUpdate(recovery_plan_id, step_order.toString(), "COMPLETED");

	const ipAddress = await getVM(newProjectId, zone, newVmName, clients);
	return { instanceName: newVmName, zone, ipAddress };
};

const getVM = async (projectId: string, zone: string, instanceName: string, clients: GCPClientSet) => {
	const [vm] = await clients.instancesClient.get({
		project: projectId,
		zone,
		instance: instanceName,
	});
	
	if (!vm?.networkInterfaces?.[0]?.accessConfigs?.[0]?.natIP) {
		throw new Error('VM network interface or IP not found');
	}
	
	if (vm.networkInterfaces[0].accessConfigs[0].natIP) {
		return vm.networkInterfaces[0].accessConfigs[0].natIP;
	}

	throw new Error('VM network interface or IP not found');
}


async function grantSnapshotAccess(
	snapshotName: string,
	sourceProject: string,
	serviceAccountEmail: string
) {
	console.log("***Granting snapshot access", snapshotName, sourceProject, serviceAccountEmail);
	const snapshotsClient = new SnapshotsClient();
	// handle error at all levels
	try {
		const [policy] = await snapshotsClient.getIamPolicy({
			project: sourceProject,
			resource: snapshotName,
		});

		policy.bindings = policy.bindings || [];
		policy.bindings.push({
			role: "roles/compute.storageAdmin",
			members: [`serviceAccount:${serviceAccountEmail}`],
		});

		await snapshotsClient.setIamPolicy({
			project: sourceProject,
			resource: snapshotName,
			globalSetPolicyRequestResource: {
				policy,
			},
		});

		console.log(`Granted storageAdmin to ${serviceAccountEmail} on snapshot ${snapshotName}`);
	} catch (error) {
		console.error("Error granting snapshot access:", error);
	}
}

// This function is for future use when project creation is needed
const createNewProject = async (projectConfig: ProjectConfig, clients: GCPClientSet) => {
	console.log("Creating new project", projectConfig);

	console.log("GOOGLE_APPLICATION_CREDENTIALS:", process.env.GOOGLE_APPLICATION_CREDENTIALS);

	// create new project in gcp
	// const auth = new GoogleAuth({
	// 	scopes: ["https://www.googleapis.com/auth/cloud-platform"],
	// 	keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
	// });

	const authClient = clients.authClient;

	const crm = google.cloudresourcemanager({
		version: "v3",
		auth: (await authClient.getClient()) as OAuth2Client | JWT,
	});

	try {
		await crm.projects.create({
			requestBody: {
				projectId: "cleanroom-project-" + projectConfig.projectId,
				displayName: "Cleanroom Project",
				parent: "organizations/************",
				labels: projectConfig.labels,
			},
		});
		console.log(`Project ${projectConfig.projectId} created`);
	} catch (error) {
		console.error("Error creating project:", error);
		return;
	}

	await crm.projects.setIamPolicy({
		resource: `projects/${projectConfig.projectId}`,
		requestBody: {
			policy: {
				bindings: projectConfig.iamPolicies,
				version: 3,
			},
		},
	});
	console.log(`IAM policy set`);

	const serviceUsage = google.serviceusage({
		version: "v1",
		auth: (await authClient.getClient()) as OAuth2Client | JWT,
	});

	for (const api of projectConfig.enabledApis) {
		await serviceUsage.services.enable({
			name: `projects/${projectConfig.projectId}/services/${api}`,
		});
		console.log(`Enabled API: ${api}`);
	}

	createNetworkResources(projectConfig.projectId, projectConfig.networks);
};

async function createNetworkResources(projectId: string, networks: ProjectConfig["networks"]) {
	const compute = google.compute("v1");

	for (const net of networks) {
		// Create Network
		await compute.networks.insert({
			project: projectId,
			requestBody: {
				name: net.name,
				autoCreateSubnetworks: net.autoCreateSubnetworks,
				routingConfig: { routingMode: net.routingMode },
			},
		});

		// Subnets
		for (const subnet of net.subnets) {
			await compute.subnetworks.insert({
				project: projectId,
				region: subnet.region,
				requestBody: {
					name: subnet.name,
					ipCidrRange: subnet.ipCidrRange,
					network: `projects/${projectId}/global/networks/${net.name}`,
					privateIpGoogleAccess: subnet.privateIpGoogleAccess,
				},
			});
		}

		// Firewalls
		for (const fw of net.firewalls) {
			await compute.firewalls.insert({
				project: projectId,
				requestBody: {
					name: fw.name,
					direction: fw.direction,
					priority: fw.priority,
					sourceRanges: fw.sourceRanges,
					allowed: fw.allowed,
					network: `projects/${projectId}/global/networks/${net.name}`,
					disabled: fw.disabled ?? false,
				},
			});
		}

		// Routes
		for (const route of net.routes) {
			await compute.routes.insert({
				project: projectId,
				requestBody: {
					name: route.name,
					destRange: route.destRange,
					nextHopGateway: route.nextHopGateway,
					priority: route.priority,
					network: `projects/${projectId}/global/networks/${net.name}`,
				},
			});
		}
	}
}

// Note: The checkStepPermission function has been removed as we're now using the approval process
// defined during recovery plan creation instead of checking permissions for each step individually.

/**
 * Execute the next steps in a recovery plan after a checkpoint
 * This function is called when a checkpoint is approved to continue execution
 * @param planId The recovery plan ID
 * @param executionId The execution ID
 * @param checkpointId The checkpoint ID that was approved
 * @param datacenterId The datacenter ID
 */
export const executeNextSteps = async (
	planId: string,
	executionId: string,
	checkpointId: string,
	datacenterId: string
) => {
	try {
		console.log(`Executing next steps for plan ${planId} after checkpoint ${checkpointId}`);
		console.log(
			`Parameters: planId=${planId}, executionId=${executionId}, checkpointId=${checkpointId}, datacenterId=${datacenterId}`
		);

		// Send an SSE update to notify clients that execution is continuing
		sendPlanUpdate(planId, "IN_PROGRESS", {
			message: "Continuing execution after approval",
			checkpoint_id: checkpointId,
			datacenter_id: datacenterId,
			timestamp: new Date().toISOString(),
		});

		// Get the checkpoint details
		const { data: checkpoint, error: checkpointError } = await supabase
			.from("recovery_plan_checkpoints")
			.select("*")
			.eq("id", checkpointId)
			.single();

		if (checkpointError || !checkpoint) {
			console.error("Error fetching checkpoint:", checkpointError);
			throw new Error(`Failed to fetch checkpoint: ${checkpointError?.message}`);
		}

		// Get the step associated with this checkpoint
		const { data: checkpointStep, error: stepError } = await supabase
			.from("recovery_steps_new")
			.select("*")
			.eq("id", checkpoint.step_id)
			.single();

		if (stepError || !checkpointStep) {
			console.error("Error fetching checkpoint step:", stepError);
			throw new Error(`Failed to fetch checkpoint step: ${stepError?.message}`);
		}

		// Get all steps for this plan
		const { data: steps, error: stepsError } = await supabase
			.from("recovery_steps_new")
			.select("*")
			.eq("recovery_plan_id", planId)
			.order("step_order", { ascending: true });

		if (stepsError || !steps) {
			console.error("Error fetching steps:", stepsError);
			throw new Error(`Failed to fetch steps: ${stepsError?.message}`);
		}

		// Find the index of the checkpoint step
		const checkpointStepIndex = steps.findIndex((step) => step.id === checkpoint.step_id);
		if (checkpointStepIndex === -1) {
			throw new Error("Checkpoint step not found in plan steps");
		}

		// Start from the step after the checkpoint
		// This ensures we continue execution from where we left off
		const nextStepIndex = checkpointStepIndex + 1;

		console.log(
			`Starting execution from step index ${nextStepIndex} (after checkpoint) with checkpoint step index ${checkpointStepIndex}`
		);

		// If there are no more steps, mark the plan as completed
		if (nextStepIndex >= steps.length) {
			console.log(`No more steps to execute for plan ${planId}, marking as completed`);

			// Update plan status to completed
			await supabase
				.from("recovery_plans_new")
				.update({
					execution_status: "completed",
					execution_completed_at: new Date().toISOString(),
				})
				.eq("id", planId);

			// Update execution status
			await supabase
				.from("recovery_plan_executions")
				.update({
					status: "completed",
					completed_at: new Date().toISOString(),
				})
				.eq("id", executionId);

			// Send SSE update
			sendPlanUpdate(planId, "COMPLETED", {
				message: "Recovery plan execution completed successfully",
			});

			return;
		}

		// Get GCP clients
		const clients = await GetGcpClients(datacenterId);

		// Log the steps for debugging
		console.log(
			`Steps to execute:`,
			steps.map((s) => ({ id: s.id, name: s.name, order: s.step_order, type: s.operation_type }))
		);

		// Send an SSE update to notify clients that execution is continuing with specific steps
		sendPlanUpdate(planId, "EXECUTING", {
			message: `Continuing execution from step ${nextStepIndex + 1} (${steps[nextStepIndex].name})`,
			checkpoint_id: checkpointId,
			datacenter_id: datacenterId,
			next_step_id: steps[nextStepIndex].id,
			next_step_name: steps[nextStepIndex].name,
			next_step_index: nextStepIndex,
			timestamp: new Date().toISOString(),
		});

		// Execute the remaining steps
		console.log(
			`Starting execution from step index ${nextStepIndex} out of ${steps.length} total steps`
		);
		for (let i = nextStepIndex; i < steps.length; i++) {
			const step = steps[i];
			console.log(
				`Executing step ${i + 1}/${steps.length}: ${step.name} (${step.operation_type}) with ID ${
					step.id
				}`
			);

			// Check if this step has already been completed in this execution
			const { data: stepProgress } = await supabase
				.from("recovery_plan_progress")
				.select("status")
				.eq("step_id", step.id)
				.eq("recovery_plan_id", step.recovery_plan_id)
				.eq("execution_id", executionId)
				.single();

			// If this is the checkpoint step that was just approved, mark it as approved and continue
			if (step.id === checkpoint.step_id) {
				console.log(`Step ${step.name} is the checkpoint step that was just approved, updating UI`);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "approved");
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "APPROVED");
				continue;
			}
			// If the step is already completed, approved, or rejected, we can skip its execution
			// but we'll still update the UI to show it's completed
			else if (
				stepProgress &&
				["completed", "approved", "rejected"].includes(stepProgress.status)
			) {
				console.log(
					`Step ${step.name} was already ${stepProgress.status}, updating UI and continuing`
				);
				sendStepUpdate(
					step.recovery_plan_id,
					step.step_order.toString(),
					stepProgress.status.toUpperCase()
				);
				continue;
			}

			// Check if this step is a checkpoint or verification step
			if (step.operation_type === "Verification" || step.operation_type === "Approval") {
				console.log(`Step ${step.name} is a ${step.operation_type} step, pausing execution`);

				// Update step status to awaiting approval
				await updateRecoveryPlanProgress(
					step.recovery_plan_id,
					step.id,
					executionId,
					"awaiting_approval"
				);

				// Create or get existing checkpoint
				const { createRecoveryPlanCheckpoint } = await import(
					"../services/recoveryProgressService"
				);

				console.log(`Creating or getting checkpoint for step ${step.name} (${step.id})`);
				let newCheckpoint;
				try {
					newCheckpoint = await createRecoveryPlanCheckpoint(
						step.recovery_plan_id,
						step.id,
						step.approval_metadata?.approver_id,
						step.approval_metadata?.approver_role
					);
					console.log(`Successfully got checkpoint: ${newCheckpoint.id}`);
				} catch (checkpointError) {
					console.error(
						`Error creating/getting checkpoint for step ${step.name}:`,
						checkpointError
					);

					// Send SSE update about the error
					sendPlanUpdate(planId, "FAILED", {
						message: `Failed to create checkpoint for step ${step.name}: ${
							checkpointError instanceof Error ? checkpointError.message : "Unknown error"
						}`,
					});

					throw checkpointError;
				}

				// Create approval request
				const { createApprovalRequest } = await import("../services/approvalService");
				await createApprovalRequest(step, {
					id: newCheckpoint.id,
					recovery_plan_id: step.recovery_plan_id,
					step_id: step.id,
					approval_required: true,
				});

				// Send SSE update
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "AWAITING_APPROVAL", {
					checkpoint_id: newCheckpoint.id,
					approver_id: step.approval_metadata?.approver_id,
					approver_role: step.approval_metadata?.approver_role,
				});

				// Pause execution until this checkpoint is approved
				console.log(`Pausing execution at step ${step.name} until approval is received`);

				// Update plan status to paused
				await supabase
					.from("recovery_plans_new")
					.update({
						execution_status: "paused",
					})
					.eq("id", planId);

				// Update execution status
				await supabase
					.from("recovery_plan_executions")
					.update({
						status: "paused",
						paused_at: new Date().toISOString(),
					})
					.eq("id", executionId);

				// Send SSE update
				sendPlanUpdate(planId, "PAUSED", {
					message: `Execution paused at step ${step.name} waiting for approval`,
					checkpoint_id: newCheckpoint.id,
				});

				return;
			}

			// Execute the step
			await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "in_progress");
			sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "IN_PROGRESS");

			try {
				// Pass the datacenter ID to the executeRecoveryStep function
				await executeRecoveryStep(step, clients, executionId, datacenterId);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "completed");
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "COMPLETED");
				console.log(`Step ${step.name} completed successfully`);
			} catch (error: any) {
				console.error(`Error executing step ${step.name}:`, error);
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "failed", {
					error: (error as Error).message || "Unknown error",
				});
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "FAILED", {
					error: (error as Error).message || "Unknown error",
				});

				// Update plan status to failed
				await supabase
					.from("recovery_plans_new")
					.update({
						execution_status: "failed",
					})
					.eq("id", planId);

				// Update execution status
				await supabase
					.from("recovery_plan_executions")
					.update({
						status: "failed",
						failed_at: new Date().toISOString(),
					})
					.eq("id", executionId);

				// Send SSE update
				sendPlanUpdate(planId, "FAILED", {
					message: `Execution failed at step ${step.name}: ${error.message}`,
				});

				return;
			}
		}

		// If we've executed all steps, mark the plan as completed
		console.log(`All steps executed for plan ${planId}, marking as completed`);

		// Update plan status to completed
		await supabase
			.from("recovery_plans_new")
			.update({
				execution_status: "completed",
				execution_completed_at: new Date().toISOString(),
			})
			.eq("id", planId);

		// Update execution status
		await supabase
			.from("recovery_plan_executions")
			.update({
				status: "completed",
				completed_at: new Date().toISOString(),
			})
			.eq("id", executionId);

		// Update all steps to completed status if they aren't already
		for (const step of steps) {
			const { data: stepProgress } = await supabase
				.from("recovery_plan_progress")
				.select("status")
				.eq("step_id", step.id)
				.eq("recovery_plan_id", step.recovery_plan_id)
				.eq("execution_id", executionId)
				.single();

			if (!stepProgress || !["completed", "approved", "rejected"].includes(stepProgress.status)) {
				await updateRecoveryPlanProgress(step.recovery_plan_id, step.id, executionId, "completed");
				sendStepUpdate(step.recovery_plan_id, step.step_order.toString(), "COMPLETED");
			}
		}

		// Send SSE update
		sendPlanUpdate(planId, "COMPLETED", {
			message: "Recovery plan execution completed successfully",
		});
	} catch (error: any) {
		console.error(`Error executing next steps for plan ${planId}:`, error);
		throw error;
	}
};

// wait for the final status of a recovery step
async function waitForFinalStatus(rowId: string, idColumn: string = 'id'): Promise<string> {
	return new Promise((resolve, reject) => {
	  const channel = supabase
		.channel('recovery-step-status-final')
		.on(
		  'postgres_changes',
		  {
			event: 'UPDATE',
			schema: 'public',
			table: 'recovery_steps_new',
			filter: `${idColumn}=eq.${rowId}`,
		  },
		  (payload) => {
			const status = payload.new.status;
			console.log(`🔄 Status updated for ${idColumn}=${rowId}: ${status}`);
			if (status === 'COMPLETED' || status === 'FAILED') {
			  channel.unsubscribe();
			  resolve(status);
			}
		  }
		)
		.subscribe((status) => {
		  if (status === 'SUBSCRIBED') {
			console.log(`✅ Waiting for status COMPLETED or FAILED on row ${idColumn} = ${rowId}`);
		  } else if (status === 'CLOSED') {
			reject(new Error('Realtime channel closed before status changed'));
		  }
		});
	});
  }	


  export const updateRecoveryStepStatus: RequestHandler = async (req, res) => {
	const { stepId } = req.params;
	const { status } = req.body;
	console.log("Updating recovery step status", stepId, status);
	await updateRecoveryStepStatusService(stepId, status);
	res.status(200).json({ message: "Recovery step status updated" });
}	