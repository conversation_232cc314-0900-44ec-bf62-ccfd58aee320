import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { RecoveryStep } from "@/lib/types";
import { useUpdateRecoveryStep } from "@/lib/api/hooks/recoveryPlans";

interface IaCStepProps {
	step: RecoveryStep;
	isEditing: boolean;
	onSave: () => void;
	onCancel: () => void;
}

const formSchema = z.object({
	name: z.string().min(3, "Name must be at least 3 characters"),
	project_naming: z.enum(["unique", "keep_same", "custom"]),
	custom_name: z.string().optional(),
	components: z.array(z.string()).min(1, "At least one component must be selected"),
});

type FormValues = z.infer<typeof formSchema>;

// Available IaC components
const availableComponents = [
	{ id: "project", name: "Project" },
	{ id: "network", name: "Network" },
	{ id: "subnet", name: "Subnet" },
	{ id: "firewall", name: "Firewall Rules" },
	{ id: "router", name: "Router" },
	{ id: "nat", name: "NAT Gateway" },
];

const IaCStep: React.FC<IaCStepProps> = ({ step, isEditing, onSave, onCancel }) => {
	// Initialize expanded state based on isEditing or if there are components already configured
	const [isExpanded, setIsExpanded] = useState(
		isEditing || (step.configuration?.components && step.configuration.components.length > 0)
	);
	const updateStep = useUpdateRecoveryStep();

	// Log for debugging
	useEffect(() => {
		console.log("IaCStep rendered:", {
			stepId: step.id,
			isEditing,
			isExpanded,
			configuration: step.configuration,
		});
	}, [step, isEditing, isExpanded]);

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		setValue,
		watch,
		reset,
	} = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: step.name || "",
			project_naming: step.configuration?.project_naming || "unique",
			custom_name: step.configuration?.custom_name || "",
			components: step.configuration?.components || ["project", "network", "subnet"],
		},
	});

	const selectedProjectNaming = watch("project_naming");
	const selectedComponents = watch("components");

	// Toggle expanded state
	const toggleExpanded = () => {
		if (isEditing) return;
		setIsExpanded(!isExpanded);
	};

	// Handle component selection
	const handleComponentSelection = (componentId: string, isChecked: boolean) => {
		const currentComponents = selectedComponents || [];
		if (isChecked) {
			setValue("components", [...currentComponents, componentId]);
		} else {
			setValue(
				"components",
				currentComponents.filter((id) => id !== componentId)
			);
		}
	};

	const onSubmit = async (data: FormValues) => {
		try {
			const updatedStep: Partial<RecoveryStep> = {
				...step,
				name: data.name,
				configuration: {
					...step.configuration,
					project_naming: data.project_naming,
					custom_name: data.custom_name,
					components: data.components,
				},
			};

			console.log("Updating IaC step:", updatedStep);
			updateStep.mutate(updatedStep as any);
			setIsExpanded(false);
			onSave();
		} catch (error) {
			console.error("Error updating IaC step:", error);
			toast.error("Failed to update IaC step");
		}
	};

	return (
		<div className="border rounded-md p-4 mb-4 bg-gray-800">
			<div className="flex justify-between items-center cursor-pointer" onClick={toggleExpanded}>
				<div>
					<h3 className="text-lg font-medium">{step.name}</h3>
					<p className="text-sm text-muted-foreground">Infrastructure as Code</p>
				</div>
				<Button
					variant="ghost"
					size="sm"
					onClick={(e) => {
						e.stopPropagation();
						toggleExpanded();
					}}
				>
					{isExpanded ? "Collapse" : "Expand"}
				</Button>
			</div>

			{isExpanded && (
				<form onSubmit={handleSubmit(onSubmit)} className="mt-4 space-y-4">
					<div className="space-y-2">
						<label htmlFor="name" className="text-sm font-medium">
							Step Name
						</label>
						<Input id="name" placeholder="Enter step name" {...register("name")} />
						{errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium">Project Naming</label>
						<Select
							onValueChange={(value) => setValue("project_naming", value as any)}
							defaultValue={selectedProjectNaming}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select project naming option" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="unique">Generate unique project name</SelectItem>
								<SelectItem value="keep_same">Keep same project name</SelectItem>
								<SelectItem value="custom">Custom project name</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{selectedProjectNaming === "custom" && (
						<div className="space-y-2">
							<label htmlFor="custom_name" className="text-sm font-medium">
								Custom Project Name
							</label>
							<Input
								id="custom_name"
								placeholder="Enter custom project name"
								{...register("custom_name")}
							/>
						</div>
					)}

					<div className="space-y-2">
						<label className="text-sm font-medium">Select Components</label>
						<div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
							{availableComponents.map((component) => (
								<div key={component.id} className="flex items-center space-x-2">
									<Checkbox
										id={`component-${component.id}`}
										checked={selectedComponents?.includes(component.id)}
										onCheckedChange={(checked) => handleComponentSelection(component.id, !!checked)}
									/>
									<label htmlFor={`component-${component.id}`} className="text-sm cursor-pointer">
										{component.name}
									</label>
								</div>
							))}
						</div>
						{errors.components && (
							<p className="text-sm text-red-500">{errors.components.message}</p>
						)}
					</div>

					<div className="flex justify-end space-x-2">
						<Button variant="outline" type="button" onClick={onCancel}>
							Cancel
						</Button>
						<Button
							type="submit"
							className="bg-dr-purple hover:bg-dr-purple-dark"
							disabled={isSubmitting}
						>
							{isSubmitting ? "Saving..." : "Save"}
						</Button>
					</div>
				</form>
			)}
		</div>
	);
};

export default IaCStep;
