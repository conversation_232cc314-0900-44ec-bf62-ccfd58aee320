import { BaseStepExecutor, ExecutionContext, ExecutionResult } from './baseStepExecutor';

export class ScriptExecutor extends BaseStepExecutor {
    constructor() {
        super('Script');
    }

    async execute(step: any, context: ExecutionContext): Promise<ExecutionResult> {
        this.log(`Starting script execution for step: ${step.name}`);
        
        try {
            this.validate(step);
            const config = this.parseConfiguration(step);
            
            // Simulate script execution
            await this.sleep(1000);
            
            return this.createSuccessResult(
                'Script executed successfully',
                { script: config.script_name || 'unknown' }
            );

        } catch (error) {
            return this.createErrorResult(`Script execution failed: ${error.message}`, error.message);
        }
    }

    validate(step: any): boolean {
        super.validate(step);
        if (step.operation_type !== 'Script') {
            throw new Error(`Invalid operation type for script: ${step.operation_type}`);
        }
        return true;
    }
}
