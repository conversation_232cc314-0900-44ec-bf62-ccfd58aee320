import { Router, Request, Response } from "express";
import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";
import { supabase } from "../../services/supabaseService";

const router = Router();

/**
 * Validate if a string is a properly formatted JWT token
 *
 * @param token The token to validate
 * @returns True if the token appears to be a valid JWT format
 */
const isValidJwtFormat = (token: string): boolean => {
	const parts = token.split(".");
	if (parts.length !== 3) {
		return false;
	}

	try {
		for (const part of parts) {
			const base64 = part.replace(/-/g, "+").replace(/_/g, "/");
			Buffer.from(base64, "base64");
		}
		return true;
	} catch (error) {
		return false;
	}
};

/**
 * Activate a license token
 *
 * Verifies the token and sets it as an HttpOnly cookie, and adds it to the database if not already present
 *
 * @route POST /api/license/activate
 * @param {string} token - License token to activate
 * @returns {Object} Success status and license info
 */
const activateLicense = async (req: Request, res: Response): Promise<any> => {
	try {
		const { token } = req.body;
		if (!token) {
			return res.status(400).json({ error: "Missing required field: token" });
		}

		if (!isValidJwtFormat(token)) {
			console.error("Invalid JWT format:", token);
			return res.status(400).json({
				error: "Invalid license format",
				details: "The provided token is not a valid JWT format",
			});
		}

		const publicKeyPath = path.join(__dirname, "../../../keys/public.key");

		if (!fs.existsSync(publicKeyPath)) {
			console.error("Public key not found at path:", publicKeyPath);
			return res.status(500).json({ error: "License public key not found" });
		}

		const publicKey = fs.readFileSync(publicKeyPath, "utf8");
		console.log("Public key loaded, length:", publicKey.length);

		try {
			const decoded = jwt.verify(token, publicKey, { algorithms: ["RS256"] });
			console.log("Token verified successfully, decoded payload:", decoded);

			const expiresIn = (decoded as any).exp - Math.floor(Date.now() / 1000);
			res.cookie("license", token, {
				httpOnly: true,
				secure: process.env.NODE_ENV === "production",
				maxAge: expiresIn * 1000,
				sameSite: "strict",
				path: "/",
			});

			res.cookie("license_status", "active", {
				httpOnly: true,
				secure: process.env.NODE_ENV === "production",
				maxAge: expiresIn * 1000,
				sameSite: "strict",
				path: "/",
			});

			const { error } = await supabase.from("licenses").insert({
				customer_id: (decoded as any).sub,
				jwt: token,
				status: "active",
				last_verified: new Date().toISOString(),
			});

			if (error) {
				console.error("Error storing license:", error);
				return res.status(500).json({ error: "Failed to store license" });
			}

			return res.status(200).json({
				ok: true,
				message: "License activated successfully",
				customerId: (decoded as any).sub,
				expiresAt: new Date((decoded as any).exp * 1000).toISOString(),
			});
		} catch (error) {
			console.error("License verification failed:", error);
			return res.status(401).json({ error: "Invalid license token" });
		}
	} catch (error) {
		console.error("License activation error:", error);
		return res.status(500).json({ error: "License activation failed" });
	}
};

router.post("/", activateLicense);

export default router;
