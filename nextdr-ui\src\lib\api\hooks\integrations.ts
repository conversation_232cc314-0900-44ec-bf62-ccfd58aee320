import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "../api-client";
import { IntegrationConfig, SyncStats } from "@/types/integrations";
import { toast } from "sonner";

export const useIntegrationConfig = () => {
	return useQuery({
		queryKey: ["integration-config"],
		queryFn: async () => {
			return apiRequest<IntegrationConfig>("/api/admin/integrations/config");
		},
	});
};

export const useUpdateIntegrationConfig = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (config: Partial<IntegrationConfig>) => {
			return apiRequest<IntegrationConfig>("/api/admin/integrations/config", {
				method: "PUT",
				body: JSON.stringify(config),
			});
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["integration-config"] });
			queryClient.invalidateQueries({ queryKey: ["sync-stats"] });
			toast.success("Integration configuration updated successfully");
		},
		onError: (error: any) => {
			toast.error("Failed to update integration configuration: " + error.message);
		},
	});
};

export const useTriggerSync = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async () => {
			return apiRequest<{ message: string; stats: SyncStats }>("/api/admin/integrations/sync", {
				method: "POST",
			});
		},
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["integration-config"] });
			queryClient.invalidateQueries({ queryKey: ["sync-stats"] });
			queryClient.invalidateQueries({ queryKey: ["userProfiles"] });
			toast.success(
				`Sync completed: ${data.stats.synced_users} users, ${data.stats.synced_groups} groups`
			);
		},
		onError: (error: any) => {
			toast.error("Failed to trigger sync: " + error.message);
		},
	});
};

export const useSyncStats = () => {
	return useQuery({
		queryKey: ["sync-stats"],
		queryFn: async () => {
			return apiRequest<SyncStats>("/api/admin/integrations/stats");
		},
	});
};
