package main

import (
	"bytes"
	"flag"
	"fmt"
	"net/http"
	"os/exec"
)

func runCommand(name string, args ...string) error {
	cmd := exec.Command(name, args...)
	cmd.Stdout = nil
	cmd.Stderr = nil
	return cmd.Run()
}

func updateOS() error {
	fmt.Println("Updating package list...")
	if err := runCommand("sudo", "apt-get", "update"); err != nil {
		return fmt.Errorf("apt-get update failed: %w", err)
	}

	fmt.Println("Upgrading packages...")
	if err := runCommand("sudo", "apt-get", "upgrade", "-y"); err != nil {
		return fmt.Errorf("apt-get upgrade failed: %w", err)
	}

	return nil
}

func notifyStatusAPI(apiURL, stepID, status string) error {
	payload := []byte(fmt.Sprintf(`{"step_id":"%s","status":"%s"}`, stepID, status))
	apiURL = apiURL + stepID
	resp, err := http.Post(apiURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 {
		return fmt.Errorf("API responded with status %d", resp.StatusCode)
	}

	fmt.Println("Successfully notified API of step completion.")
	return nil
}

func main() {
	// Define CLI flags
	apiURL := flag.String("api-url", "", "API URL to send status update")
	stepID := flag.String("step-id", "", "Step ID for recovery plan step")
	

	flag.Parse()

	if *apiURL == "" || *stepID == "" {
		fmt.Println("Missing required arguments: --api-url and --step-id are required")
		flag.Usage()
		return
	}

	fmt.Println("OS Update Agent starting...")
	fmt.Printf("StepID: %s\n", *stepID)

	if err := updateOS(); err != nil {
		fmt.Printf("OS update failed: %v\n", err)
		err = notifyStatusAPI(*apiURL, *stepID, "failed")
		if err != nil {
			fmt.Printf("Failed to notify API: %v\n", err)
		}
		return	
	}

	if err := notifyStatusAPI(*apiURL, *stepID, "completed"); err != nil {
		fmt.Printf("Failed to notify API: %v\n", err)
		return
	}

	fmt.Println("OS Update Agent completed.")
}