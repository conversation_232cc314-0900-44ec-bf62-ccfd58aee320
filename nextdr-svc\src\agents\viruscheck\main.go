package main

import (
	"bytes"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/exec"
)

func runVirusScan() (string, error) {
	var out bytes.Buffer
	cmd := exec.Command("clamscan", "-r", "/home/<USER>")
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	return out.String(), err
}

func reportStatus(apiURL, stepID,status, result string) error {
	payload := fmt.Sprintf(`{
		"step_id": "%s",
		"status": "%s",
		"details": %q
	}`, stepID, status, result)

	resp, err := http.Post(apiURL, "application/json", bytes.NewBuffer([]byte(payload)))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 {
		return fmt.Errorf("API responded with status code %d", resp.StatusCode)
	}
	return nil
}

func main() {
	apiURL := flag.String("api-url", "", "API endpoint to report virus scan status")
	stepID := flag.String("step-id", "", "Step ID for the virus scan step")
	flag.Parse()

	fmt.Println("Virus Check Agent starting...")

	result, err := runVirusScan()
	status := "COMPLETED"
	if err != nil {
		fmt.Printf("Virus scan error: %v\n", err)
		status = "FAILED"
	}

	fmt.Println("Virus Scan Output:")
	fmt.Println(result)

	if *apiURL != "" && *stepID != "" {
		err := reportStatus(*apiURL, *stepID, status, result)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to report status: %v\n", err)
		}
	}
}