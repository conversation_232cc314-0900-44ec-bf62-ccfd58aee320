import React, { useState, useEffect } from "react";
import { User, RefreshCw, Shield, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { getIAMAccountsRoles } from "@/lib/api/api-client";
import { IAMRole } from "@/lib/types";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface GoogleCloudIAMDiscoveryProps {
	projectId?: string;
	datacenterId: string;
}

export default function GoogleCloudIAMDiscovery({
	projectId,
	datacenterId,
}: GoogleCloudIAMDiscoveryProps) {
	const [iamAccounts, setIAMAccounts] = useState<IAMRole[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [expandedRoles, setExpandedRoles] = useState<Record<string, boolean>>({});
	const { toast } = useToast();

	// Toggle role expansion
	const toggleRoleExpansion = (roleId: string) => {
		setExpandedRoles((prev) => ({
			...prev,
			[roleId]: !prev[roleId],
		}));
	};

	const fetchIAMAccounts = async () => {
		if (!projectId) return;

		setLoading(true);
		setError(null);
		try {
			const data: IAMRole[] = await getIAMAccountsRoles(projectId, datacenterId);
			setIAMAccounts(data);
		} catch (err: any) {
			setError(err.message || "Failed to fetch IAM accounts");
			toast({
				title: "Error",
				description: "Failed to fetch IAM accounts",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (projectId) {
			fetchIAMAccounts();
		}
	}, [projectId, datacenterId]);

	// No need for grouping roles anymore

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div className="flex items-center gap-3">
					<User className="h-6 w-6 text-blue-500" />
					<h2 className="text-xl font-semibold">Accounts and Roles</h2>
					{iamAccounts.length > 0 && (
						<span className="px-2 py-1 bg-secondary rounded-full text-xs text-muted-foreground">
							{iamAccounts.length} {iamAccounts.length === 1 ? "account" : "accounts"}
						</span>
					)}
				</div>

				<Button onClick={() => fetchIAMAccounts()} variant="outline" size="sm" disabled={loading}>
					{loading ? (
						<div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					) : (
						<RefreshCw className="h-4 w-4 mr-2" />
					)}
					Refresh
				</Button>
			</div>

			{error && (
				<div className="bg-red-900/20 border border-red-800/30 text-red-400 p-4 rounded-md">
					{error}
				</div>
			)}

			{loading ? (
				<div className="flex justify-center items-center py-8">
					<div className="animate-spin h-6 w-6 border-t-2 border-b-2 border-blue-500 rounded-full mr-2"></div>
					<span>Loading IAM accounts...</span>
				</div>
			) : iamAccounts.length > 0 ? (
				<div className="flex flex-wrap gap-3">
					{iamAccounts.map((account) => (
						<Collapsible
							key={account.role}
							open={expandedRoles[account.role]}
							onOpenChange={() => toggleRoleExpansion(account.role)}
							className="flex-grow basis-[200px] max-w-none"
						>
							<Card className="overflow-hidden border-border hover:border-blue-500/30 transition-colors h-full">
								<CardContent className="p-3">
									<CollapsibleTrigger className="w-full">
										<div className="flex items-center justify-between w-full">
											<div className="flex items-center gap-2">
												<Shield className="h-4 w-4 text-blue-500" />
												<h3 className="font-medium text-sm truncate max-w-[180px]">
													{account.role.split("/").pop()}
												</h3>
											</div>
											{expandedRoles[account.role] ? (
												<ChevronDown className="h-4 w-4 ml-1 flex-shrink-0 text-muted-foreground" />
											) : (
												<ChevronRight className="h-4 w-4 ml-1 flex-shrink-0 text-muted-foreground" />
											)}
										</div>
									</CollapsibleTrigger>

									<CollapsibleContent className="mt-3 space-y-2 pt-2 border-t border-border">
										<div className="flex flex-col gap-1">
											<span className="text-xs text-muted-foreground">Members:</span>
											<span className="text-xs font-mono break-all">
												{account.members.join(", ")}
											</span>
										</div>

										<div className="flex flex-col gap-1">
											<span className="text-xs text-muted-foreground">Full Role:</span>
											<span className="text-xs break-all">
												<span className="px-2 py-0.5 bg-blue-900/20 text-blue-400 rounded-full text-xs">
													{account.role}
												</span>
											</span>
										</div>
									</CollapsibleContent>
								</CardContent>
							</Card>
						</Collapsible>
					))}
				</div>
			) : (
				<div className="text-center p-8 bg-secondary rounded-lg">
					<User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<h4 className="text-lg font-medium mb-2">No accounts or roles found</h4>
					<p className="text-muted-foreground">
						This project has no IAM accounts or roles configured
					</p>
				</div>
			)}
		</div>
	);
}
