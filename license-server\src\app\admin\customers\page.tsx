"use client";

import { useEffect, useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import { supabase } from "@/lib/supabase/client";

// Temporary types until we have proper type definitions
interface CustomerProfile {
	id: string;
	company_name: string;
	contact_number: string;
	address: string;
	created_at: string;
}

export default function CustomersPage() {
	const [customers, setCustomers] = useState<CustomerProfile[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState("");
	const [showAddModal, setShowAddModal] = useState(false);
	const [newCustomer, setNewCustomer] = useState({
		email: "",
		password: "",
		company_name: "",
		contact_number: "",
		address: "",
	});

	useEffect(() => {
		// Remove auth check since AdminLayout handles it
		fetchCustomers();
	}, []);

	const fetchCustomers = async () => {
		try {
			// Get the current session token
			const {
				data: { session },
			} = await supabase.auth.getSession();

			const response = await fetch("/api/admin/customers", {
				headers: {
					Authorization: `Bearer ${session?.access_token}`,
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to fetch customers");
			}
			const data = await response.json();
			setCustomers(data);
		} catch (error: any) {
			setError(error.message);
		} finally {
			setLoading(false);
		}
	};

	const handleAddCustomer = async (e: React.FormEvent) => {
		e.preventDefault();
		try {
			// Get the current session token
			const {
				data: { session },
			} = await supabase.auth.getSession();

			const response = await fetch("/api/admin/customers", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session?.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify(newCustomer),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to add customer");
			}

			setShowAddModal(false);
			setNewCustomer({
				email: "",
				password: "",
				company_name: "",
				contact_number: "",
				address: "",
			});
			fetchCustomers();
		} catch (error: any) {
			setError(error.message);
		}
	};

	return (
		<AdminLayout title="Customer Management">
			<div className="space-y-6">
				{/* Header with Add Customer button */}
				<div className="flex justify-between items-center">
					<div>
						<p className="text-sm text-gray-500">Manage customer accounts and information</p>
					</div>
					<button
						onClick={() => setShowAddModal(true)}
						className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
					>
						Add Customer
					</button>
				</div>

				{loading ? (
					<div className="text-center py-12">
						<div className="text-xl">Loading customers...</div>
					</div>
				) : (
					<div>
						{error && (
							<div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
								{error}
							</div>
						)}
						<div className="mt-8 flex flex-col">
							<div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
								<div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
									<div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
										<table className="min-w-full divide-y divide-gray-300">
											<thead className="bg-gray-50">
												<tr>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Company Name
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Contact Number
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Address
													</th>
													<th className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
														Created At
													</th>
												</tr>
											</thead>
											<tbody className="divide-y divide-gray-200 bg-white">
												{customers.map((customer) => (
													<tr key={customer.id}>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{customer.company_name}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{customer.contact_number}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{customer.address}
														</td>
														<td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
															{new Date(customer.created_at).toLocaleDateString()}
														</td>
													</tr>
												))}
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Add Customer Modal */}
			{showAddModal && (
				<div className="fixed z-10 inset-0 overflow-y-auto">
					<div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
						<div className="fixed inset-0 transition-opacity" aria-hidden="true">
							<div className="absolute inset-0 bg-gray-500 opacity-75"></div>
						</div>

						<div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
							<form onSubmit={handleAddCustomer}>
								<div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
									<div className="space-y-4">
										<div>
											<label htmlFor="email" className="block text-sm font-medium text-gray-700">
												Email
											</label>
											<input
												type="email"
												name="email"
												id="email"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newCustomer.email}
												onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
											/>
										</div>
										<div>
											<label htmlFor="password" className="block text-sm font-medium text-gray-700">
												Password
											</label>
											<input
												type="password"
												name="password"
												id="password"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newCustomer.password}
												onChange={(e) =>
													setNewCustomer({
														...newCustomer,
														password: e.target.value,
													})
												}
											/>
										</div>
										<div>
											<label
												htmlFor="company_name"
												className="block text-sm font-medium text-gray-700"
											>
												Company Name
											</label>
											<input
												type="text"
												name="company_name"
												id="company_name"
												required
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newCustomer.company_name}
												onChange={(e) =>
													setNewCustomer({
														...newCustomer,
														company_name: e.target.value,
													})
												}
											/>
										</div>
										<div>
											<label
												htmlFor="contact_number"
												className="block text-sm font-medium text-gray-700"
											>
												Contact Number
											</label>
											<input
												type="text"
												name="contact_number"
												id="contact_number"
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newCustomer.contact_number}
												onChange={(e) =>
													setNewCustomer({
														...newCustomer,
														contact_number: e.target.value,
													})
												}
											/>
										</div>
										<div>
											<label htmlFor="address" className="block text-sm font-medium text-gray-700">
												Address
											</label>
											<textarea
												name="address"
												id="address"
												rows={3}
												className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
												value={newCustomer.address}
												onChange={(e) =>
													setNewCustomer({
														...newCustomer,
														address: e.target.value,
													})
												}
											/>
										</div>
									</div>
								</div>
								<div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
									<button
										type="submit"
										className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
									>
										Add
									</button>
									<button
										type="button"
										onClick={() => setShowAddModal(false)}
										className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
									>
										Cancel
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			)}
		</AdminLayout>
	);
}
