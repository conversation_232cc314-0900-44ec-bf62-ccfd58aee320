/**
 * <PERSON><PERSON><PERSON> to generate a license token
 * 
 * This script generates a license token for a customer.
 * It uses the private key to sign a JWT token with the provided payload.
 * 
 * Usage:
 * node scripts/generate-license.js <customer-id> [expiration-days]
 * 
 * Examples:
 * node scripts/generate-license.js acme-corp
 * node scripts/generate-license.js acme-corp 365
 */

const fs = require('fs');
const path = require('path');
const jwt = require('jsonwebtoken');

// Get command line arguments
const customerId = process.argv[2];
const expirationDays = parseInt(process.argv[3] || '365', 10);

if (!customerId) {
  console.error('Error: Customer ID is required');
  console.error('Usage: node scripts/generate-license.js <customer-id> [expiration-days]');
  process.exit(1);
}

// Check if private key exists
const privateKeyPath = path.join(__dirname, '../keys/private.key');
if (!fs.existsSync(privateKeyPath)) {
  console.error('Private key not found. Please run scripts/generate-keys.js first.');
  process.exit(1);
}

// Read private key
const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

if (!privateKey || privateKey.trim() === '') {
  console.error('Private key is empty or invalid');
  process.exit(1);
}

console.log('Private key loaded successfully');

// Create JWT payload
const expirationDate = new Date();
expirationDate.setDate(expirationDate.getDate() + expirationDays);
const exp = Math.floor(expirationDate.getTime() / 1000);

const payload = {
  sub: customerId,
  exp
};

console.log(`Creating license for customer: ${customerId}`);
console.log(`License will expire on: ${expirationDate.toISOString()}`);

try {
  // Sign the JWT with RS256 algorithm
  const token = jwt.sign(payload, privateKey, { algorithm: 'RS256' });

  console.log('\nLicense token generated:');
  console.log(token);

  // Verify the token format
  const parts = token.split('.');
  if (parts.length !== 3) {
    console.error('WARNING: Generated token does not have the expected JWT format (3 parts)');
  }

  // Save to a file for easy copying
  const outputPath = path.join(__dirname, `../license-${customerId}.txt`);
  fs.writeFileSync(outputPath, token);
  console.log(`\nToken saved to: ${outputPath}`);
} catch (error) {
  console.error('Error generating license token:', error);
  process.exit(1);
}
