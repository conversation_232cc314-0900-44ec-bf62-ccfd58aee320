import { useAuth } from "@/lib/context/AuthContext";
import { UserRole, RolePermissions, ROLE_PERMISSIONS } from "../types/rbac";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";

export const useRBAC = () => {
	const { user } = useAuth();

	const { data: userProfile } = useQuery({
		queryKey: ["userProfile", user?.id],
		queryFn: async () => {
			if (!user?.id) return null;
			const { data, error } = await supabase
				.from("user_profiles")
				.select("role")
				.eq("id", user.id)
				.single();

			if (error) {
				console.error("Error fetching user profile:", error);
				return null;
			}
			return data;
		},
		enabled: !!user?.id,
	});

	const getUserRole = (): UserRole => {
		return (userProfile?.role as UserRole) || "viewer";
	};

	const getUserPermissions = (): RolePermissions => {
		const role = getUserRole();
		return ROLE_PERMISSIONS[role];
	};

	const hasPermission = (permission: keyof RolePermissions): boolean => {
		const permissions = getUserPermissions();
		return permissions[permission];
	};

	const canManageUsers = () => hasPermission("canManageUsers");
	const canManageGroups = () => hasPermission("canManageGroups");
	const canExecuteRecoveryPlans = () => hasPermission("canExecuteRecoveryPlans");
	const canApproveActions = () => hasPermission("canApproveActions");
	const canViewAuditLogs = () => hasPermission("canViewAuditLogs");
	const canExportAuditLogs = () => hasPermission("canExportAuditLogs");

	return {
		getUserRole,
		getUserPermissions,
		hasPermission,
		canManageUsers,
		canManageGroups,
		canExecuteRecoveryPlans,
		canApproveActions,
		canViewAuditLogs,
		canExportAuditLogs,
	};
};
